# Unified Keyphrase Extractor

A comprehensive Python module that integrates multiple keyphrase extraction methods to provide the most relevant and important keyphrases from scientific articles.

## 🎯 **Features**

### **Integrated Extractors**
- ✅ **YAKE** - Unsupervised statistical extraction
- ✅ **KeyBERT** - BERT-based semantic extraction  
- ✅ **Rakun** - Graph-based extraction
- ✅ **LLaMA 3.2** - Large language model extraction
- ✅ **Keyphrase Model** - HuggingFace transformer-based
- ✅ **Vocabulary Matcher** - Curated vocabulary matching

### **Advanced Features**
- 🎯 **Relevance Filtering** - Semantic similarity to title+abstract
- 📊 **Multi-method Ensemble** - Combines results from all extractors
- 🔧 **Configurable Parameters** - Customizable for different domains
- 📈 **Quality Metrics** - Evaluation against manual annotations
- 🚀 **Batch Processing** - Efficient processing of multiple documents

## 🚀 **Quick Start**

### **Basic Usage**
```python
from unified_keyphrase_extractor import extract_keywords_with_sources_unified

# Your text and sections
text = "Sarcopenia is a syndrome characterized by progressive loss of muscle mass..."
sections = {
    "title": "Sarcopenia in chronic kidney disease",
    "abstract": text,
    "methods": "Cross-sectional study...",
    "results": "Prevalence varied from 15% to 28%..."
}

# Extract keyphrases using all available extractors
df = extract_keywords_with_sources_unified(
    raw_text=text,
    section_texts=sections,
    filter_results=True
)

print(f"Extracted {len(df)} keyword-sentence pairs")
print(f"Unique keywords: {df['keyword'].nunique()}")
print(f"Extractors used: {', '.join(df['extractor'].unique())}")
```

### **Advanced Usage with Custom Configuration**
```python
from unified_keyphrase_extractor import UnifiedKeyphraseExtractor

# Load custom configuration
extractor = UnifiedKeyphraseExtractor("config_unified.yaml")

# Extract with detailed control
df = extractor.extract_from_text(text, sections)

# Get summary statistics
stats = extractor.get_summary_stats(df)
print(f"Extraction statistics: {stats}")

# Get top keywords by frequency
top_keywords = extractor.get_top_keywords_by_frequency(df, top_n=15)
print(top_keywords)

# Apply relevance filtering
filtered_df = extractor.filter_by_relevance(df, min_frequency=2, min_length=4)
```

## 📊 **Output Format**

The unified extractor returns a pandas DataFrame with the following columns:

| Column | Description |
|--------|-------------|
| `extractor` | Source extractor (yake, keybert, rakun, llama, etc.) |
| `keyword` | Extracted keyphrase |
| `source_sentence` | Sentence containing the keyword |
| `confidence` | Confidence score (0.0 to 1.0) |

### **Sample Output**
```
   extractor              keyword                    source_sentence  confidence
0       yake            sarcopenia  Sarcopenia is a syndrome...           1.0
1    keybert        muscle mass     ...loss of skeletal muscle mass...    1.0
2      rakun  chronic kidney disease  ...in chronic kidney disease...     1.0
3      llama     body composition   Body composition was assessed...      1.0
```

## ⚙️ **Configuration**

### **Configuration File Structure**
```yaml
extractors:
  yake:
    lan: "en"
    max_keywords: 30
    dedup_threshold: 0.9
    n: 2
  
  keyphrase:
    model_path: "models/keyphrase_model"
    top_n: 30
    score_threshold: 0.5
  
  # ... other extractors
  
relevance:
  threshold: 0.3
  max_keyphrases: 20
  
filtering:
  min_word_length: 3
  stop_words: ["study", "analysis", "method"]
```

### **Key Parameters**
- **`relevance.threshold`**: Minimum semantic similarity to title+abstract (0.0-1.0)
- **`relevance.max_keyphrases`**: Maximum number of keyphrases to return
- **`filtering.min_word_length`**: Minimum keyphrase length
- **`extractors.*.top_n`**: Number of keyphrases per extractor

## 🔧 **Installation & Setup**

### **Prerequisites**
```bash
pip install pandas numpy scikit-learn
pip install yake keybert sentence-transformers
pip install rakun2 transformers torch
```

### **Model Setup**
1. Download required models to `models/` directory:
   - Sentence transformer model
   - Keyphrase extraction model
   
2. Set up LLaMA API endpoint (if using LLaMA extraction)

3. Prepare vocabulary file (Excel format) for vocab matching

### **File Structure**
```
project/
├── unified_keyphrase_extractor.py
├── config_unified.yaml
├── demo_unified_extractor.py
├── src/
│   ├── extractors/
│   │   ├── yake_extractor.py
│   │   ├── keybert_extractor.py
│   │   ├── rakun_extractor.py
│   │   ├── llama_extractor_chunked.py
│   │   └── vocab_matcher.py
│   └── semantic/
├── models/
│   ├── sentence_transformer/
│   └── keyphrase_model/
└── data/
    ├── curated_vocab.xlsx
    └── section_52_dataset_title_abstract_kw.xlsx
```

## 📈 **Performance & Quality**

### **Evaluation Metrics**
- **Precision**: % of extracted keyphrases that are relevant
- **Recall**: % of relevant keyphrases that were extracted  
- **F1-Score**: Harmonic mean of precision and recall
- **Coverage**: Number of different extractor methods contributing

### **Quality Improvements**
1. **Multi-method Ensemble**: Combines strengths of different approaches
2. **Relevance Filtering**: Ensures keyphrases match document topic
3. **Frequency-based Ranking**: Prioritizes keyphrases found by multiple extractors
4. **Manual Validation**: Evaluation against expert-annotated keyphrases

## 🎯 **Use Cases**

### **Scientific Literature Analysis**
- Extract key concepts from research papers
- Identify important methodologies and findings
- Compare keyphrases across different studies

### **Domain-Specific Applications**
- Medical research: diseases, treatments, biomarkers
- Chemistry: compounds, reactions, materials
- Engineering: processes, technologies, materials

### **Content Indexing**
- Automatic tagging of documents
- Search index enhancement
- Content recommendation systems

## 🚀 **Migration from main_source.py**

### **Before (main_source.py)**
```python
from main_source import extract_keywords_with_sources

df = extract_keywords_with_sources(raw_text, config)
```

### **After (Unified Extractor)**
```python
from unified_keyphrase_extractor import extract_keywords_with_sources_unified

df = extract_keywords_with_sources_unified(
    raw_text=raw_text,
    section_texts=section_texts,  # New: section-aware extraction
    config="config_unified.yaml", # Enhanced configuration
    filter_results=True           # New: automatic relevance filtering
)
```

### **Benefits of Migration**
- ✅ **More Extractors**: 6 methods vs 3 in original
- ✅ **Better Quality**: Relevance filtering and ensemble ranking
- ✅ **Section Awareness**: LLaMA extractor uses document structure
- ✅ **Configurable**: Easy parameter tuning for different domains
- ✅ **Evaluation**: Built-in metrics against manual annotations

## 📋 **Next Steps**

1. **Test with Your Data**: Run `demo_unified_extractor.py`
2. **Customize Configuration**: Modify `config_unified.yaml` for your domain
3. **Evaluate Quality**: Compare against your manual keyphrase dataset
4. **Fine-tune Parameters**: Adjust thresholds based on results
5. **Scale Up**: Process your full document collection

## 🤝 **Integration with Existing Workflow**

The unified extractor is designed to be a drop-in replacement for your existing `main_source.py` while providing significantly enhanced functionality. It maintains the same output format while adding new capabilities for better keyphrase quality and relevance.
