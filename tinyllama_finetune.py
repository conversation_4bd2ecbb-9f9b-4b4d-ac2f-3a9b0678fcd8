from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model #  using PEFT

# 1. Prepare Dataset (dummy )
# Replace with your actual dataset loading and formatting
train_dataset = [{"text": "This is a document about machine learning.", "keywords": "machine learning"}, ...]
eval_dataset = [{"text": "Another text for keyword extraction.", "keywords": "keyword extraction"}, ...]

# 2. Load Model and Tokenizer
model_name = "TinyLlama/TinyLlama-1.1B-Chat-v1.0" # Or your chosen TinyLlama variant
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# Apply PEFT (e.g., LoRA)
lora_config = LoraConfig(r=8, lora_alpha=16, lora_dropout=0.05, bias="none", task_type="CAUSAL_LM")
model = get_peft_model(model, lora_config)

# 3. Configure Training Arguments
training_args = TrainingArguments(
    output_dir="./results",
    num_train_epochs=3,
    per_device_train_batch_size=4,
    per_device_eval_batch_size=4,
    logging_dir="./logs",
    logging_steps=100,
    evaluation_strategy="epoch",
    save_strategy="epoch",
    load_best_model_at_end=True,
)

# 4. Create and Train the Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    tokenizer=tokenizer,
)

trainer.train()

# 5. Evaluate and Save ()
results = trainer.evaluate()
print(results)
model.save_pretrained("./fine_tuned_tinyllama_keywords")