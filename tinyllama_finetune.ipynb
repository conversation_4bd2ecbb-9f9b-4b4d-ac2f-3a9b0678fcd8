{"cells": [{"cell_type": "code", "execution_count": null, "id": "1729b208", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ef03195", "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments, Trainer\n", "from peft import LoraConfig, get_peft_model #  using PEFT\n", "\n", "# 1. Prepare Dataset (dummy )\n", "# Replace with your actual dataset loading and formatting\n", "train_dataset = [{\"text\": \"This is a document about machine learning.\", \"keywords\": \"machine learning\"}, ...]\n", "eval_dataset = [{\"text\": \"Another text for keyword extraction.\", \"keywords\": \"keyword extraction\"}, ...]\n", "\n", "# 2. <PERSON><PERSON> and <PERSON><PERSON><PERSON>\n", "model_name = \"TinyLlama/TinyLlama-1.1B-Chat-v1.0\" # Or your chosen TinyLlama variant\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name)\n", "\n", "# Apply PEFT (e.g., LoRA)\n", "lora_config = LoraConfig(r=8, lora_alpha=16, lora_dropout=0.05, bias=\"none\", task_type=\"CAUSAL_LM\")\n", "model = get_peft_model(model, lora_config)\n", "\n", "# 3. Configure Training Arguments\n", "training_args = TrainingArguments(\n", "    output_dir=\"./results\",\n", "    num_train_epochs=3,\n", "    per_device_train_batch_size=4,\n", "    per_device_eval_batch_size=4,\n", "    logging_dir=\"./logs\",\n", "    logging_steps=100,\n", "    evaluation_strategy=\"epoch\",\n", "    save_strategy=\"epoch\",\n", "    load_best_model_at_end=True,\n", ")\n", "\n", "# 4. <PERSON><PERSON> and <PERSON> the Trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    tokenizer=tokenizer,\n", ")\n", "\n", "trainer.train()\n", "\n", "# 5. Evaluate and Save ()\n", "results = trainer.evaluate()\n", "print(results)\n", "model.save_pretrained(\"./fine_tuned_tinyllama_keywords\")"]}, {"cell_type": "code", "execution_count": null, "id": "3af78baf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "09db8cb4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}