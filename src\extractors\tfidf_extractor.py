# # # src/extractors/tfidf_extractor.py
# import spacy
# from sklearn.feature_extraction.text import TfidfVectorizer
# import yaml

# class TFIDFExtractor:
#     def __init__(self, top_n=10, spacy_model="en_core_web_sm"):
#         self.top_n = top_n
#         self.nlp = spacy.load(spacy_model)
#         self.stop_articles = {"a", "an", "the"}

#     def _clean_phrase(self, phrase):
#         # Remove newlines, multiple spaces, lowercase
#         phrase = phrase.replace("\n", " ").strip().lower()

#         # Remove starting/ending articles
#         words = phrase.split()
#         if words and words[0] in self.stop_articles:
#             words = words[1:]
#         if words and words[-1] in self.stop_articles:
#             words = words[:-1]

#         return " ".join(words).strip()

#     def extract(self, text):
#         try:
#             # Extract noun phrases with cleaning
#             doc = self.nlp(text)
#             noun_chunks = list(
#                 set(self._clean_phrase(chunk.text) for chunk in doc.noun_chunks if len(chunk.text.split()) <= 3)
#             )

#             # Remove empty phrases after cleaning
#             noun_chunks = [p for p in noun_chunks if p]

#             if not noun_chunks:
#                 return []

#             # Score noun phrases with TF-IDF
#             vectorizer = TfidfVectorizer(vocabulary=noun_chunks, stop_words="english")
#             X = vectorizer.fit_transform([text])
#             scores = X.toarray()[0]

#             # Sort by score and return top phrases
#             scored_phrases = sorted(
#                 ((phrase, score) for phrase, score in zip(vectorizer.get_feature_names_out(), scores)),
#                 key=lambda x: x[1],
#                 reverse=True
#             )

#             return [phrase for phrase, score in scored_phrases[:self.top_n]]

#         except Exception as e:
#             print(f"TF-IDF error: {e}")
#             return []





# def run_tfidf_extractors(text, top_n=10):
#     """Run all keyword extractors and return a dictionary."""
#     extractors = {
#         # "YAKE": YAKEExtractor(top_n=top_n),
#         # "RAKE": RAKEExtractor(top_n=top_n),
#         # "SummaTextRank": SummaTextRankExtractor(top_n=top_n),
#         "TFIDF": TFIDFExtractor(top_n=top_n),
#         # "MultipartiteRank": MultipartiteRankExtractor(top_n=top_n)
#     }
    
#     results = {}
#     for name, extractor in extractors.items():
#         print(f"Running {name} extractor...")
#         results[name] = extractor.extract(text)
#     return results

# if __name__ == "__main__":


#     # Run all extractors
#     results = run_tfidf_extractors(sample_text, top_n=10)
#     print(results)


# src/extractors/tfidf_extractor.py
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
import re
import yaml

class TFIDFExtractor:
    def __init__(self, top_n=10, spacy_model="en_core_web_sm"):
        self.top_n = top_n
        self.nlp = spacy.load(spacy_model, disable=["ner"])
        self.stop_articles = {"a", "an", "the"}

    def _clean_phrase(self, phrase):
        # Remove newlines, multiple spaces, lowercase
        phrase = phrase.replace("\n", " ").strip().lower()

        # Remove starting/ending articles
        words = phrase.split()
        if words and words[0] in self.stop_articles:
            words = words[1:]
        if words and words[-1] in self.stop_articles:
            words = words[:-1]

        return " ".join(words).strip()

    def _is_valid_phrase(self, phrase):
        """Reject phrases that are too short, mostly numbers/symbols, or junky."""
        if not phrase or len(phrase) < 3:
            return False

        # Reject if contains digits + letters mixed (e.g., mg12b80, 31g)
        if re.search(r"[0-9].*[a-zA-Z]|[a-zA-Z].*[0-9]", phrase):
            return False

        # Reject if mostly non-letters
        if not re.search(r"[a-zA-Z]", phrase):
            return False

        # Reject if it's a single character or common junk
        if phrase in {"table", "figure", "et", "al"}:
            return False

        return True

    def extract(self, text):
        try:
            # Extract noun phrases with cleaning
            doc = self.nlp(text)
            noun_chunks = list(
                set(self._clean_phrase(chunk.text) for chunk in doc.noun_chunks if len(chunk.text.split()) <= 3)
            )

            # Remove empty and invalid phrases
            noun_chunks = [p for p in noun_chunks if p and self._is_valid_phrase(p)]

            if not noun_chunks:
                return []

            # Score noun phrases with TF-IDF
            vectorizer = TfidfVectorizer(vocabulary=noun_chunks, stop_words="english")
            X = vectorizer.fit_transform([text])
            scores = X.toarray()[0]

            # Sort by score and return top phrases
            scored_phrases = sorted(
                ((phrase, score) for phrase, score in zip(vectorizer.get_feature_names_out(), scores)),
                key=lambda x: x[1],
                reverse=True
            )

            return [phrase for phrase, score in scored_phrases[:self.top_n]]

        except Exception as e:
            print(f"TF-IDF error: {e}")
            return []





if __name__ == "__main__":
    def load_config(config_path):
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
        
    raw_text = """
    Solar energy storage and conversion is a growing field with applications in renewable energy systems. 
    Advances in hydrogen fuel storage, thermoelectric devices, and biomass gasification have increased efficiency. 
    Waste heat recovery systems and geothermal energy harvesting are critical for sustainable power generation.
    """
    config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")


    tfidf_extractor = TFIDFExtractor(**config["extractors"]["tfidf"])
    keywords = tfidf_extractor.extract(raw_text)
    print(keywords)