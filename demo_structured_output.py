#!/usr/bin/env python3
"""
Demo script for the Structured Output Keyphrase Extractor
Shows how to use the merge_and_deduplicate_keywords function and get structured output.
"""

import pandas as pd
import time
from unified_keyphrase_extractor import (
    extract_keywords_with_sources_unified,
    extract_keyphrases_structured_output,
    merge_and_deduplicate_keywords
)

def demo_deduplication_and_noise_removal():
    """Demonstrate the merge_and_deduplicate_keywords function."""
    print("🔬 DEDUPLICATION AND NOISE REMOVAL DEMO")
    print("=" * 60)
    
    # Sample scientific text with potential duplicates and noise
    sample_text = """
    Sarcopenia is a syndrome characterized by progressive and generalized loss of skeletal muscle mass and strength. 
    The prevalence of sarcopenia in chronic kidney disease (CKD) patients varies widely depending on the definition used. 
    Muscle mass assessment is crucial for sarcopenia diagnosis. Body composition analysis using dual-energy X-ray 
    absorptiometry provides accurate muscle mass measurements. The relationship between muscle strength and muscle mass 
    is important in sarcopenia research. CKD patients often experience muscle wasting and reduced muscle function.
    """
    
    sample_sections = {
        "title": "Sarcopenia and muscle mass in chronic kidney disease patients",
        "abstract": sample_text,
        "methods": "Cross-sectional study measuring muscle mass and strength in CKD patients",
        "results": "Significant correlation between muscle mass and kidney function was observed",
        "conclusion": "Muscle mass assessment is essential for sarcopenia diagnosis in CKD"
    }
    
    print(f"📄 Sample Text: {sample_text[:100]}...")
    
    # Extract without deduplication first
    print("\n🚀 Extracting WITHOUT deduplication...")
    df_raw = extract_keywords_with_sources_unified(
        raw_text=sample_text,
        section_texts=sample_sections,
        filter_results=True,
        use_deduplication=False
    )
    
    print(f"📊 RAW EXTRACTION RESULTS:")
    print(f"Total keyword-sentence pairs: {len(df_raw)}")
    print(f"Unique keywords: {df_raw['keyword'].nunique()}")
    
    if not df_raw.empty:
        print(f"\n📈 TOP 15 RAW KEYWORDS:")
        top_raw = df_raw['keyword'].value_counts().head(15)
        for i, (keyword, count) in enumerate(top_raw.items(), 1):
            extractors = df_raw[df_raw['keyword'] == keyword]['extractor'].unique()
            print(f"  {i:2d}. {keyword} (freq: {count}, extractors: {', '.join(extractors)})")
    
    # Extract with deduplication
    print("\n🚀 Extracting WITH deduplication...")
    df_deduplicated = extract_keywords_with_sources_unified(
        raw_text=sample_text,
        section_texts=sample_sections,
        filter_results=True,
        use_deduplication=True
    )
    
    print(f"\n📊 DEDUPLICATED EXTRACTION RESULTS:")
    print(f"Total keyword-sentence pairs: {len(df_deduplicated)}")
    print(f"Unique keywords: {df_deduplicated['keyword'].nunique()}")
    
    if not df_deduplicated.empty:
        print(f"\n🎯 TOP DEDUPLICATED KEYWORDS (prioritizing multi-extractor keywords):")
        for i, (_, row) in enumerate(df_deduplicated.head(15).iterrows(), 1):
            extractor_count = len(row['extractor'].split('|'))
            print(f"  {i:2d}. {row['keyword']} (extractors: {row['extractor']}, count: {extractor_count})")
    
    # Show the difference
    print(f"\n📉 NOISE REDUCTION:")
    print(f"Keywords before deduplication: {len(df_raw)}")
    print(f"Keywords after deduplication: {len(df_deduplicated)}")
    print(f"Noise removed: {len(df_raw) - len(df_deduplicated)} ({((len(df_raw) - len(df_deduplicated)) / len(df_raw) * 100):.1f}%)")

def demo_structured_output():
    """Demonstrate the structured output format."""
    print("\n" + "=" * 60)
    print("🔬 STRUCTURED OUTPUT FORMAT DEMO")
    print("=" * 60)
    
    # Sample medical research text
    medical_text = """
    Chronic kidney disease affects muscle metabolism and leads to sarcopenia. 
    Sarcopenia diagnosis requires assessment of muscle mass, muscle strength, and physical performance. 
    Body composition analysis using DXA provides accurate muscle mass measurements. 
    Bioelectrical impedance analysis offers an alternative method for body composition assessment. 
    The prevalence of sarcopenia varies depending on diagnostic criteria used.
    """
    
    medical_sections = {
        "title": "Sarcopenia diagnosis in chronic kidney disease",
        "abstract": "Study of sarcopenia prevalence and diagnostic methods in CKD patients",
        "methods": medical_text,
        "results": "Sarcopenia prevalence ranged from 15% to 30% depending on criteria. DXA showed high accuracy.",
        "discussion": "Standardized diagnostic criteria are needed for sarcopenia in CKD populations"
    }
    
    print("🚀 Extracting with structured output format...")
    structured_df = extract_keyphrases_structured_output(
        raw_text=medical_text,
        section_texts=medical_sections,
        filter_results=True,
        use_deduplication=True
    )
    
    if not structured_df.empty:
        print(f"\n📊 STRUCTURED OUTPUT RESULTS:")
        print(f"Total sentences with keyphrases: {len(structured_df)}")
        print(f"Total unique keyphrases: {sum(len(kp_list) for kp_list in structured_df['keyphrase_list'])}")
        
        print(f"\n📋 STRUCTURED OUTPUT FORMAT:")
        print("Columns:", list(structured_df.columns))
        
        print(f"\n🎯 TOP SENTENCES BY KEYPHRASE COUNT:")
        for i, (_, row) in enumerate(structured_df.head(10).iterrows(), 1):
            print(f"\n{i:2d}. SENTENCE: {row['sentence'][:80]}...")
            print(f"    KEYPHRASES: {row['keyphrase_list']}")
            print(f"    EXTRACTORS: {row['extractor_list']}")
            print(f"    SEGMENT: {row['input_segment']}")
            print(f"    COUNT: {row['keyphrase_count']}")
        
        print(f"\n📈 SEGMENT DISTRIBUTION:")
        segment_counts = structured_df['input_segment'].value_counts()
        for segment, count in segment_counts.items():
            total_keyphrases = structured_df[structured_df['input_segment'] == segment]['keyphrase_count'].sum()
            print(f"  {segment}: {count} sentences, {total_keyphrases} total keyphrases")
        
        print(f"\n🏆 MOST PRODUCTIVE EXTRACTORS:")
        all_extractors = []
        for extractor_list in structured_df['extractor_list']:
            all_extractors.extend(extractor_list.split('|'))
        
        from collections import Counter
        extractor_counts = Counter(all_extractors)
        for extractor, count in extractor_counts.most_common():
            print(f"  {extractor}: {count} contributions")
    
    else:
        print("❌ No structured output generated")

def demo_comparison_formats():
    """Compare different output formats."""
    print("\n" + "=" * 60)
    print("🔬 OUTPUT FORMAT COMPARISON")
    print("=" * 60)
    
    sample_text = "Sarcopenia affects muscle mass and strength in chronic kidney disease patients."
    sample_sections = {
        "title": "Sarcopenia in CKD",
        "abstract": sample_text
    }
    
    # Format 1: Original format (like main_source.py)
    print("📊 FORMAT 1: Original (main_source.py style)")
    df_original = extract_keywords_with_sources_unified(
        sample_text, sample_sections, use_deduplication=True
    )
    print(f"Columns: {list(df_original.columns)}")
    if not df_original.empty:
        print("Sample rows:")
        print(df_original.head(3).to_string(index=False))
    
    # Format 2: Structured output
    print(f"\n📊 FORMAT 2: Structured (sentence-grouped)")
    df_structured = extract_keyphrases_structured_output(
        sample_text, sample_sections, use_deduplication=True
    )
    print(f"Columns: {list(df_structured.columns)}")
    if not df_structured.empty:
        print("Sample rows:")
        for _, row in df_structured.head(2).iterrows():
            print(f"Sentence: {row['sentence'][:50]}...")
            print(f"Keyphrases: {row['keyphrase_list']}")
            print(f"Extractors: {row['extractor_list']}")
            print(f"Segment: {row['input_segment']}")
            print(f"Count: {row['keyphrase_count']}")
            print("-" * 40)
    
    print(f"\n✅ BENEFITS OF STRUCTURED FORMAT:")
    print("  • Groups keyphrases by sentence for better context")
    print("  • Shows which extractors contributed to each sentence")
    print("  • Identifies source segment for each sentence")
    print("  • Prioritizes sentences with multiple keyphrases")
    print("  • Reduces redundancy through deduplication")

if __name__ == "__main__":
    try:
        # Run all demos
        demo_deduplication_and_noise_removal()
        demo_structured_output()
        demo_comparison_formats()
        
        print("\n" + "=" * 60)
        print("🎉 STRUCTURED OUTPUT DEMO COMPLETED!")
        print("=" * 60)
        print("""
        📋 KEY FEATURES DEMONSTRATED:
        
        1. 🧹 NOISE REMOVAL & DEDUPLICATION:
           - merge_and_deduplicate_keywords() removes duplicates and noise
           - Prioritizes keywords found by multiple extractors
           - Handles singular/plural variations and partial matches
        
        2. 📊 STRUCTURED OUTPUT FORMAT:
           - Groups keyphrases by sentence for better context
           - Shows extractor contributions per sentence
           - Identifies source document segment
           - Ranks by keyphrase density
        
        3. 🎯 QUALITY IMPROVEMENTS:
           - Multi-extractor consensus increases confidence
           - Sentence-level grouping preserves context
           - Segment awareness enables section-specific analysis
           - Configurable filtering and deduplication
        
        📈 USAGE RECOMMENDATIONS:
        - Use extract_keyphrases_structured_output() for analysis and visualization
        - Use extract_keywords_with_sources_unified() for compatibility with existing code
        - Enable use_deduplication=True for higher quality results
        - Customize config for domain-specific requirements
        """)
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("This might be due to missing dependencies or model files.")
        import traceback
        traceback.print_exc()
