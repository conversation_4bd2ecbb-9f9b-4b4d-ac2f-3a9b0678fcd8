import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer, util
import spacy
from itertools import chain
import re
from transformers import AutoTokenizer, AutoModel


# Load spaCy English model for lemmatization and POS tagging
nlp = spacy.load("en_core_web_sm")

def normalize_phrases(phrases):
    """
    Normalize phrases by lemmatizing for grouping, 
    but always return the original-case version of the chosen phrase.
    If both noun and non-noun variants of the same lemma exist, keep only the noun (singular).
    If only one POS type exists, keep it as-is.
    """
    lemma_groups = {}
    phrases = filter_similar_strings(phrases)
    for phrase in phrases:

        doc = nlp(phrase)
        lemma_tokens = [token.lemma_.lower() for token in doc]
        lemma_phrase = " ".join(lemma_tokens)

        # Collect original phrase and POS info (preserve case of original phrase)
        if lemma_phrase not in lemma_groups:
            lemma_groups[lemma_phrase] = []
        lemma_groups[lemma_phrase].append((phrase, [token.pos_ for token in doc]))

    normalized = []
    for lemma_phrase, variants in lemma_groups.items():
        # Check if any NOUN form exists
        noun_variants = [p for p, pos_list in variants if "NOUN" in pos_list]

        if noun_variants:
            # Prefer shortest noun variant, but keep its original case
            chosen = min(noun_variants, key=len)
        else:
            # If no noun variant, keep the shortest overall, preserving case
            chosen = min([p for p, _ in variants], key=len)

        normalized.append(chosen)  # ✅ keep original case

    return list(dict.fromkeys(normalized))  # preserves order & uniqueness


def filter_hierarchical(phrases):
    """
    Remove less informative or redundant phrases when more specific or
    base forms exist.
    Rules:
    - Remove plural if singular with modifier exists (e.g., 'current densities' → drop if 'high current density' exists).
    - Remove adjectives if noun form exists in the cluster (e.g., 'cathodic' → drop if 'cathodes' exists).
    - Remove generic shorter phrases if longer, more informative ones exist (subsumption).
    Always keep at least one phrase.
    """
    if not phrases:
        return phrases

    doc_map = {p: nlp(p) for p in phrases}
    phrases = sorted(set(phrases), key=len, reverse=True)  # longer first
    filtered = []

    for phrase in phrases:
        doc = doc_map[phrase]

        # Rule 1: Skip plural if singular form + modifier exists
        if any(
            other != phrase
            and any(t.pos_ == "NOUN" and t.lemma_ in [tok.lemma_ for tok in doc] for t in doc_map[other])
            and len(other.split()) > len(phrase.split())
            for other in phrases
        ):
            continue

        # Rule 2: Skip adjective form if noun exists in same cluster
        if any(
            other != phrase
            and any(t.pos_ == "NOUN" for t in doc_map[other])
            and any(tok.pos_ == "ADJ" and tok.lemma_ in [t.lemma_ for t in doc_map[other]] for tok in doc)
            for other in phrases
        ):
            continue

        # Rule 3: Generic containment filtering (shorter contained in longer)
        if any(
            other != phrase and phrase in other
            for other in phrases
        ):
            continue

        filtered.append(phrase)

    # ✅ safeguard: if filtering removes everything, keep the longest phrase
    if not filtered:
        filtered = [max(phrases, key=len)]

    return filtered


def filter_similar_strings(string_list):
    """
    Filters a list of strings to remove shorter, similar strings.

    Similarity is determined by comparing strings after removing all non-alphanumeric
    characters and converting them to lowercase. When two or more strings are
    found to be similar, only the longest one (by original character count) is kept.
    The original case of the kept string is preserved.

    Args:
        string_list: A list of strings to be filtered.

    Returns:
        A new list containing the filtered strings.
    """
    if not string_list:
        return []

    # This dictionary will store the results.
    # Key: a "normalized" version of the string (lowercase, alphanumeric only).
    # Value: the longest original string found so far for that key.
    longest_strings = {}

    for current_string in string_list:
        # Normalize the string for comparison purposes:
        # 1. Remove any character that is not a letter or a number.
        # 2. Convert the result to lowercase to make the comparison case-insensitive.
        # Example: 'MOF chemistry' -> 'mofchemistry'
        normalized_key = re.sub(r'[^a-zA-Z0-9]', '', current_string).lower()

        # Check if we've already seen a similar string.
        if normalized_key in longest_strings:
            # If we have, get the one we already stored.
            existing_string = longest_strings[normalized_key]

            # Compare the length of the current string with the stored one.
            # If the current one is longer, it replaces the one in our dictionary.
            if len(current_string) > len(existing_string):
                longest_strings[normalized_key] = current_string
        else:
            # If this is the first time we've seen this normalized key,
            # add the original string to our dictionary.
            longest_strings[normalized_key] = current_string

    # The final result is the list of values from our dictionary.
    return list(longest_strings.values())



def deduplicate_and_reduce_phrases(phrases, threshold=0.85, top_n=3, model=None):
    """
    Removes semantically overlapping phrases and selects top_n representative ones
    based on embedding centrality (contextual importance).
    Always returns a Python list (never numpy arrays).
    """
    if phrases is None or (isinstance(phrases, float) and pd.isna(phrases)):
        return []

    # Ensure list format
    if isinstance(phrases, str):
        try:
            phrases = eval(phrases) if phrases.startswith("[") else [phrases]
        except Exception:
            phrases = [phrases]

    if not isinstance(phrases, list) or len(phrases) == 0:
        return []

    # Step 0: Morphological normalization (lemma + POS preference)
    phrases = normalize_phrases(phrases)

    # Step 1: Remove hierarchical/subsumed phrases
    phrases = filter_hierarchical(phrases)

    if len(phrases) == 1:
        return list(phrases)  # ensure list

    # Encode phrases
    embeddings = model.encode(phrases, convert_to_numpy=True, normalize_embeddings=True)

    # Step 2: Deduplication by cosine similarity
    kept = []
    used = set()
    for i, phrase in enumerate(phrases):
        if i in used:
            continue

        kept.append((phrase, embeddings[i]))
        sims = util.cos_sim(embeddings[i], embeddings)[0].cpu().numpy()  # force numpy
        for j, score in enumerate(sims):
            if j != i and score > threshold:
                used.add(j)

    if not kept:
        return list(phrases[:top_n])

    phrases, emb_list = zip(*kept)
    emb_array = np.vstack(emb_list)

    # Step 3: Centrality ranking
    sim_matrix = cosine_similarity(emb_array)
    centrality_scores = sim_matrix.mean(axis=1)

    ranked_idx = np.argsort(-centrality_scores)  # descending
    ranked_phrases = [phrases[i] for i in ranked_idx]

    # Step 4: Keep top N (always as list)
    return list(ranked_phrases[:min(top_n, len(ranked_phrases))])

#  general/common terms list
GENERAL_TERMS = {"engineering", "cycles", "integration", "system", "data", "analysis", 'thereinto', "theoretical", "ivity"}

def filter_general_terms_globally(series_of_lists):
    """
    Removes general terms on a global scale across all lists in a pandas Series.

    It identifies phrases that are substrings of other phrases anywhere in the
    Series and removes the shorter, more general ones.

    Args:
        series_of_lists: A pandas Series where each element is a list of strings.

    Returns:
        A new pandas Series with the globally general terms removed from each list.
    """
    # Step 1: Create a single, flat list of all unique phrases from all lists
    all_phrases = list(set(chain.from_iterable(series_of_lists)))
    
    # Step 3: Filter each list in the original series, keeping only the globally specific phrases
    return series_of_lists.apply(
        lambda current_list: [phrase for phrase in current_list if phrase in all_phrases]
    )



def remove_general_terms(phrases_list, general_terms=GENERAL_TERMS):
    """
    Remove general/common terms from the list of phrases.
    Keeps all other phrases as they are.
    """
    if not isinstance(phrases_list, list):
        return phrases_list
    return [phrase for phrase in phrases_list if phrase.lower() not in general_terms]

def apply_remove_general_terms(df, phrases_col="unique_cluster_phrases"):
    """
    Apply remove_general_terms on a given dataframe column.
    Keeps all columns and adds a cleaned column.
    """
    df = df.copy()
    df[phrases_col] = df[phrases_col].apply(remove_general_terms)
    return df

def split_large_clusters(df, cluster_col="cluster_phrases", len_col="len_cluster", max_items=8):
    """
    Splits rows in df[cluster_col] into multiple rows if the list length exceeds max_items.
    Updates df[len_col] accordingly.
    """
    new_rows = []
    for _, row in df.iterrows():
        cluster_items = row[cluster_col]

        # ensure list type
        if isinstance(cluster_items, str):
            try:
                cluster_items = eval(cluster_items)
            except Exception:
                cluster_items = [cluster_items]
        elif isinstance(cluster_items, (np.ndarray, pd.Series)):
            cluster_items = cluster_items.tolist()
        elif not isinstance(cluster_items, list):
            cluster_items = [cluster_items]

        if len(cluster_items) > max_items:
            chunks = [cluster_items[i:i+max_items] for i in range(0, len(cluster_items), max_items)]
            for chunk in chunks:
                new_row = row.copy()
                new_row[cluster_col] = list(chunk)  # force list
                new_row[len_col] = len(chunk)
                new_rows.append(new_row)
        else:
            new_row = row.copy()
            new_row[cluster_col] = list(cluster_items)  # force list
            new_row[len_col] = len(cluster_items)
            new_rows.append(new_row)

    return pd.DataFrame(new_rows).reset_index(drop=True)


def add_unique_cluster_phrases(df, column="cluster_phrases", threshold=0.85, top_n=3):
    """
    Adds 'unique_cluster_phrases' column beside the input column.
    Splits large clusters first and ensures dataframe resizing works correctly.
    """
    print(f"Input : {df.shape}")
    df = split_large_clusters(df, cluster_col=column, max_items=7)
    print("After splitting:", df.shape)

    model_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\models\paraphrase-mpnet-base-v2"
    model = SentenceTransformer(model_path)

    # Ensure everything passed is a list
    def safe_process(x):
        if isinstance(x, str):
            try:
                x = eval(x)
            except Exception:
                x = [x]
        elif isinstance(x, (np.ndarray, pd.Series)):
            x = x.tolist()
        if not isinstance(x, list):
            x = [x]
        return deduplicate_and_reduce_phrases(x, threshold, top_n, model)

    # Step 1: Deduplication + ranking row-wise
    df["unique_cluster_phrases"] = df[column].apply(safe_process)
    print(f'After safe_process {df["unique_cluster_phrases"].to_markdown()}')

    # Step 2: Global filtering (improved substring-safe version)
    df["unique_cluster_phrases"] = filter_general_terms_globally(df["unique_cluster_phrases"])
    print(f'After global filtering {df["unique_cluster_phrases"].to_markdown()}')

    # Step 3: Row-wise removal of general terms
    df = apply_remove_general_terms(df)

    # Step 4: Length column
    df['unique_cluster_phrases_length'] = df['unique_cluster_phrases'].apply(len)

    print("After processing:", df.shape)
    return df


# input_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\keyphrase_consensus_analysis_6.xlsx"
input_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\batch_extraction_08101275D\keyphrase_consensus_analysis_08101275D.xlsx"

output_excel = input_excel.replace(".xlsx", "_unique.xlsx")

df = pd.read_excel(input_excel)
# print(df.shape) 

print(df[["cluster_phrases"]].to_markdown())
df = add_unique_cluster_phrases(df, column="cluster_phrases", threshold=0.9, top_n=3)
print(df.shape)
# print(df[["cluster_phrases", "unique_cluster_phrases"]])
df.to_excel(output_excel, index=False)