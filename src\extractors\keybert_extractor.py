
from sentence_transformers import SentenceTransformer
from keybert import KeyBERT
import os


class KeyBertExtractor:
    def __init__(self, model_path, top_n =30):
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Local model path does not exist: {model_path}")
        
        try:
            model = SentenceTransformer(model_path)
        except Exception as e:
            print(f"[⚠️] Failed to load model directly, using fallback. Reason: {e}")
            from sentence_transformers.models import Transformer, Pooling
            transformer = Transformer(model_path)
            pooling = Pooling(
                word_embedding_dimension=transformer.get_word_embedding_dimension(),
                pooling_mode='mean'
            )
            model = SentenceTransformer(modules=[transformer, pooling])

        self.model = KeyBERT(model=model_path)
        self.top_n = top_n

    def extract(self, text):
        keywords = self.model.extract_keywords(text, top_n=self.top_n, stop_words='english')
        return [kw[0] for kw in keywords]
