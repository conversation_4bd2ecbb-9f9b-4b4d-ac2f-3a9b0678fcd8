# Unified Keyphrase Extractor Configuration
# This configuration integrates all available extractors

extractors:
  # YAKE - Unsupervised keyword extraction
  yake:
    lan: "en"                    # Language
    max_keywords: 30             # Maximum number of keywords to extract
    dedup_threshold: 0.9         # Deduplication threshold
    n: 2                         # N-gram size (1=unigrams, 2=bigrams, 3=trigrams)

  # Keyphrase Extractor - HuggingFace model-based
  keyphrase:
    model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\keyphrase_model"  # Path to local keyphrase model
    top_n: 30                             # Number of top keyphrases
    score_threshold: 0.5                  # Minimum confidence score

  # Rakun - Graph-based keyword extraction
  rakun:
    top_n: 20                    # Number of top keywords
    merge_threshold: 1.1         # Threshold for merging similar keywords
    alpha: 0.3                   # Alpha parameter for ranking
    token_prune_len: 3           # Minimum token length

  # KeyBERT - BERT-based semantic keyword extraction
  keybert:
    model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"  # Path to sentence transformer model
    top_n: 30                                  # Number of top keywords

  # LLaMA - Large language model extraction with semantic filtering
  llama:
    api_url: "http://***********:5007/lamma_3_2_model"  # LLaMA API endpoint
    topic_name: "Chemical Indexing"                      # Domain/topic name
    topic_info: "Chemical compounds, reactions, and materials science"  # Topic description
    sample_keywords:                                     # Example keywords for the domain
      - "catalyst"
      - "polymer"
      - "synthesis"
      - "reaction"
      - "compound"
      - "material"
      - "chemical"
      - "molecular"
    max_chars: 1000              # Maximum characters per chunk
    similarity_threshold: 0.3    # Semantic similarity threshold for sentence filtering
    top_k_sentences: 8           # Maximum sentences to keep per segment
    model_name: "all-MiniLM-L6-v2"  # SentenceTransformer model for semantic filtering

  # Vocabulary Matcher - Match against curated vocabulary
  vocab_matcher:
    threshold: 0.8               # Similarity threshold for matching
    vocab_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\\curated_vocab.xlsx"  # Path to vocabulary file

# Model paths
model_paths:
  sentence_transformer: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"
  keyphrase_model: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\keyphrase_model"

# Paths to data files
paths:
  curated_vocab: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\curated_vocab.xlsx"
  manual_keyphrases: "section_52_dataset_title_abstract_kw.xlsx"

# Relevance filtering settings
relevance:
  threshold: 0.3               # Minimum semantic similarity to title+abstract
  max_keyphrases: 20           # Maximum number of keyphrases to return
  min_frequency: 1             # Minimum frequency across extractors
  min_length: 3                # Minimum keyphrase length

# Embedding settings
embedding:
  similarity_threshold: 0.35   # Threshold for sentence relevance filtering
  normalize_embeddings: true   # Whether to normalize embeddings

# Output settings
output:
  include_confidence: true     # Include confidence scores in output
  remove_duplicates: true      # Remove duplicate keyphrases
  sort_by_frequency: true      # Sort results by frequency

# Filtering settings
filtering:
  stop_words:                  # Words to filter out
    - "study"
    - "analysis" 
    - "method"
    - "result"
    - "conclusion"
    - "research"
    - "data"
    - "using"
    - "based"
    - "approach"
    - "technique"
    - "process"
  
  min_word_length: 3           # Minimum word length
  max_word_length: 50          # Maximum word length
  remove_numbers: false        # Whether to remove pure numbers
  remove_single_chars: true    # Remove single character words

# Evaluation settings (for validation against manual keyphrases)
evaluation:
  similarity_threshold: 0.8    # Threshold for matching with manual keyphrases
  metrics:                     # Metrics to calculate
    - "precision"
    - "recall" 
    - "f1_score"

# Logging settings
logging:
  level: "INFO"                # Logging level
  show_progress: true          # Show extraction progress
  show_stats: true             # Show extraction statistics
