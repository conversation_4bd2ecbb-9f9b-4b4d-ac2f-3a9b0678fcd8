# Enhanced Keyphrase Extraction Approach for Scientific Articles

## 🎯 **Objective**
Extract the most important and relevant keyphrases from scientific PDF articles (title, abstract, and segments like Results/Conclusion) that are topically relevant to the main research focus.

## 📊 **Current Assets**
- ✅ Existing YAKE-based extraction pipeline
- ✅ Sentence-level semantic similarity filtering
- ✅ Large dataset of manually extracted keyphrases (Excel file)
- ✅ SentenceTransformer embeddings for relevance scoring

## 🚀 **Recommended Multi-Stage Approach**

### **Stage 1: Content Relevance Filtering**
```python
# Already implemented in your codebase
relevant_sentences = retrieve_relevant_sentences(
    title, abstract, section_texts, 
    embedding_model, similarity_threshold=0.35
)
```
**Purpose**: Ensure extracted keyphrases come from content relevant to the main topic.

### **Stage 2: Multi-Method Keyphrase Extraction**

#### **Method A: YAKE (Unsupervised)**
- ✅ Already implemented
- Good for domain-specific technical terms
- Language-agnostic statistical approach

#### **Method B: KeyBERT (Transformer-based)**
- ✅ Partially implemented (commented out)
- Semantically aware using BERT embeddings
- Better for conceptual keyphrases

#### **Method C: Manual Validation Dataset**
- ✅ Available in your Excel file
- Use for training relevance classifiers
- Validate extraction quality

### **Stage 3: Relevance Scoring & Filtering**
```python
# New enhancement in updated code
filtered_keyphrases = filter_keyphrases_by_relevance(
    extracted_keyphrases, title, abstract, 
    embedding_model, relevance_threshold=0.3
)
```

### **Stage 4: Ensemble Ranking**
Combine scores from multiple sources:
- **Extraction Confidence** (YAKE score, KeyBERT score)
- **Relevance Score** (semantic similarity to title+abstract)
- **Manual Validation** (if available from Excel dataset)

## 🔧 **Implementation Strategy**

### **Phase 1: Enhanced Current Pipeline** ✅ COMPLETED
- [x] Add relevance filtering using semantic similarity
- [x] Implement KeyBERT extraction
- [x] Create manual keyphrase dataset loader
- [x] Add evaluation metrics against manual annotations

### **Phase 2: Advanced Features** 🚧 RECOMMENDED NEXT STEPS

#### **A. Supervised Relevance Classifier**
```python
# Train a classifier using manual keyphrases
from sklearn.ensemble import RandomForestClassifier

def train_relevance_classifier(manual_keyphrases_data):
    # Features: keyphrase embeddings, extraction scores, position, etc.
    # Labels: relevant (1) vs irrelevant (0) from manual annotations
    pass
```

#### **B. Domain-Specific Fine-tuning**
```python
# Fine-tune sentence transformer on your scientific domain
from sentence_transformers import SentenceTransformer

def fine_tune_domain_model(scientific_texts, keyphrases):
    # Create training pairs: (text, relevant_keyphrase)
    # Fine-tune model for better domain relevance
    pass
```

#### **C. Hierarchical Keyphrase Extraction**
```python
def extract_hierarchical_keyphrases():
    # Level 1: Broad topic keyphrases (from title+abstract)
    # Level 2: Specific findings (from Results)
    # Level 3: Methodological terms (from Methods)
    pass
```

## 📈 **Quality Metrics**

### **Automatic Evaluation**
- **Precision**: % of extracted keyphrases that are relevant
- **Recall**: % of relevant keyphrases that were extracted
- **F1-Score**: Harmonic mean of precision and recall
- **Relevance Score**: Average semantic similarity to title+abstract

### **Manual Validation** (Using Excel Dataset)
```python
evaluation = evaluate_keyphrases_against_manual(
    extracted_keyphrases, manual_keyphrases, 
    embedding_model, similarity_threshold=0.8
)
```

## ⚙️ **Configuration Parameters**

### **Relevance Filtering**
- `relevance_threshold`: 0.3 (adjust based on domain)
- `max_keyphrases`: 20 (limit output size)
- `similarity_method`: cosine similarity with normalized embeddings

### **Extraction Methods**
- **YAKE**: `n=2` (bigrams), `dedup_threshold=0.9`
- **KeyBERT**: `ngram_range=(1,3)`, `top_n=10`

### **Ensemble Weighting**
- Relevance Score: 70%
- Extraction Confidence: 30%

## 🎯 **Expected Benefits**

1. **Higher Relevance**: Keyphrases directly related to main research topic
2. **Better Coverage**: Multiple extraction methods capture different keyphrase types
3. **Quality Validation**: Manual dataset provides ground truth for evaluation
4. **Configurable**: Adjustable thresholds for different domains/requirements
5. **Scalable**: Can process large numbers of scientific articles efficiently

## 🚀 **Getting Started**

1. **Run the enhanced demo**:
   ```bash
   python enhanced_keyphrase_demo.py
   ```

2. **Integrate with your Excel dataset**:
   ```python
   manual_keyphrases = load_manual_keyphrases("section_52_dataset_title_abstract_kw.xlsx")
   ```

3. **Evaluate and tune parameters**:
   ```python
   # Adjust thresholds based on your domain requirements
   config["relevance"]["threshold"] = 0.4  # More strict
   config["relevance"]["max_keyphrases"] = 15  # Fewer results
   ```

## 📋 **Next Steps**

1. **Test the enhanced pipeline** with your Excel dataset
2. **Evaluate extraction quality** using manual annotations
3. **Fine-tune parameters** based on domain-specific requirements
4. **Consider training supervised models** for even better relevance scoring
5. **Scale to process your full document collection**

This approach leverages your existing infrastructure while significantly improving the relevance and quality of extracted keyphrases through multi-method extraction and semantic filtering.
