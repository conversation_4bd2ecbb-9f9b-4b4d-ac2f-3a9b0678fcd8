# config/config.yaml

# Model paths
model_paths:
  sentence_transformer: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"
  sentence_transformer_specter: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\allenai-specter"

paths:
  curated_vocab: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\\curated\\section_52_vocab_all_cleaned.xlsx"
  embedding_store: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\\embeddings\\vocab_embeddings.pkl"
  extracted_terms_dir: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\output\\extracted_terms"


embedding: 
  # Threshold for filtering extracted terms based on semantic similarity
  similarity_threshold: 0.9


extractors:
  # Fuzzy matching threshold for vocabulary
  fuzzy_match_threshold: 0.9

  # YAKE - Unsupervised keyword extraction
  yake:
    lan: "en"
    max_keywords: 10
    dedup_threshold: 0.9
    n: 2

  # SummaTextRank - TextRank-based keyword extraction
  suma:
    top_n: 10


  # TF-IDF - Term Frequency-Inverse Document Frequency
  tfidf:
    top_n: 10

  # MultipartiteRank - Unsupervised keyword extraction
  multipartiterank:
    top_n: 10

  
  # Vocabulary Matcher - Match against curated vocabulary
  embedding_model:
    model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"

  vocab_matcher:
    threshold: 0.9
    embedding_model:
      model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"

  # RAKE - Rapid Automtic Keyword Extraction
  rake:
    max_length: 4
    max_keywords: 10

  # KeyBERT - BERT-based semantic keyword extraction
  keybert:
    model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-MiniLM-L6-v2"
    top_n: 10

  # TextRank - Unsupervised keyword extraction
  textrank:
    top_n: 10


  # Keyphrase Extractor - HuggingFace model-based
  keyphrase:
    model_path: "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\keyphrase_model"  # local path 
    top_n: 10

  # Rakun - Graph-based keyword extraction
  rakun:
    top_n: 10
    merge_threshold: 1.1
    alpha: 0.3
    token_prune_len: 3

  # PositionRank - Unsupervised keyword extraction
  positionrank:
    top_n: 10
    spacy_model: "en_core_web_sm"
    min_word_count: 2
    min_char_len: 4

  # LLaMA - Large language model extraction with semantic filtering
  llama:
    api_url: "http://172.27.7.85:5007/lamma_3_2_model"


