
# # # src/extractors/position_rank_extractor.py

# import os
# import spacy
# import pytextrank
# from spacy.lang.en.stop_words import STOP_WORDS
# import re
# import yaml

# class PositionRankExtractor:
#     def __init__(self, top_n=10, spacy_model="en_core_web_sm", min_word_count=2, min_char_len=4):
#         try:
#             self.nlp = spacy.load(spacy_model)
#         except OSError:
#             raise OSError(
#                 f"spaCy model '{spacy_model}' not found. "
#                 f"Install via: python -m spacy download {spacy_model}"
#             )

#         if "textrank" not in self.nlp.pipe_names:
#             self.nlp.add_pipe("textrank")

#         self.top_n = top_n
#         self.min_word_count = min_word_count
#         self.min_char_len = min_char_len

#     def is_valid_phrase(self, phrase):
#         phrase = phrase.strip()

#         # Rule-based filtering only
#         if len(phrase) < self.min_char_len:
#             return False
#         if len(phrase.split()) < self.min_word_count:
#             return False
#         if all(word.lower() in STOP_WORDS for word in phrase.split()):
#             return False
#         if re.search(r'^\W+$', phrase):  # only punctuation
#             return False
#         if re.fullmatch(r'[A-Za-z]{1,2}\d*', phrase):  # short chemical-like tokens (e.g., "Mg", "B80")
#             return False
#         return True

#     def extract(self, text):
#         doc = self.nlp(text)
#         raw_phrases = [phrase.text.strip() for phrase in doc._.phrases[: self.top_n * 2]]

#         # Apply rule-based filter
#         filtered_phrases = []
#         seen = set()
#         for p in raw_phrases:
#             if self.is_valid_phrase(p):
#                 key = p.lower()
#                 if key not in seen:
#                     seen.add(key)
#                     filtered_phrases.append(p)

#         return filtered_phrases[: self.top_n]


# src/extractors/position_rank_extractor.py

# src/extractors/position_rank_extractor.py

import spacy
import pytextrank
from spacy.lang.en.stop_words import STOP_WORDS
import re
import yaml

class PositionRankExtractor:
    def __init__(self, top_n=10, spacy_model="en_core_web_sm", min_word_count=2, min_char_len=4):
        try:
            self.nlp = spacy.load(spacy_model)
        except OSError:
            raise OSError(
                f"spaCy model '{spacy_model}' not found. "
                f"Install via: python -m spacy download {spacy_model}"
            )

        if "textrank" not in self.nlp.pipe_names:
            self.nlp.add_pipe("textrank")

        self.top_n = top_n
        self.min_word_count = min_word_count
        self.min_char_len = min_char_len

    def is_valid_phrase(self, phrase):
        phrase = phrase.strip()

        # Basic filters
        if len(phrase) < self.min_char_len:
            return False
        if len(phrase.split()) < self.min_word_count:
            return False
        if all(word.lower() in STOP_WORDS for word in phrase.split()):
            return False
        if re.search(r'^\W+$', phrase):  # only punctuation
            return False
        if re.fullmatch(r'[A-Za-z]{1,2}\d*', phrase):  # short chemical-like tokens (e.g., "Mg", "B80")
            return False

        # Additional stricter filters
        words = [w for w in phrase.split() if w.lower() not in STOP_WORDS]
        if len(words) < self.min_word_count:  # ensure enough non-stopwords
            return False

        # POS-based filter: require at least one NOUN/PROPN
        span_doc = self.nlp(phrase)
        if not any(tok.pos_ in {"NOUN", "PROPN"} for tok in span_doc):
            return False

        # Avoid overly numeric terms
        if sum(c.isdigit() for c in phrase) > len(phrase) * 0.3:
            return False

        return True

    def extract(self, text):
        doc = self.nlp(text)
        raw_phrases = [phrase.text.strip() for phrase in doc._.phrases[: self.top_n * 3]]  # grab more, filter later

        filtered_phrases = []
        seen = set()
        for p in raw_phrases:
            if self.is_valid_phrase(p):
                key = p.lower()
                # Avoid substrings of longer already accepted phrases
                if not any(key in fp.lower() and key != fp.lower() for fp in seen):
                    if key not in seen:
                        seen.add(key)
                        filtered_phrases.append(p)

        return filtered_phrases[: self.top_n]





if __name__ == "__main__":
    def load_config(config_path):
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
        
    doc ={
        "title": "Investigation of Electronic and Topological Properties of Magnesium-coated Boron Fullerenes and Their Interaction with Hydrogen Molecule",
        "abstract": "Various nanostructures have been widely investigated as alternative materials for hydrogen storage using experimental and computational techniques. In this research, adsorption, electronic, topological properties, and some molecular descriptors of magnesium-doped boron fullerenes and their interaction with H2 for hydrogen storage are investigated using density functional theory at B3LYP/6-31G//M062X/6-31G** theoretical level. Structures of B80, Mg12B80, Mg20B80 and Mg30B80 were optimized and their interaction with hydrogen molecule were analyzed. Results shows that charge transfer from Mg to B atoms is responsible for positive charge of Mg atoms. When hydrogen molecule approach to the system, it gets polarized and adsorbed to these boron fullerenes doped with Mg atoms. Also, it was found that Mg12B80 possesses the lowest energy gap (ΔEH-L), lowest hardness (η), and the highest adsorption energy, which indicates the reactivity and the hydrogen storage capability of this structure to adsorb hydrogen.",
        "results": "Fig. 1 shows optimized structures in which Mg atoms bind to the boron atoms in boron fullerene (B80). According to the structure presented by Szwacki et al. [31], when Mg atoms bind to the boron fullerene, charge transfer takes place from Mg atoms to B atoms. Tables 1 to 3 include amount of charge transfers for Mg12B80, Mg20B80 and Mg30B80 when Mg atoms bind to boron pentagonal and boron hexagonal rings, calculated in the context of Mulliken population analysis. This charge transfers make Mg atoms to become positively-charged, that leads to interaction with boron fullerene with negative charge. The binding energies for boron fullerene doped with Mg is calculated b",
        "figure_caption": "Fig. 1. Optimized configurations of side view of a) B80, c) Mg12B80, e) Mg20B80 and g) Mg30B80. Optimized configurations of top view of b) B80, d) Mg12B80, f) Mg20B80 and h) Mg30B80. The blue ball for B, the green ball for Mg.",
        "table_data": "",
        "table_caption": "Table 1. Mulliken charge distribution in the Mg and B atoms of the Mg12B80H2 and average charge transfer (Δq) from Mg to B atoms in pentagonal faces of B80 cage. Table 2. Mulliken charge distribution in the Mg and B atoms of the Mg20B80H2 and average charge transfer(Δq) from Mg to B atoms in hexagonal faces of the B80 cage. Table 3. Mulliken charge distribution in the Mg and B atoms of the Mg30B80H2 and average charge transfer (Δq)from Mg to B atoms in hexagonal faces of the B80 cage. Table 5. Hydrogen absorption energy (Ea) on Metal-coated Boron nanostructures calculated in previous papers and this research."
    }

    raw_text = " ".join(doc.values())
    config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")


    positionrank_extractor = PositionRankExtractor(**config["extractors"]["positionrank"])
    keywords = positionrank_extractor.extract(raw_text)
    print(keywords)
