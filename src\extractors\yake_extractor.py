# # src/extractors/yake_extractor.py

import yake

class YakeExtractor:
    def __init__(self, max_keywords=30, dedup_threshold=0.75, lan="en", language=None, n=2):
        # Allow both 'lan' and 'language' keys
        lang = language if language else lan

        self.kw_extractor = yake.KeywordExtractor(
            lan=lang,
            n=n,
            dedupLim=dedup_threshold,
            top=max_keywords,
            features=None
        )

    def extract(self, text):
        keywords = self.kw_extractor.extract_keywords(text)
        return [kw[0] for kw in keywords if kw[0].strip() != ""]

