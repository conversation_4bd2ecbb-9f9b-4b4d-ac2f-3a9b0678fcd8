#!/usr/bin/env python3
"""
Test script for unified keyphrase extraction with semantic filtering
Demonstrates the complete workflow with sections_texts_dict, topic, and topic_information
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor

def test_unified_extraction():
    """Test the unified keyphrase extraction with semantic filtering."""
    
    print("🧪 TESTING UNIFIED KEYPHRASE EXTRACTION")
    print("=" * 60)
    
    # Define topic and topic information
    topic = "Renewable Energy and Building Efficiency"
    
    topic_information = """
    This topic covers renewable energy systems, energy efficiency in buildings, 
    geothermal energy applications, solar energy systems, energy storage technologies, 
    and sustainable building design. It includes heat pumps, energy optimization, 
    building energy management, and integration of renewable energy sources in 
    building applications.
    """
    
    # Sample keyphrases related to the topic
    sample_keyphrases = [
        "renewable energy", "geothermal energy", "energy efficiency", "heat pump",
        "solar energy", "energy storage", "building optimization", "energy management",
        "sustainable building", "energy consumption", "thermal energy", "energy system"
    ]
    
    # Define sections_texts_dict with title, abstract, and other sections
    sections_texts_dict = {
        "title": "AI-Enhanced Geothermal Energy System for Zero Energy Buildings",
        
        "abstract": """
        This study presents an innovative approach to achieving zero energy buildings 
        through the integration of AI-optimized geothermal energy systems. The research 
        focuses on developing a comprehensive energy management system that combines 
        geothermal heat pumps, thermal storage, and intelligent control algorithms 
        to maximize energy efficiency while minimizing environmental impact.
        """,
        
        "introduction": """
        Zero energy buildings represent a critical advancement in sustainable 
        construction and energy management. These buildings aim to produce as much 
        energy as they consume over the course of a year through renewable energy 
        sources and energy efficiency measures. Geothermal energy systems offer 
        significant potential for achieving this goal due to their consistent 
        performance and low environmental impact. The weather was particularly 
        favorable during our initial site surveys. Coffee was provided to all 
        participants during the planning meetings.
        """,
        
        "methodology": """
        The research methodology involved developing an AI-based optimization 
        algorithm for geothermal heat pump systems. Machine learning techniques 
        were employed to predict building energy demands and optimize system 
        performance. Thermal modeling was conducted using specialized software 
        to simulate various operating conditions. Data collection included 
        temperature measurements, energy consumption monitoring, and system 
        performance analysis. The research team enjoyed excellent catering 
        during the experimental phase.
        """,
        
        "results": """
        The AI-optimized geothermal system achieved a 35% improvement in energy 
        efficiency compared to conventional systems. Peak energy demand was 
        reduced by 28% through intelligent load management. The system demonstrated 
        consistent performance across different seasonal conditions. Thermal 
        storage integration resulted in 15% additional energy savings. Cost 
        analysis showed a payback period of 8.5 years for the enhanced system. 
        The laboratory facilities provided excellent working conditions throughout 
        the study period.
        """,
        
        "conclusion": """
        The integration of AI optimization with geothermal energy systems presents 
        a viable pathway to achieving zero energy buildings. The demonstrated 
        energy efficiency improvements and cost-effectiveness make this approach 
        attractive for sustainable building applications. Future research should 
        focus on scaling these systems for larger commercial applications and 
        exploring integration with other renewable energy sources.
        """
    }
    
    # Create sample text from abstract
    sample_text = sections_texts_dict["abstract"]
    
    print(f"📄 TEST CONFIGURATION:")
    print(f"Topic: {topic}")
    print(f"Topic Info: {topic_information.strip()[:100]}...")
    print(f"Sample Keyphrases: {len(sample_keyphrases)} provided")
    print(f"Sections: {list(sections_texts_dict.keys())}")
    print(f"Sample Text Length: {len(sample_text)} characters")
    
    try:
        # Initialize the unified extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Update configuration with topic information
        print(f"📝 Updating configuration with topic information...")
        extractor.config["extractors"]["llama"]["topic_name"] = topic
        extractor.config["extractors"]["llama"]["topic_info"] = topic_information
        extractor.config["extractors"]["llama"]["sample_keywords"] = sample_keyphrases
        
        # Test semantic filtering first
        print(f"\n🔍 Testing semantic filtering...")
        title = sections_texts_dict["title"]
        abstract = sections_texts_dict["abstract"]
        
        filtered_segments = extractor.filter_sentences_by_semantic_similarity(
            title=title,
            abstract=abstract,
            segments=sections_texts_dict,
            topic_name=topic,
            topic_info=topic_information,
            similarity_threshold=0.3,
            top_k_per_segment=5
        )
        
        print(f"📊 Semantic filtering results:")
        for segment_name, filtered_sentences in filtered_segments.items():
            print(f"  {segment_name}: {len(filtered_sentences)} relevant sentences")
        
        # Test full extraction with semantic filtering
        print(f"\n🚀 Running full extraction with semantic filtering...")
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=sections_texts_dict,
            use_cleaned_text=True  # Enable semantic filtering
        )
        
        # Analyze results
        if not df.empty:
            print(f"\n✅ EXTRACTION SUCCESSFUL!")
            print(f"📊 Results Summary:")
            print(f"  Total keyword instances: {len(df)}")
            print(f"  Unique keywords: {df['keyword'].nunique()}")
            print(f"  Extractors used: {', '.join(df['extractor'].unique())}")
            
            # Show performance by extractor
            print(f"\n🎯 Performance by Extractor:")
            extractor_counts = df['extractor'].value_counts()
            for extractor_name, count in extractor_counts.items():
                unique_count = df[df['extractor'] == extractor_name]['keyword'].nunique()
                print(f"  {extractor_name}: {count} instances, {unique_count} unique keywords")
            
            # Show top keywords
            print(f"\n🏆 Top 10 Keywords:")
            top_keywords = df['keyword'].value_counts().head(10)
            for i, (keyword, count) in enumerate(top_keywords.items(), 1):
                print(f"  {i:2d}. {keyword} ({count} times)")
            
            # Check topic relevance
            topic_related_keywords = []
            for keyword in df['keyword'].unique():
                keyword_lower = keyword.lower()
                if any(topic_word in keyword_lower for topic_word in 
                      ['energy', 'geothermal', 'building', 'thermal', 'system', 'efficiency']):
                    topic_related_keywords.append(keyword)
            
            print(f"\n🎯 Topic-Relevant Keywords ({len(topic_related_keywords)}):")
            for keyword in topic_related_keywords[:10]:
                print(f"  • {keyword}")
            
            # Analyze consensus across extractors
            print(f"\n🔍 ANALYZING EXTRACTOR CONSENSUS...")

            from unified_keyphrase_extractor import analyze_keyphrase_consensus, create_summary_report

            consensus_analysis = analyze_keyphrase_consensus(df)

            print(f"📊 Consensus Results:")
            print(f"  Total unique keyphrases: {len(consensus_analysis)}")

            # Show consensus levels
            consensus_levels = consensus_analysis['extractor_count'].value_counts().sort_index(ascending=False)
            for count, num_keyphrases in consensus_levels.items():
                print(f"  Keyphrases found by {count} extractors: {num_keyphrases}")

            # Show high consensus keyphrases
            high_consensus = consensus_analysis[consensus_analysis['extractor_count'] >= 2]
            if not high_consensus.empty:
                print(f"\n🏆 HIGH CONSENSUS KEYPHRASES (multiple extractors):")
                for i, (_, row) in enumerate(high_consensus.head(5).iterrows(), 1):
                    extractors = ', '.join(row['extractors'])
                    print(f"  {i}. {row['keyphrase']} ({row['extractor_count']} extractors: {extractors})")

            # Calculate accuracy metrics
            total_keywords = len(consensus_analysis)
            high_consensus_count = len(high_consensus)
            consensus_rate = (high_consensus_count / total_keywords * 100) if total_keywords > 0 else 0

            print(f"\n📈 ACCURACY METRICS:")
            print(f"  Consensus Rate: {consensus_rate:.1f}% ({high_consensus_count}/{total_keywords})")
            print(f"  This indicates how many keyphrases are validated by multiple extractors")

            # Save detailed results
            print(f"\n💾 Saving comprehensive results...")

            # Save raw results
            df.to_excel("test_unified_extraction_results.xlsx", index=False)
            print(f"✅ Raw results: test_unified_extraction_results.xlsx")

            # Save consensus analysis
            consensus_analysis.to_excel("test_consensus_analysis.xlsx", index=False)
            print(f"✅ Consensus analysis: test_consensus_analysis.xlsx")

            # Create comprehensive summary report
            create_summary_report(df, consensus_analysis, sections_texts_dict, topic, topic_information)
            print(f"✅ Summary report: extraction_summary_report.xlsx")

            return True
            
        else:
            print("⚠️  No keywords extracted")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_options():
    """Test different configuration options."""
    print(f"\n🔧 TESTING CONFIGURATION OPTIONS")
    print("=" * 40)
    
    try:
        extractor = UnifiedKeyphraseExtractor()
        
        # Test different similarity thresholds
        thresholds = [0.2, 0.3, 0.4]
        
        title = "Renewable Energy Systems"
        abstract = "This study focuses on renewable energy applications in buildings."
        segments = {
            "methods": "We analyzed various renewable energy technologies including solar and geothermal systems.",
            "results": "The results showed significant energy savings. The weather was nice during testing."
        }
        
        print(f"Testing different similarity thresholds:")
        for threshold in thresholds:
            filtered = extractor.filter_sentences_by_semantic_similarity(
                title=title,
                abstract=abstract,
                segments=segments,
                similarity_threshold=threshold,
                top_k_per_segment=3
            )
            
            total_sentences = sum(len(sentences) for sentences in filtered.values())
            print(f"  Threshold {threshold}: {total_sentences} sentences retained")
            
    except Exception as e:
        print(f"Configuration test failed: {e}")

if __name__ == "__main__":
    print("🚀 UNIFIED KEYPHRASE EXTRACTION TEST SUITE")
    print("=" * 60)
    
    # Run main test
    success = test_unified_extraction()
    
    if success:
        # Run configuration tests
        test_configuration_options()
        
        print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print(f"✅ The unified keyphrase extraction system is working correctly")
        print(f"✅ Semantic filtering is functioning as expected")
        print(f"✅ Topic integration is working properly")
        
    else:
        print(f"\n❌ TESTS FAILED")
        print(f"Please check the error messages above and ensure:")
        print(f"  • All required dependencies are installed")
        print(f"  • LLaMA API endpoint is accessible (if using LLaMA)")
        print(f"  • SentenceTransformer models are available")
    
    print(f"\n📋 USAGE SUMMARY:")
    print(f"To use the unified extractor in your code:")
    print(f"""
    from unified_keyphrase_extractor import UnifiedKeyphraseExtractor
    
    extractor = UnifiedKeyphraseExtractor()
    
    # Update with your topic information
    extractor.config["extractors"]["llama"]["topic_name"] = "Your Topic"
    extractor.config["extractors"]["llama"]["topic_info"] = "Your topic description"
    
    # Extract with semantic filtering
    df = extractor.extract_from_text(
        text=your_text,
        section_texts=your_sections_dict,
        use_cleaned_text=True
    )
    """)
