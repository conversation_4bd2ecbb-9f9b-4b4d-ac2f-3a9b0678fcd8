def get_mac_accuracy(mac_data, cur_data, tan_number):
    """
    """
    try:
        tan_out = []
        match_per_out = []
        noise_per_out =[]
        ml_data_count_out =[]
        cur_data_count_out =[]
        ml_data_count_out_cth =[]
        cur_data_count_out_cth =[]
        noise_words_out = []
        missed_words_out = []
        accuracy_data_df_ship = []
        noise_words_count_out = []
        missed_words_count_out = []
        miss_per_out = []
        match_words_out = []
        match_words_count_out = []
        final_mac_words_lower = [str(x).lower() for x in mac_data]
        final_cur_words_final = [x for x in cur_data if str(x).lower() !="none"]
        final_cur_words_lower = [str(x).lower() for x in cur_data if str(x).lower() !="none"]
        final_cur_words_lower = list(set(final_cur_words_lower))
        final_mac_words_lower = list(set(final_mac_words_lower))
        noise_cth = list(set(final_mac_words_lower)-set(final_cur_words_lower))
        #print(noise_cth)
        missed_cth = list(set(final_cur_words_lower)-set(final_mac_words_lower))
        #print(missed_cth)
        match_cth = list(set(final_cur_words_lower)-set(missed_cth))
        #print(match_cth)
        try:
            match_per_out_values = round((len(match_cth)/len(final_cur_words_lower))*100,)
        except Exception as error:
            match_per_out_values = 0
        try:
            noise_per_out_values = round((len(noise_cth)/len(final_mac_words_lower))*100,)
        except Exception as error:
            noise_per_out_values = 0
        try:
            miss_per_out_values = round((len(missed_cth)/len(final_cur_words_lower))*100,)
        except Exception as error:
            miss_per_out_values = 0
        tan_out.append(tan_number)
        match_per_out.append(match_per_out_values)
        noise_per_out.append(noise_per_out_values)
        miss_per_out.append(miss_per_out_values)
        ml_data_count_out.append(len(final_mac_words_lower))
        cur_data_count_out.append(len(final_cur_words_lower))
        ml_data_count_out_cth.append(" | ".join(final_mac_words_lower))
        cur_data_count_out_cth.append(" | ".join(final_cur_words_lower))
        noise_words_out.append(" | ".join(noise_cth))
        missed_words_out.append(" | ".join(missed_cth))
        match_words_out.append(" | ".join(match_cth))

        noise_words_count_out.append(len(noise_cth))
        missed_words_count_out.append(len(missed_cth))
        match_words_count_out.append(len(match_cth))

        df_out = pd.DataFrame()
        df_out["tan_number"] = tan_out
        df_out["mac_count"] = ml_data_count_out
        df_out["cur_count"] = cur_data_count_out
        df_out["match_%"] = match_per_out
        df_out["noise_%"] = noise_per_out
        df_out["missed_%"] = miss_per_out




        df_out["match_count"] = match_words_count_out
        df_out["noise_count"] = noise_words_count_out
        df_out["missed_count"] = missed_words_count_out


        df_out["MAC_data"] = ml_data_count_out_cth
        df_out["cur_data"] = cur_data_count_out_cth
        df_out["match_data"] = match_words_out
        df_out["noise_data"] = noise_words_out
        df_out["missed_data"] = missed_words_out
        return df_out
    except Exception as error:
        print(error)
        pass
