#!/usr/bin/env python3
"""
Demo script for Enhanced LLaMA Keyphrase Extraction with Semantic Filtering
Shows how to use the new semantic filtering functionality.
"""

from src.extractors.llama_extractor_chunked import (
    extract_llama_keyphrases_from_segments,
    extract_llama_keyphrases_with_semantic_filtering,
    filter_sentences_by_semantic_similarity
)

def demo_semantic_filtering():
    """Demonstrate semantic sentence filtering."""
    print("🔍 SEMANTIC SENTENCE FILTERING DEMO")
    print("=" * 60)
    
    # Sample scientific article
    title = "Sarcopenia in chronic kidney disease: prevalence and diagnostic criteria"
    abstract = """
    Sarcopenia is a syndrome characterized by progressive loss of skeletal muscle mass and strength. 
    In chronic kidney disease (CKD) patients, sarcopenia prevalence varies widely depending on 
    diagnostic criteria used. This study evaluates different sarcopenia definitions in CKD populations.
    """
    
    # Large text sections with mixed relevance
    methods = """
    This cross-sectional study included 150 adult CKD patients stages 3-5 from three nephrology clinics. 
    Sarcopenia was diagnosed using three different criteria: European Working Group on Sarcopenia in 
    Older People 2 (EWGSOP2), Asian Working Group for Sarcopenia (AWGS), and Foundation for the 
    National Institutes of Health (FNIH). Body composition was assessed using dual-energy X-ray 
    absorptiometry (DXA). Muscle strength was measured using handgrip strength testing. Statistical 
    analysis was performed using SPSS version 25. The weather was particularly sunny during data 
    collection. Participants were recruited through convenience sampling. The study protocol was 
    approved by the institutional review board. Coffee was provided to participants during testing.
    """
    
    results = """
    The study included 150 CKD patients (mean age 65.2 ± 12.4 years, 60% male). Sarcopenia prevalence 
    varied significantly: 15.3% using EWGSOP2 criteria, 22.7% using AWGS criteria, and 28.0% using 
    FNIH criteria. Sarcopenic patients had significantly lower BMI (22.1 ± 3.2 vs 25.8 ± 4.1 kg/m², 
    p<0.001) and higher visceral fat area. Strong correlation between muscle mass and kidney function 
    was observed (r=0.68, p<0.001). The laboratory had new equipment installed last month. Muscle 
    strength correlated with eGFR in all patient groups. Some participants mentioned enjoying the 
    hospital cafeteria food during their visits.
    """
    
    segments = {
        "title": title,
        "abstract": abstract,
        "methods": methods,
        "results": results
    }
    
    print(f"📄 Title: {title}")
    print(f"📝 Abstract: {abstract[:100]}...")
    
    # Apply semantic filtering
    print(f"\n🔍 Applying semantic filtering...")
    filtered_segments = filter_sentences_by_semantic_similarity(
        title=title,
        abstract=abstract,
        segments={"methods": methods, "results": results},
        similarity_threshold=0.3,
        top_k_per_segment=5
    )
    
    print(f"\n📊 FILTERING RESULTS:")
    for segment_name, filtered_sentences in filtered_segments.items():
        print(f"\n{segment_name.upper()}:")
        print(f"  Filtered sentences ({len(filtered_sentences)}):")
        for i, sentence in enumerate(filtered_sentences, 1):
            print(f"    {i}. {sentence[:80]}...")

def demo_enhanced_llama_extraction():
    """Demonstrate enhanced LLaMA extraction with semantic filtering."""
    print("\n" + "=" * 60)
    print("🚀 ENHANCED LLAMA EXTRACTION DEMO")
    print("=" * 60)
    
    # Sample data
    api_url = "http://172.27.7.85:5007/lamma_3_2_model"  # Update with your API URL
    
    segments = {
        "title": "Machine learning approaches for predicting sarcopenia in chronic kidney disease",
        "abstract": """
        Sarcopenia is a common complication in chronic kidney disease (CKD) patients, characterized by 
        loss of muscle mass and function. Early detection is crucial for intervention. This study 
        develops machine learning models to predict sarcopenia risk in CKD populations using clinical 
        and biochemical parameters.
        """,
        "methods": """
        We collected data from 500 CKD patients including demographics, laboratory values, and body 
        composition measurements. Sarcopenia was defined using EWGSOP2 criteria. Machine learning 
        algorithms including random forest, support vector machines, and neural networks were trained. 
        Feature selection was performed using recursive feature elimination. The weather was cloudy 
        during data collection. Model performance was evaluated using cross-validation. Coffee breaks 
        were provided during long data collection sessions.
        """,
        "results": """
        The random forest model achieved the highest accuracy (85.2%) for sarcopenia prediction. 
        Key predictive features included serum albumin, handgrip strength, and estimated glomerular 
        filtration rate. The neural network model showed good sensitivity (82.1%) but lower specificity. 
        Feature importance analysis revealed muscle mass index as the most significant predictor. 
        The hospital cafeteria served excellent sandwiches during the study period. Cross-validation 
        confirmed model robustness across different patient subgroups.
        """
    }
    
    topic_name = "Sarcopenia and Chronic Kidney Disease"
    topic_info = "Medical research on muscle wasting disorders in kidney disease patients"
    sample_keywords = ["sarcopenia", "chronic kidney disease", "muscle mass", "machine learning", "prediction"]
    
    print(f"📄 Document: {segments['title']}")
    print(f"🎯 Topic: {topic_name}")
    
    try:
        # Method 1: Original extraction
        print(f"\n🔄 METHOD 1: Original LLaMA extraction")
        df_original = extract_llama_keyphrases_from_segments(
            api_url=api_url,
            segments=segments,
            topic_name=topic_name,
            topic_info=topic_info,
            sample_keywords=sample_keywords,
            max_chars=800
        )
        
        print(f"📊 Original results: {len(df_original)} segments processed")
        if not df_original.empty:
            total_original = sum(len(kp) if isinstance(kp, list) else 0 for kp in df_original['keyphrases'])
            print(f"🎯 Total keyphrases: {total_original}")
        
        # Method 2: Enhanced extraction with semantic filtering
        print(f"\n🔄 METHOD 2: Enhanced extraction with semantic filtering")
        df_enhanced = extract_llama_keyphrases_with_semantic_filtering(
            api_url=api_url,
            segments=segments,
            topic_name=topic_name,
            topic_info=topic_info,
            sample_keywords=sample_keywords,
            max_chars=800,
            similarity_threshold=0.3,
            top_k_sentences=5
        )
        
        print(f"📊 Enhanced results: {len(df_enhanced)} segments processed")
        if not df_enhanced.empty:
            total_enhanced = sum(len(kp) if isinstance(kp, list) else 0 for kp in df_enhanced['keyphrases'])
            print(f"🎯 Total keyphrases: {total_enhanced}")
            
            # Show filtered vs original segments
            filtered_count = sum(1 for _, row in df_enhanced.iterrows() if row['similarity_filtered'])
            print(f"🔍 Semantically filtered segments: {filtered_count}")
        
        print(f"\n✅ COMPARISON:")
        print(f"  Original method: Processes all text regardless of relevance")
        print(f"  Enhanced method: Focuses on sentences most relevant to title + abstract")
        print(f"  Benefits: Higher quality, more relevant keyphrases")
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        print("This might be due to LLaMA API not being available.")
        print("The semantic filtering functionality works independently of the API.")

def demo_configuration_options():
    """Show different configuration options for semantic filtering."""
    print("\n" + "=" * 60)
    print("⚙️ CONFIGURATION OPTIONS DEMO")
    print("=" * 60)
    
    print("""
    📋 KEY PARAMETERS FOR SEMANTIC FILTERING:
    
    1. similarity_threshold (0.0-1.0):
       • 0.2: Very permissive - includes loosely related sentences
       • 0.3: Balanced - good for most scientific texts
       • 0.4: Strict - only highly relevant sentences
       • 0.5: Very strict - only very closely related content
    
    2. top_k_sentences (int):
       • 5: Conservative - keeps only top sentences
       • 10: Balanced - good coverage while filtering noise
       • 15: Liberal - keeps more content
    
    3. model_name (str):
       • "all-MiniLM-L6-v2": Fast, good quality (default)
       • "all-mpnet-base-v2": Higher quality, slower
       • "sentence-transformers/all-roberta-large-v1": Best quality, slowest
    
    📈 RECOMMENDED SETTINGS:
    
    For Medical/Scientific texts:
    - similarity_threshold: 0.3
    - top_k_sentences: 8-10
    - model_name: "all-MiniLM-L6-v2"
    
    For Technical/Engineering texts:
    - similarity_threshold: 0.25
    - top_k_sentences: 10-12
    - model_name: "all-MiniLM-L6-v2"
    
    For Chemistry/Materials texts:
    - similarity_threshold: 0.35
    - top_k_sentences: 6-8
    - model_name: "all-mpnet-base-v2"
    """)

if __name__ == "__main__":
    try:
        demo_semantic_filtering()
        demo_enhanced_llama_extraction()
        demo_configuration_options()
        
        print("\n" + "=" * 60)
        print("🎉 ENHANCED LLAMA DEMO COMPLETED!")
        print("=" * 60)
        print("""
        📋 SUMMARY OF ENHANCEMENTS:
        
        1. 🔍 SEMANTIC FILTERING:
           - Filters sentences by similarity to title + abstract
           - Reduces noise from irrelevant content
           - Configurable similarity thresholds
        
        2. 🎯 IMPROVED PROMPTS:
           - Better instructions for keyphrase extraction
           - Handles repeated relevant keyphrases appropriately
           - Enhanced context awareness
        
        3. 📊 ENHANCED OUTPUT:
           - Tracks which segments were filtered
           - Better comparison capabilities
           - Improved quality metrics
        
        🚀 NEXT STEPS:
        - Integrate into your unified_keyphrase_extractor.py
        - Adjust similarity_threshold for your domain
        - Test with your specific scientific texts
        - Compare quality with original method
        """)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
