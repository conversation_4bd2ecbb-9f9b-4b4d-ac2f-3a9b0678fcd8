# # src/semantic/embedder.py
# from sentence_transformers import SentenceTransformer
# import numpy as np
# import pickle
# import os

# class EmbeddingModel:
#     def __init__(self, model_name='paraphrase-mpnet-base-v2'):
#         self.model = SentenceTransformer(model_name)

#     def encode(self, texts, normalize=True):
#         embeddings = self.model.encode(texts, convert_to_numpy=True, normalize_embeddings=normalize)
#         return embeddings

#     def save_embeddings(self, embeddings, terms, output_path):
#         data = {'terms': terms, 'embeddings': embeddings}
#         with open(output_path, 'wb') as f:
#             pickle.dump(data, f)

#     def load_embeddings(self, file_path):
#         with open(file_path, 'rb') as f:
#             data = pickle.load(f)
#         return data['terms'], data['embeddings']

# from sentence_transformers import SentenceTransformer

# def load_local_model(model_path):
#     """Load model from local directory"""
#     return SentenceTransformer(model_path, device='cpu')

# # In your pipeline configuration
# model = load_local_model('./models/paraphrase-mpnet-base-v2')
# class EmbeddingModel:
#     def __init__(self, model_name='paraphrase-mpnet-base-v2', local_model_path=None):
#         if local_model_path and os.path.exists(local_model_path):
#             self.model = SentenceTransformer(local_model_path)
#         else:
#             self.model = SentenceTransformer(model_name)

##########################################

# # src/semantic/embedder.py
# from sentence_transformers import SentenceTransformer
# import numpy as np
# import pickle
# import os



# class EmbeddingModel:
#     def __init__(self, model_name='paraphrase-mpnet-base-v2', local_model_path=None):
#         if local_model_path:
#             if not os.path.exists(local_model_path):
#                 raise FileNotFoundError(f"Local model path not found: {local_model_path}")
#             self.model = SentenceTransformer(local_model_path)
#         else:
#             self.model = SentenceTransformer(model_name)

#     def encode(self, texts, normalize=True):
#         embeddings = self.model.encode(texts, convert_to_numpy=True, normalize_embeddings=normalize)
#         return embeddings

#     def save_embeddings(self, embeddings, terms, output_path):
#         data = {'terms': terms, 'embeddings': embeddings}
#         with open(output_path, 'wb') as f:
#             pickle.dump(data, f)

#     def load_embeddings(self, file_path):
#         with open(file_path, 'rb') as f:
#             data = pickle.load(f)
#         return data['terms'], data['embeddings']


###################################
import os
import pandas as pd
from sentence_transformers import SentenceTransformer
import pickle

class EmbeddingModel:
    def __init__(self, model_name='paraphrase-mpnet-base-v2', local_model_path=None):
        print(f"{local_model_path =}")
        if local_model_path:
            if not os.path.exists(local_model_path):
                raise FileNotFoundError(f"Local model path not found: {local_model_path}")
            self.model = SentenceTransformer(local_model_path)
        else:
            self.model = SentenceTransformer(model_name)

    def encode(self, texts, normalize=True):
        embeddings = self.model.encode(texts, convert_to_numpy=True, normalize_embeddings=normalize)
        return embeddings

    def save_embeddings(self, embeddings, terms, output_path):
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        data = {'terms': terms, 'embeddings': embeddings}
        with open(output_path, 'wb') as f:
            pickle.dump(data, f)
        print(f"[✅] Embeddings saved to: {output_path}")

    def load_embeddings(self, file_path):
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        return data['terms'], data['embeddings']


# ---- EMBEDDING STORE CREATION ----
def create_vocab_embedding_store(vocab_excel_path: str, output_pkl_path: str, model_path=None):
    print(f"[📄] Reading curated vocab from: {vocab_excel_path}")
    df = pd.read_excel(vocab_excel_path)
    
    if 'CTH' not in df.columns:
        raise ValueError("Missing 'CTH' column in curated vocab file.")

    # Clean and filter terms
    terms = df['CTH'].dropna().astype(str).str.strip().unique().tolist()
    print(f"[🔍] Found {len(terms)} unique terms.")

    # Load embedding model
    embedder = EmbeddingModel(local_model_path=model_path)
    embeddings = embedder.encode(terms)

    # Save to .pkl
    embedder.save_embeddings(embeddings, terms, output_pkl_path)



if __name__ == "__main__":
    vocab_excel_path = r"C:\Users\<USER>\Desktop\chemindexing_2025\data\curated\section_52_vocab_all_cleaned.xlsx"

    output_pkl_path = r"C:\Users\<USER>\Desktop\chemindexing_2025\data\embeddings\vocab_embeddings.pkl"

    local_model_path = r"C:\Users\<USER>\Desktop\chemindexing_2025\models\paraphrase-mpnet-base-v2"  

    create_vocab_embedding_store(
        vocab_excel_path=vocab_excel_path,
        output_pkl_path=output_pkl_path,
        model_path=local_model_path
    )
