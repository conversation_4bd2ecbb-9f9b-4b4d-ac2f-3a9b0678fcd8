sentence,keyphrase_list,extractor_list,input_segment,keyphrase_count
"Leveraging AI and geothermal cogeneration to boost energy efficiency in a multipurpose zero energy building 
    Recent studies have targeted the emission of less CO2 alongside saving more energy","['zero energy buildings', 'energy consumption', 'geothermal energy', 'energy efficiency', 'multipurpose zero energy building', 'geothermal cogeneration']",keyphrase|llama|rakun|yake,main_text,6
"Using BEopt software, the simulation, and optimization of the complex was performed","['effective optimization', 'Multi objective optimization', 'optimization process', 'multi-objective optimization', 'optimization results']",keyphrase|llama|rakun|yake,abstract,5
"This diagram displays the normal probability distribution of
residuals derived from linear regression analysis, providing insights into
the statistical properties and relationships between these objectives.
Fig","['linear regression', 'probability distribution', 'residuals', 'statistical properties']",llama|rakun,results,4
"Evaluating the cogeneration system’s performance in Birmingham demonstrated its capability to generate 6884.61 MWh of electricity, 27841.66 MWh of heating, and 2462.53 MWh of cooling per year using geothermal energy","['performance', 'geothermal energy', 'birmingham demonstrated']",keyphrase|llama|rakun|yake,abstract,3
"Exergy efficiency (EE), and cost rate (CR), the outputs of EES, ought to be optimized via a combination of ANN and NSGA-II (Non-dominated Sorting Genetic Algorithm II)","['genetic algorithms', 'non-dominated sorting genetic algorithm ii', 'exergy efficiency']",keyphrase|llama|rakun|yake,abstract,3
Multi objective optimization using ANN & genetic algorithm,"['objective functions', 'Multi objective optimization', 'multi-objective optimization']",keyphrase|llama|rakun|yake,results,3
"These variables have been carefully selected, and their ranges
have been defined to ensure the most effective optimization results.
The optimal values of decision variables and objective functions are
presented in Table 12, showcasing the outcomes of the multi-objective
optimization process","['optimization process', 'effective optimization', 'objective functions']",keyphrase|llama|rakun|yake,results,3
"19 displays the predicted values of functions plotted against the
remaining values of those functions","['remaining values', 'predicted values']",rakun,results,2
"The
optimization aims to improve system performance and reduce costs by
combining neural network and genetic optimization algorithms","['reduce costs', 'neural network']",keyphrase|llama|rakun,results,2
"The
process involves analyzing results and output data from the proposed
system using an intelligent neural network, converting the analyzed data
into a smart mathematical function, and then optimizing this function
using a genetic multi-objective optimization tool","['objective functions', 'multi-objective optimization']",keyphrase|llama|rakun|yake,results,2
"The energy assessment revealed the building’s electricity consumption at 2594.48 MWh, heating demand at 3157.63 MWh, and cooling requirement at 75.56 MWh annually","['cooling requirement', 'mwh']",keyphrase|llama|rakun|yake,abstract,2
"This study focuses on a proposed cogeneration geothermal system, comprising two Rankine cycle units and an absorption chiller, designed to fulfill the clean energy requirements of a 5-story ZEB in Birmingham, England","['geothermal system', 'birmingham demonstrated']",keyphrase|rakun|yake,abstract,2
"This type of visualization is
commonly used in data analysis to assess the accuracy of predictions and
understand how well the predicted values align with the actual
remaining values","['data analysis', 'visualization']",llama,results,2
"14, providing a clear overview of the
optimization goals and methods.
Table 11 outlines the optimization decision variables (input parameters)
and their ranges that are crucial for optimum proposed system
process",['decision variables'],rakun,results,1
"16 showcases the Pareto chart depicting the two objective
functions under evaluation",['Pareto chart'],llama|rakun,results,1
"17 displays a histogram representing the two objective functions:
EE and the CR, which are key outcomes for the training and
computation processes",['histogram'],llama,results,1
"18 illustrates the normality plot of the two OFs concerning EE
and CR",['normality plot'],llama,results,1
"Comparing the building’s energy consumption with the system’s production highlighted significant savings: 4290.28 MWh of electricity, 24682.71 MWh of heating, and 2385.01 MWh of cooling annually while meeting the complex’s energy needs yearly.
     
     
    Table 10 presents a comparison between the current study and the
research by Amiri Rad et al",['production highlighted'],rakun,main_text,1
"The horizontal axis represents the values of the
objective functions, while the vertical axis illustrates the percentage
frequency distribution.
Fig",['probability distribution'],llama|rakun,results,1
The net energy consumption of the building was calculated in one year,['energy consumption'],llama|rakun|yake,abstract,1
"The optimization results showed that in the most optimal operating mode, an EE of 69.11 % and a CR of 23.1 $/h can be reached",['optimization results'],keyphrase|llama|yake,abstract,1
"This optimization strategy combines neural
network and genetic algorithms to enhance system performance and
reduce costs effectively.
In multi-objective optimizations, the target is to optimize each
objective function while considering the trade-offs between them",['genetic algorithms'],keyphrase|llama|rakun|yake,results,1
"To achieve part of this purpose, zero energy buildings (ZEBs) are introduced, replacing fossil fuels with renewable energies",['zero energy buildings'],keyphrase|yake,abstract,1
