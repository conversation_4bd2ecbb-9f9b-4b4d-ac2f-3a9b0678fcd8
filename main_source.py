import os
import time
import yaml
import pandas as pd
import ast

# from src.preprocess.cleaner import clean_and_split_sections
from src.extractors.vocab_matcher import matched_vocab_terms    
from src.extractors.yake_extractor import YakeExtractor
from src.extractors.keyphrase_extractor_v2 import KeyphraseExtractor  
from src.extractors.rakun_extractor import RakunExtractor  
# from src.extractors.position_rank_extractor import PositionRankExtractor
from src.extractors.llama_extractor_chunked import extract_llama_keyphrases_from_segments

import warnings
warnings.filterwarnings("ignore", category=FutureWarning, module="torch.nn.modules.module")

# Internal config
DEFAULT_CONFIG_0 = {
    "extractors": {
        "yake": {
            "lan": "en",
            "max_keywords": 20,
            "dedup_threshold": 0.9,
            "n": 2
        },
        "keyphrase": {
            "model_path": "models/keyphrase_model",
            "top_n": 30
            # "score_threshold":0.5   ### For V2
        }
    }
}

def extract_keywords_with_sources_0(raw_text, config=DEFAULT_CONFIG_0):
    """
    Extracts keywords from the input text using YAKE and HuggingFace Keyphrase extractor.
    Returns a dataframe with the keyword, source sentence, and extractor used.
    """
    # Sentence-level splitting
    sentences = [s.strip() for s in raw_text.split('. ') if s.strip()]
    
    # Extract keywords from full text
    yake_extractor = YakeExtractor(**config['extractors']['yake'])
    keyphrase_extractor = KeyphraseExtractor(**config['extractors']['keyphrase'])

    yake_keywords = set(yake_extractor.extract(raw_text))
    keyphrase_keywords = set(keyphrase_extractor.extract(raw_text))

    results = []

    for sentence in sentences:
        for keyword in yake_keywords:
            if keyword in sentence:
                results.append({
                    'extractor': 'yake',
                    'keyword': keyword,
                    'source_sentence': sentence
                })
        for keyword in keyphrase_keywords:
            if keyword in sentence:
                results.append({
                    'extractor': 'keyphrase',
                    'keyword': keyword,
                    'source_sentence': sentence
                })

    return pd.DataFrame(results)


# Internal config
DEFAULT_CONFIG = {
    
    "extractors": {
        "yake": {
            "lan": "en",
            "max_keywords": 20,
            "dedup_threshold": 0.9,
            "n": 2
        },
        "keyphrase": {
            "model_path": "models/keyphrase_model", 
            "top_n": 30,
            "score_threshold": 0.5
        }
    }
}

def load_config(config_path):
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)
    

config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\config\config.yaml")




# def extract_keywords_with_sources(raw_text, config=DEFAULT_CONFIG):
def extract_keywords_with_sources(section_texts, test_topic, test_topic_information, test_sample_keyphrases, config=config):
    """
    Extracts keywords from input text using YAKE and HF Keyphrase extractor.
    Returns a dataframe with columns: 'extractor', 'keyword', 'source_sentence'
    """
    raw_text = " ".join(section_texts.values())
    # print(f"{config =}")

    # Split into sentences
    sentences = [s.strip() for s in raw_text.split('. ') if s.strip()]

    # Step 1: Clean & tokenize
    # sentences = clean_and_split_sections(raw_text)
    
    # Step 2: Vocab Match

    vocab_matches = matched_vocab_terms(raw_text, config, threshold=0.9)
    # print(f"{vocab_matches =}")


    # Extract keywords
    yake_extractor = YakeExtractor(**config["extractors"]["yake"])
    keyphrase_extractor = KeyphraseExtractor(**config["extractors"]["keyphrase"])
    rakun_extractor = RakunExtractor(**config['extractors']['rakun'])
    # positionrank_extractor = PositionRankExtractor(**config["extractors"]["positionrank"])
    yake_keywords = set(yake_extractor.extract(raw_text))
    keyphrase_keywords = set(keyphrase_extractor.extract(raw_text))
    
    # positionrank_keywords = positionrank_extractor.extract(raw_text)
    rakun_keywords = rakun_extractor.extract(raw_text)
    results = []
    # sentence = raw_text
    # Map keywords back to source sentence
    for sentence in sentences:
        for keyword in yake_keywords:
            if keyword in sentence:
                results.append({
                    "extractor": "yake",
                    "keyword": keyword,
                    "source_sentence": sentence
                })
        for keyword in keyphrase_keywords:
            if keyword in sentence:
                results.append({
                    "extractor": "keyphrase",
                    "keyword": keyword,
                    "source_sentence": sentence
                })

        for keyword in vocab_matches:
            if keyword in sentence:
                results.append({
                    "extractor": "vocab_matches",
                    "keyword": keyword,
                    "source_sentence": sentence
                })

        for keyword in rakun_keywords:
            if keyword in sentence:
                results.append({
                    "extractor": "rakun_keywords",
                    "keyword": keyword,
                    "source_sentence": sentence
                })

        # for keyword in positionrank_keywords:
        #     if keyword in sentence:
        #         results.append({
        #             "extractor": "positionrank_keywords",
        #             "keyword": keyword,
        #             "source_sentence": sentence
        #         })


# config['extractors']['llama']['api_url']
    llama_keywords = get_llama_keywords("http://172.27.7.85:5007/lamma_3_2_model", section_texts, test_topic, test_topic_information, test_sample_keyphrases)
    print(f"{llama_keywords =}")
    if llama_keywords is not None:
        for term in llama_keywords:
            sentence = term["sentence"]
            keyword = term["keyword"]
                
            results.append({
                "extractor": "llama_keywords",
                "keyword": keyword,
                "source_sentence": sentence
            })
        print(f"{results =}")

    return pd.DataFrame(results)




def get_llama_keywords(api_url, section_texts_dict, topic, topic_information, sample_keyphrases):
    df = extract_llama_keyphrases_from_segments( 
        api_url=api_url,
        segments = section_texts_dict,
        topic_name= topic,
        topic_info= topic_information,
        sample_keywords= sample_keyphrases,
        max_chars = 1000
    )

    # flat_list = [item for sublist in df['keyphrases'].to_list() for item in sublist]
    # Convert string representation of list to actual list if necessary
    df["keyphrases"] = df["keyphrases"].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)

    # Flatten into list of dicts
    data_for_loop = [
        {"sentence": row["text"], "keyword": keyword}
        for _, row in df.iterrows()
        for keyword in row["keyphrases"]
    ]
    # if len(data_for_loop) > 0:
    #     return None
    print(data_for_loop)
    return data_for_loop




import inflect

p = inflect.engine()

def merge_and_deduplicate_keywords(df):
    """
    Deduplicate and merge keywords:
    1. Drop partial matches (keep longer string)
    2. Drop singular/plural duplicates (keep longer)
    """
    # Step 1: Normalize
    df['keyword'] = df['keyword'].astype(str).str.strip()
    df['keyword_lower'] = df['keyword'].str.lower()

    # Step 2: Merge exact lowercase duplicates
    grouped = (
        df.groupby('keyword_lower')
        .agg({
            'keyword': lambda x: max(x, key=len),
            'extractor': lambda x: '|'.join(sorted(set('|'.join(x).split('|')))),
            'source_sentence': lambda x: ' | '.join(sorted(set(x)))
        })
        .reset_index(drop=True)
    )

    # Step 3: Drop partial matches (keep longer)
    to_remove = set()
    for i, kw in enumerate(grouped['keyword']):
        for j, other_kw in enumerate(grouped['keyword']):
            if i != j and kw.lower() in other_kw.lower() and len(other_kw) > len(kw):
                # Merge extractors/sentences into longer keyword
                grouped.loc[j, 'extractor'] = '|'.join(
                    sorted(set(grouped.loc[j, 'extractor'].split('|')) |
                        set(grouped.loc[i, 'extractor'].split('|')))
                )
                grouped.loc[j, 'source_sentence'] = ' | '.join(
                    sorted(set(grouped.loc[j, 'source_sentence'].split(' | ')) |
                        set(grouped.loc[i, 'source_sentence'].split(' | ')))
                )
                to_remove.add(i)

    grouped = grouped.drop(index=list(to_remove)).reset_index(drop=True)

    # Step 4: Drop singular/plural duplicates
    to_remove = set()
    for i, kw in enumerate(grouped['keyword']):
        singular = p.singular_noun(kw)
        plural = p.plural(kw)

        singular_str = singular if isinstance(singular, str) else kw
        plural_str = plural if isinstance(plural, str) else kw

        for j, other_kw in enumerate(grouped['keyword']):
            if i != j and (other_kw.lower() == plural_str.lower() or other_kw.lower() == singular_str.lower()):
                # Keep the longer term
                keep_idx = i if len(grouped.loc[i, 'keyword']) >= len(grouped.loc[j, 'keyword']) else j
                drop_idx = j if keep_idx == i else i

                grouped.loc[keep_idx, 'extractor'] = '|'.join(
                    sorted(set(grouped.loc[keep_idx, 'extractor'].split('|')) |
                        set(grouped.loc[drop_idx, 'extractor'].split('|')))
                )
                grouped.loc[keep_idx, 'source_sentence'] = ' | '.join(
                    sorted(set(grouped.loc[keep_idx, 'source_sentence'].split(' | ')) |
                        set(grouped.loc[drop_idx, 'source_sentence'].split(' | ')))
                )
                to_remove.add(drop_idx)

    grouped = grouped.drop(index=list(to_remove)).reset_index(drop=True)
    grouped = drop_rows_by_extractor_limit(grouped, limit=30)
    return grouped[['keyword', 'extractor', 'source_sentence']]



def drop_rows_by_extractor_limit(df, limit=30):
    """
    Drop rows based on extractor value priority until row count <= limit.

    Priority order:
    1. yake
    2. vocab_matches
    3. rakun_keywords
    4. keyphrase
    5. llama_keywords
    """
    drop_order = [
        "yake",
        "vocab_matches",
        "rakun_keywords",
        "keyphrase",
        "llama_keywords"
    ]
    
    df = df.copy()  # Avoid modifying original

    for extractor in drop_order:
        if len(df) <= limit:
            break
        # Drop rows where extractor exactly matches the given value
        mask = df['extractor'].str.strip() == extractor
        drop_count = min(len(df) - limit, mask.sum())
        if drop_count > 0:
            idx_to_drop = df[mask].head(drop_count).index
            df = df.drop(idx_to_drop)

    return df.reset_index(drop=True)


if __name__ == "__main__":

    start = time.time()
    section_texts = {
        "title": "Investigation of Electronic and Topological Properties of Magnesium-coated Boron Fullerenes and Their Interaction with Hydrogen Molecule",
        "abstract": "Various nanostructures have been widely investigated as alternative materials for hydrogen storage using experimental and computational techniques. In this research, adsorption, electronic, topological properties, and some molecular descriptors of magnesium-doped boron fullerenes and their interaction with H2 for hydrogen storage are investigated using density functional theory at B3LYP/6-31G//M062X/6-31G** theoretical level. Structures of B80, Mg12B80, Mg20B80 and Mg30B80 were optimized and their interaction with hydrogen molecule were analyzed. Results shows that charge transfer from Mg to B atoms is responsible for positive charge of Mg atoms. When hydrogen molecule approach to the system, it gets polarized and adsorbed to these boron fullerenes doped with Mg atoms. Also, it was found that Mg12B80 possesses the lowest energy gap (ΔEH-L), lowest hardness (η), and the highest adsorption energy, which indicates the reactivity and the hydrogen storage capability of this structure to adsorb hydrogen rather than B80, Mg20B80 and Mg30B80.",
        "results": "Fig. 1 shows optimized structures in which Mg atoms bind to the boron atoms in boron fullerene (B80). According to the structure presented by Szwacki et al. [31], when Mg atoms bind to the boron fullerene, charge transfer takes place from Mg atoms to B atoms. Tables 1 to 3 include amount of charge transfers for Mg12B80, Mg20B80 and Mg30B80 when Mg atoms bind to boron pentagonal and boron hexagonal rings, calculated in the context of Mulliken population analysis. This charge transfers make Mg atoms to become positively-charged, that leads to interaction with boron fullerene with negative charge. The binding energies for boron fullerene doped with Mg is calculated b. By using the energy of HOMO and LUMO orbitals, EH and EL, the values of ionization energy(IP), electron affinity (EA), electronegativity (χ), and hardness (ƞ) can be obtained:",
        "figure_caption": "Fig. 1. Optimized configurations of side view of a) B80, c) Mg12B80, e) Mg20B80 and g) Mg30B80. Optimized configurations of top view of b) B80, d) Mg12B80, f) Mg20B80 and h) Mg30B80. The blue ball for B, the green ball for Mg. The calculated binding energies are listed in Table 4. Our calculated results show that the average binding energy in the Mg12B80, Mg20B80 and Mg30B80 are -0.76, -1.13 and -0.35 eV/Mg, respectively. To investigate the interaction of the hydrogen molecule with the B80 coated with Mg, a hydrogen molecule is added to this system, that displaces the absorbed Mg atoms. The electric field due to the positively charged Mg atoms increases the polarizability of the H2 molecule which leads to adsorption of H2 molecule without dissociation.",
        "table_data": "",
        "table_caption": "Table 1. Mulliken charge distribution in the Mg and B atoms of the Mg12B80H2 and average charge transfer (Δq) from Mg to B atoms in pentagonal faces of B80 cage. Table 2. Mulliken charge distribution in the Mg and B atoms of the Mg20B80H2 and average charge transfer(Δq) from Mg to B atoms in hexagonal faces of the B80 cage. Table 3. Mulliken charge distribution in the Mg and B atoms of the Mg30B80H2 and average charge transfer (Δq)from Mg to B atoms in hexagonal faces of the B80 cage. Table 5. Hydrogen absorption energy (Ea) on Metal-coated Boron nanostructures calculated in previous papers and this research."
    }


    api_url = "http://172.27.7.85:5007/lamma_3_2_model"
    topic = "Electrochemical, Radiational, and Thermal Energy Technology"

    topic_information = """This topic covers chemical, biochemical, electrochemical, photochemical, and chemical engineering aspects of energy sources including geothermal, solar energy, ocean thermal energy, and other non-fossil fuel energy sources. Also included are studies of photoinduced redox reactions and artificial photosynthesis when used in solar energy conversion and storage and recovery and usage of waste heat. Hydrogen manufacture for fuel use by photolytic, biophotolytic, photoelectrochemical, and thermochemical processes if solar energy is involved as well as fuel manufacture from biomass are also covered in this topic. Additionally, energy conversion technology and energy conversion devices, including device components (batteries, fuel cells, solar cells, thermoelectric devices, etc) are also found in this topic. This topic also includes studies on chemical and chemical engineering aspects of handling, transport, and storage of thermal, solar, and geothermal energy, and of non-fossil fuels."""

    sample_keyphrases = [
        "Fuel cell", "Lithium secondary batteries", "Battery cathode",  "polymer electrolyte",
        "Secondary battery", "Battery anode", "Fluoropolymer",  "Solid oxide fuel cell",  "Carbon black",  "Solar cell", "Battery electrode","Ionomer", "Polyoxyalkylenes",
        "Electric current-potential relationship", "Fuel cell anode", "Polymer electrolyte", 
    ]

    df = extract_keywords_with_sources(section_texts, topic, topic_information, sample_keyphrases)


    print(df.head()) 
    df.to_csv("output/extracted_terms.csv", index=False)
    print(df.shape)
    print(f"\nResults saved to: output/extracted_terms.csv")
    cleaned_df = merge_and_deduplicate_keywords(df)
    print(cleaned_df.shape)
    output_path = "output/source_extracted_terms.csv"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    cleaned_df.to_csv(output_path, index=False)
    print(f"\nResults saved to: {output_path}")
    print(f"Total time required {round(time.time() - start, 2)} seconds..")
