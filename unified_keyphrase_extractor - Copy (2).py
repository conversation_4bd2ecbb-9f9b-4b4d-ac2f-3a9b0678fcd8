##unified_keyphrase_extractor.py
"""
Unified Keyphrase Extractor Module
Integrates all available extractors: YAKE, KeyBERT, Rakun, LLaMA, Keyphrase, and Vocab Matcher
Returns a comprehensive DataFrame with keywords from all sources.
"""

import os
import time
import yaml
import pandas as pd
import ast
import numpy as np
import re
import inflect
from typing import Dict, List, Optional, Union
import warnings
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import AffinityPropagation
warnings.filterwarnings("ignore", category=FutureWarning, module="torch.nn.modules.module")


# Import all extractors from src
from src.extractors.vocab_matcher import matched_vocab_terms
from src.extractors.yake_extractor import YakeExtractor
from src.extractors.keyphrase_extractor_v2 import KeyphraseExtractor
from src.extractors.rakun_extractor import RakunExtractor
from src.extractors.position_rank_extractor import PositionRankExtractor
from src.extractors.suma_extractor import SumaKeyphraseExtractor

# Import unique cluster phrases function
from get_unique_clusters import add_unique_cluster_phrases
from src.extractors.multipartite_extractor import MultipartiteRankExtractor
from src.extractors.tfidf_extractor import TFIDFExtractor
from src.extractors.rake_extractor import RakeExtractor
from src.extractors.llama_extractor_chunked import (
    extract_llama_keyphrases_from_segments,
    extract_llama_keyphrases_with_semantic_filtering,
    clean_and_form_sentences
)

# # Try to import KeyBERT if available
# try:
#     from src.extractors.keybert_extractor import KeyBertExtractor
#     KEYBERT_AVAILABLE = True
# except ImportError:
#     KEYBERT_AVAILABLE = False
#     print("KeyBERT extractor not available")


# Default configuration
DEFAULT_CONFIG = {
    "extractors": {
        "llama": {
            "api_url": "http://***********:5007/lamma_3_2_model",
            "topic_name": "Energy coversion, Energy devices, Energy handling and safety",
            "topic_info":  """This topic covers thermal, chemical, biochemical, electrochemical, photochemical, and engineering aspects of energy handling, transport, storage, and conversion, with a focus on non-fossil fuel sources and associated technologies. It includes heat storage and transport systems, such as sensible, latent, and thermochemical storage; solar energy collection, absorption, and pond systems; and heat pump technologies for the utilization of low-grade thermal energy sources. Hydrogen storage, distribution, and transport—both physical and materials-based—are also addressed.  

            Energy sources in scope include solar energy, ocean thermal energy, geothermal energy, waste heat recovery, and fuels derived from fermentation, gasification, or pyrolysis of biomass and wastes. Studies involving hydrogen as a fuel, combustion of non-fossil fuels, and the integration of waste heat into usable energy streams are also covered.  

            Energy conversion devices and technologies covered by this topic include batteries, fuel cells, solar cells, photoelectrochemical cells, solar thermophotovoltaic devices, thermoelectric devices, thermionic energy converters, magnetohydrodynamic and electrohydrodynamic generators, thermomagnetic energy converters, and other emerging energy conversion systems, including their components and materials.  

            The topic further encompasses safety aspects of energy utilization, including thermal system safety, hydrogen safety, battery and electrical safety, and process hazard mitigation for non-fossil fuel systems. Cross-cutting considerations such as system integration, durability, control systems, and environmental impact assessments are also included.""",

            "sample_keywords": ["Fuel cell", "Lithium secondary batteries", "polymer electrolyte","Secondary battery",  "Fluoropolymer", "Fuel cell electrolyte",  "Carbon black", "Solar cell","proton exchange membrane", "Ionomer", "Polyoxyalkylenes", "Electric current-potential relationship", "Polymer electrolyte" ],
            "max_chars": 1000
        },

        # YAKE - Unsupervised keyword extraction
        "yake": {
            "lan": "en",
            "max_keywords":10,
            "dedup_threshold": 0.9,
            "n": 2
        },
        # Keyphrase Extractor - HuggingFace model-based
        "keyphrase": {
            "model_path": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\keyphrase_model", 
            "top_n": 10,
            "score_threshold": 0.5
        },
        # Rakun - Graph-based keyword extraction
        "rakun": {
            "top_n": 10,
            "merge_threshold": 1.1,
            "alpha": 0.3,
            "token_prune_len": 3
        },
        # KeyBERT - BERT-based semantic keyword extraction
        "keybert": {
            "model_path": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-MiniLM-L6-v2",
            "top_n": 10
        },
        # PositionRank - Unsupervised keyword extraction
        "position_rank": {
            "top_n": 10,
            "spacy_model": "en_core_web_sm",
            "min_word_count": 2,
            "min_char_len": 3
        },
        # TextRank - Unsupervised keyword extraction
        "textrank": {
            "top_n": 10
        },
        # Vocabulary Matcher - Match against curated vocabulary
        "vocab_matcher": {
            "threshold": 0.8,
            "embedding_model": {
                "model_path": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-mpnet-base-v2"
            }
        },

        # SummaTextRank - TextRank-based keyword extraction
        "suma":{"top_n": 10 },

        # TF-IDF - Term Frequency-Inverse Document Frequency
        "tfidf": {"top_n": 10 },
    
        # MultipartiteRank - Unsupervised keyword extraction
        "multipartiterank":  {"top_n": 10}
    },

    "paths": {
        "embedding_store": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\\embeddings\\vocab_embeddings.pkl"
        # "vocab_matcher": {
        #     "threshold": 0.9
        # }
    }
}


def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file with fallback to default."""
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"Config file not found: {config_path}. Using default configuration.")
        return DEFAULT_CONFIG


class UnifiedKeyphraseExtractor:
    """
    Unified keyphrase extractor that combines multiple extraction methods.
    """
    def __init__(self, config: Optional[Union[str, Dict]] = None):
        """
        Initialize the unified extractor with configuration.

        Args:
            config: Configuration dictionary or path to config file
        """
        if isinstance(config, str):
            self.config = load_config(config)
        elif config is None:
            self.config = DEFAULT_CONFIG
        else:
            self.config = config

        self.extractors = {}
        self._initialize_extractors()

        
    def filter_sentences_by_semantic_similarity(
        self,
        title: str,
        abstract: str,
        segments: Dict[str, str],
        topic_name: Optional[str] = None,
        topic_info: Optional[str] = None,
        # model_name: str = "all-MiniLM-L6-v2",
        model_name: str = "allenai/specter",
        similarity_threshold: float = 0.75,
        top_k_per_segment: int = 10
    ) -> Dict[str, List[str]]:
        """
        Filter sentences by semantic similarity using the check_semantic_sentences module.
        Maintains the same output format as the original method.
        """

        print(f" Using check_semantic_sentences module for semantic filtering")
        # print(f"  Similarity threshold: {similarity_threshold}")
        # print(f"  Top-k per segment: {top_k_per_segment}")

        try:
            # Import the external module
            from src.extractors.check_semantic_sentences import extract_relevant_sentences

            # Create reference text from title, abstract, and topic information
            reference_parts = []
            if title:
                reference_parts.append(title.strip())
            if abstract:
                reference_parts.append(abstract.strip())
            if topic_name:
                reference_parts.append(topic_name.strip())
            if topic_info:
                reference_parts.append(topic_info.strip())

            # Use title and abstract as the main reference (matching the external module's expected input)
            query_title = title if title else ""
            query_abstract = abstract if abstract else ""

            # If we have topic information, append it to the abstract
            if topic_name or topic_info:
                topic_text = " ".join([topic_name or "", topic_info or ""]).strip()
                if topic_text:
                    query_abstract = f"{query_abstract} {topic_text}".strip()

            filtered_segments = {}

            for segment_name, segment_text in segments.items():
                # Skip title and abstract as they are already used as reference
                if segment_name.lower().strip() in ['title', 'abstract']:
                    continue

                # Clean the segment text
                cleaned_text = clean_and_form_sentences(segment_text)

                if not cleaned_text:
                    filtered_segments[segment_name] = []
                    continue

                # Use the external module to extract relevant sentences
                try:
                    # The external module returns a list of (sentence, similarity_score) tuples
                    relevant_results = extract_relevant_sentences(
                        title=query_title,
                        abstract=query_abstract,
                        text=cleaned_text,
                        threshold=similarity_threshold
                    )

                    # Extract just the sentences and limit to top_k
                    relevant_sentences = [sentence for sentence, score in relevant_results[:top_k_per_segment]]

                    filtered_segments[segment_name] = relevant_sentences

                    # Calculate total sentences for logging (approximate)
                    import nltk
                    try:
                        total_sentences = len(nltk.sent_tokenize(cleaned_text))
                    except:
                        total_sentences = len(cleaned_text.split('.'))

                    print(f"  {segment_name}: {total_sentences} → {len(relevant_sentences)} sentences (threshold: {similarity_threshold})")

                except Exception as e:
                    # print(f"    Error processing {segment_name} with external module: {e}")
                    # Fallback to basic filtering for this segment
                    sentences = self._split_into_sentences(cleaned_text)
                    filtered_segments[segment_name] = sentences[:top_k_per_segment]

            return filtered_segments

        except ImportError as e:
            print(f"    Could not import check_semantic_sentences module: {e}")
            print("   Falling back to original semantic filtering method...")
            return self._fallback_sentence_filtering(segments, top_k_per_segment)

        except Exception as e:
            print(f"   Error using external semantic filtering module: {e}")
            print("   Falling back to basic filtering...")
            return self._fallback_sentence_filtering(segments, top_k_per_segment)



    def _split_into_sentences(self, text: str) -> List[str]:
        """Split cleaned text into sentences using regex."""
        cleaned_text = clean_and_form_sentences(text)

        if not cleaned_text:
            return []

        sentences = re.split(r'(?<=[.!?])\s+', cleaned_text)

        valid_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if (sentence and len(sentence) > 10 and
                re.search(r'[a-zA-Z]', sentence) and
                not sentence.lower().startswith(('fig.', 'table', 'eq.', 'section'))):
                valid_sentences.append(sentence)

        return valid_sentences

    def _fallback_sentence_filtering(self, segments: Dict[str, str], top_k: int) -> Dict[str, List[str]]:
        """Fallback filtering when semantic model is not available."""
        filtered = {}
        for segment_name, segment_text in segments.items():
            if segment_name.lower().strip() not in ['title', 'abstract']:
                sentences = self._split_into_sentences(segment_text)
                filtered[segment_name] = sentences[:top_k]
        return filtered

    def get_cleaned_and_filtered_text_for_extractors(
        self,
        title: str,
        abstract: str,
        segments: Dict[str, str],
        topic_name: Optional[str] = None,
        topic_info: Optional[str] = None,
        model_name: str = "all-MiniLM-L6-v2",
        similarity_threshold: float = 0.75,
        top_k_per_segment: int = 10,
        include_original_title_abstract: bool = True
    ) -> Dict[str, str]:
        """
        Get cleaned and semantically filtered text suitable for other extractors.
        """
        # Get filtered sentences
        filtered_segments = self.filter_sentences_by_semantic_similarity(
            title=title,
            abstract=abstract,
            segments=segments,
            topic_name=topic_name,
            topic_info=topic_info,
            model_name=model_name,
            similarity_threshold=similarity_threshold,
            top_k_per_segment=top_k_per_segment
        )

        # Convert back to text format for other extractors
        cleaned_segments = {}

        # Include title and abstract if requested
        if include_original_title_abstract:
            if title:
                cleaned_segments['title'] = clean_and_form_sentences(title)
            if abstract:
                cleaned_segments['abstract'] = clean_and_form_sentences(abstract)

        # Add filtered content from other segments
        for segment_name, filtered_sentences in filtered_segments.items():
            if filtered_sentences:
                cleaned_text = ' '.join(filtered_sentences)
                cleaned_segments[f"{segment_name}_filtered"] = cleaned_text
        # print(f"{cleaned_segments =}")
        return cleaned_segments


    
    def _initialize_extractors(self):
        """Initialize all available extractors."""
        try:
            # YAKE Extractor
            self.extractors['yake'] = YakeExtractor(**self.config["extractors"]["yake"])
            print("YAKE extractor initialized")
            
            # Keyphrase Extractor
            self.extractors['keyphrase'] = KeyphraseExtractor(**self.config["extractors"]["keyphrase"])
            print("Keyphrase extractor initialized")
            
            # Rakun Extractor
            self.extractors['rakun'] = RakunExtractor(**self.config["extractors"]["rakun"])
            print("Rakun extractor initialized")

            # LLaMA Extractor
            self.extractors['llama'] = extract_llama_keyphrases_with_semantic_filtering
            print("LLaMA extractor initialized")

            # Vocabulary Matcher Extractor
            self.extractors['vocab_matcher'] = matched_vocab_terms
            print("Vocabulary matcher initialized")


            # Suma Extractor
            if "suma" in self.config["extractors"]:
                # print(f"Suma extractor present in config {self.config['extractors']}")
                try:
                    self.extractors['suma'] = SumaKeyphraseExtractor(**self.config["extractors"]["suma"])
                    print("Suma extractor initialized")
                except Exception as e:
                    print(f" Suma extractor failed to initialize: {e}")


            # RAKE Extractor
            if "rake" in self.config["extractors"]:
                # print(f"RAKE extractor present in config {self.config['extractors']}")
                try:
                    self.extractors['rake'] = RakeExtractor(**self.config["extractors"]["rake"])
                    print("RAKE extractor initialized")
                except Exception as e:
                    print(f" RAKE extractor failed to initialize: {e}")
            

            # # KeyBERT Extractor
            # self.extractors['keybert'] = KeyBertExtractor(**self.config["extractors"]["keybert"])
            # print("KeyBERT extractor initialized")

            # # TextRank Extractor
            # self.extractors['textrank'] = TextRankExtractor(**self.config["extractors"]["textrank"])
            # print("TextRank extractor initialized")

            # MultipartiteRank Extractor
            if "multipartiterank" in self.config["extractors"]:
                # print(f"multipartiterank extractor present in config {self.config['extractors']}")
                try:
                    self.extractors['multipartiterank'] = MultipartiteRankExtractor(**self.config["extractors"]["multipartiterank"])
                    print("MultipartiteRank extractor initialized")
                except Exception as e:
                    print(f" MultipartiteRank extractor failed to initialize: {e}")

            # # TF-IDF Extractor
            # if "tfidf" in self.config["extractors"]:
            #     # print(f"tfidf extractor present in config {self.config['extractors']}")
            #     try:
            #         self.extractors['tfidf'] = TFIDFExtractor(**self.config["extractors"]["tfidf"])
            #         print("TF-IDF extractor initialized")
            #     except Exception as e:
            #         print(f" TF-IDF extractor failed to initialize: {e}")   


            # # KeyBERT Extractor (if available)
            # if KEYBERT_AVAILABLE and "keybert" in self.config["extractors"]:
            #     try:
            #         self.extractors['keybert'] = KeyBertExtractor(**self.config["extractors"]["keybert"])
            #         print(" KeyBERT extractor initialized")
            #     except Exception as e:
            #         print(f" KeyBERT extractor failed to initialize: {e}")

            # PositionRank Extractor
            if "position_rank" in self.config["extractors"]:
                try:
                    self.extractors['position_rank'] = PositionRankExtractor(**self.config["extractors"]["position_rank"])
                    print(" PositionRank extractor initialized")
                except Exception as e:
                    print(f" PositionRank extractor failed to initialize: {e}")

            # TextRank Extractor
            if "textrank" in self.config["extractors"]:
                try:
                    self.extractors['textrank'] = SumaKeyphraseExtractor(**self.config["extractors"]["textrank"])
                    print(" TextRank extractor initialized")
                except Exception as e:
                    print(f" TextRank extractor failed to initialize: {e}")
            # MultipartiteRank Extractor
            if "multipartiterank" in self.config["extractors"]:
                try:
                    self.extractors['textrank'] = MultipartiteRankExtractor(**self.config["extractors"]["multipartiterank"])
                    print(" MultipartiteRank extractor initialized")
                except Exception as e:
                    print(f" MultipartiteRank extractor failed to initialize: {e}")

            ## TF-IDF Extractor
            if "tfidf" in self.config["extractors"]:
                try:
                    self.extractors['tfidf'] = TFIDFExtractor(**self.config["extractors"]["tfidf"])
                    print(" TF-IDF extractor initialized")
                except Exception as e:
                    print(f" TF-IDF extractor failed to initialize: {e}")

            # # Suma Extractor
            # if "suma" in self.config["extractors"]:
            #     try:
            #         self.extractors['suma'] = SumaKeyphraseExtractor(**self.config["extractors"]["suma"])
            #         print(" Suma extractor initialized")
            #     except Exception as e:
            #         print(f" Suma extractor failed to initialize: {e}")

        except Exception as e:
            print(f" Error initializing extractors: {e}")
    

    def extract_from_text(self, text: str, section_texts: Optional[Dict[str, str]] = None,
                        use_cleaned_text: bool = True) -> pd.DataFrame:
        """
        Extract keyphrases from text using all available extractors.

        Args:
            text: Raw text to extract keyphrases from
            section_texts: Dictionary of section texts for LLaMA extractor
            use_cleaned_text: Whether to use cleaned and filtered text for other extractors

        Returns:
            DataFrame with columns: ['extractor', 'keyword', 'source_sentence', 'confidence']
        """
        # Determine which text to use for extraction
        extraction_text = text
        extraction_sentences = [s.strip() for s in text.split('. ') if s.strip()]

        # If we have section texts and want to use cleaned text, get filtered content
        if use_cleaned_text and section_texts:
            title = section_texts.get('title', '') or section_texts.get('Title', '')
            abstract = section_texts.get('abstract', '') or section_texts.get('Abstract', '')

            if title or abstract:
                try:
                    # Get topic information from config if available
                    topic_name = self.config.get("extractors", {}).get("llama", {}).get("topic_name", "")
                    topic_info = self.config.get("extractors", {}).get("llama", {}).get("topic_info", "")

                    cleaned_segments = self.get_cleaned_and_filtered_text_for_extractors(
                        title=title,
                        abstract=abstract,
                        segments=section_texts,
                        topic_name=topic_name,
                        topic_info=topic_info,
                        similarity_threshold=0.75,
                        top_k_per_segment=10
                    )

                    # Combine cleaned segments for extraction
                    cleaned_texts = []
                    for segment_name, segment_text in cleaned_segments.items():
                        cleaned_texts.append(segment_text)

                    if cleaned_texts:
                        extraction_text = ' '.join(cleaned_texts)
                        extraction_sentences = [s.strip() for s in extraction_text.split('. ') if s.strip()]
                        print(f" Using cleaned and filtered text ({len(extraction_text)} chars)")

                except Exception as e:
                    print(f"  Could not use cleaned text, falling back to original: {e}")

        results = []
        print(" Starting unified keyphrase extraction...")
        
        # 1. YAKE Extraction
        if 'yake' in self.extractors:
            # print(" Extracting with YAKE...")
            yake_keywords = set(self.extractors['yake'].extract(extraction_text))
            results.extend(self._map_keywords_to_sentences(yake_keywords, extraction_sentences, 'yake'))

        # 2. Keyphrase Extraction
        if 'keyphrase' in self.extractors:
            # print(" Extracting with Keyphrase model...")
            keyphrase_keywords = set(self.extractors['keyphrase'].extract(extraction_text))
            results.extend(self._map_keywords_to_sentences(keyphrase_keywords, extraction_sentences, 'keyphrase'))

        # 3. Rakun Extraction
        if 'rakun' in self.extractors:
            # print(" Extracting with Rakun...")
            rakun_keywords = set(self.extractors['rakun'].extract(extraction_text))
            results.extend(self._map_keywords_to_sentences(rakun_keywords, extraction_sentences, 'rakun'))

        # 4. KeyBERT Extraction
        if 'keybert' in self.extractors:
            # print(" Extracting with KeyBERT...")
            try:
                keybert_keywords = set(self.extractors['keybert'].extract(extraction_text))
                results.extend(self._map_keywords_to_sentences(keybert_keywords, extraction_sentences, 'keybert'))
            except Exception as e:
                print(f"  KeyBERT extraction failed: {e}")

        # # 5. PositionRank Extraction
        # if 'position_rank' in self.extractors:
        #     # print(" Extracting with PositionRank...")
        #     try:
        #         position_rank_keywords = set(self.extractors['position_rank'].extract(extraction_text))
        #         results.extend(self._map_keywords_to_sentences(position_rank_keywords, extraction_sentences, 'position_rank'))
        #     except Exception as e: 
        #         print(f"  PositionRank extraction failed: {e}")

        # 6. TextRank Extraction
        if 'textrank' in self.extractors:
            # print(" Extracting with TextRank...")
            try:
                textrank_keywords = set(self.extractors['textrank'].extract(extraction_text))
                results.extend(self._map_keywords_to_sentences(textrank_keywords, extraction_sentences, 'textrank'))
            except Exception as e:
                print(f"  TextRank extraction failed: {e}")

        #7 TF-IDF Extraction
        if 'tfidf' in self.extractors:
            # print(" Extracting with TF-IDF...")
            try:
                tfidf_keywords = set(self.extractors['tfidf'].extract(extraction_text))
                results.extend(self._map_keywords_to_sentences(tfidf_keywords, extraction_sentences, 'tfidf'))
            except Exception as e:
                print(f"  TF-IDF extraction failed: {e}")

        # 8. MultipartiteRank Extraction
        if 'multipartiterank' in self.extractors:
            # print(" Extracting with MultipartiteRank...")
            try:
                multipartiterank_keywords = set(self.extractors['multipartiterank'].extract(extraction_text))
                results.extend(self._map_keywords_to_sentences(multipartiterank_keywords, extraction_sentences, 'multipartiterank'))
                print(f"  MultipartiteRank found keywords: {multipartiterank_keywords}")
            except Exception as e:
                print(f"  MultipartiteRank extraction failed: {e}")

        # 9. Suma Extraction
        if 'suma' in self.extractors:
            # print(" Extracting with Suma...")
            try:
                suma_keywords = set(self.extractors['suma'].extract(extraction_text))
                print(f"  Suma found keywords: {suma_keywords}")
                results.extend(self._map_keywords_to_sentences(suma_keywords, extraction_sentences, 'suma'))
            except Exception as e:
                print(f"  Suma extraction failed: {e}")



        # 10. LLaMA Extraction (if section_texts provided)
        if section_texts:
            print(" Extracting with LLaMA...")
            llama_keywords = self._extract_llama_keywords(section_texts)
            results.extend(self._map_keywords_to_sentences(llama_keywords, extraction_sentences, 'llama'))

        # 11. Vocab Matching
        print(" Matching with vocabulary...")
        vocab_keywords = self._extract_vocab_keywords(extraction_text)
        # print(f"  Vocab matching found >>>> {vocab_keywords}")
        results.extend(self._map_keywords_to_sentences(vocab_keywords, extraction_sentences, 'vocab_matcher'))
        
        # Create DataFrame
        df = pd.DataFrame(results)
        
        if not df.empty:
            # Add confidence scores (placeholder - can be enhanced)
            df['confidence'] = 1.0  # Default confidence
            
            # Remove duplicates and sort
            df = df.drop_duplicates(subset=['keyword', 'extractor']).reset_index(drop=True)
            df = df.sort_values(['extractor', 'keyword']).reset_index(drop=True)
        
        print(f"Extraction complete! Found {len(df)} keyword-sentence pairs from {df['extractor'].nunique() if not df.empty else 0} extractors")
        return df

    def extract_and_deduplicate(self, text: str, section_texts: Optional[Dict[str, str]] = None,
                            use_cleaned_text: bool = True, apply_deduplication: bool = True) -> pd.DataFrame:
        """
        Extract keyphrases and apply advanced deduplication.

        Args:
            text: Raw text to extract keyphrases from
            section_texts: Dictionary of section texts for LLaMA extractor
            use_cleaned_text: Whether to use cleaned and filtered text for other extractors
            apply_deduplication: Whether to apply the enhanced deduplication process

        Returns:
            DataFrame with deduplicated keywords and enhanced metrics
        """
        # First, extract keywords using the standard method
        df = self.extract_from_text(text, section_texts, use_cleaned_text)

        if df.empty:
            return df

        if apply_deduplication:
            print(f"\n Applying enhanced deduplication...")
            # Apply the enhanced deduplication function
            deduplicated_df = merge_and_deduplicate_keywords(df)

            print(f" DEDUPLICATION SUMMARY:")
            print(f"  Original keywords: {len(df)}")
            print(f"  After deduplication: {len(deduplicated_df)}")
            print(f"  Reduction: {len(df) - len(deduplicated_df)} ({((len(df) - len(deduplicated_df)) / len(df) * 100):.1f}%)")

            return deduplicated_df
        else:
            return df
    
    def _map_keywords_to_sentences(self, keywords: set, sentences: List[str], extractor_name: str) -> List[Dict]:
        """Map keywords to their source sentences."""
        results = []
        for sentence in sentences:
            for keyword in keywords:
                if keyword and keyword.lower() in sentence.lower():
                    results.append({
                        'extractor': extractor_name,
                        'keyword': keyword,
                        'source_sentence': sentence
                    })
        return results
    
    def _extract_llama_keywords(self, section_texts: Dict[str, str]) -> set:
        """Extract keywords using enhanced LLaMA model with semantic filtering."""
        try:
            llama_config = self.config["extractors"]["llama"]

            # Get title and abstract for semantic filtering
            title = section_texts.get('title', '') or section_texts.get('Title', '')
            abstract = section_texts.get('abstract', '') or section_texts.get('Abstract', '')

            # Apply semantic filtering using unified extractor's method
            if title or abstract:
                print(" Applying semantic filtering for LLaMA extraction...")
                filtered_segments = self.filter_sentences_by_semantic_similarity(
                    title=title,
                    abstract=abstract,
                    segments=section_texts,
                    topic_name=llama_config["topic_name"],
                    topic_info=llama_config["topic_info"],
                    similarity_threshold=llama_config.get("similarity_threshold", 0.75),
                    top_k_per_segment=llama_config.get("top_k_sentences", 10),
                    model_name=llama_config.get("model_name", "all-MiniLM-L6-v2")
                )

                # Prepare filtered segments for LLaMA
                llama_segments = {}
                if title:
                    llama_segments['title'] = title
                if abstract:
                    llama_segments['abstract'] = abstract

                # Add filtered content
                for segment_name, filtered_sentences in filtered_segments.items():
                    if filtered_sentences:
                        combined_text = ' '.join(filtered_sentences)
                        llama_segments[f"{segment_name}_filtered"] = combined_text

                print(f" Sending {len(llama_segments)} segments to LLaMA")
            else:
                llama_segments = section_texts

            # Use basic LLaMA extraction with filtered segments
            llama_df = extract_llama_keyphrases_from_segments(
                api_url=llama_config["api_url"],
                segments=llama_segments,
                topic_name=llama_config["topic_name"],
                topic_info=llama_config["topic_info"],
                sample_keywords=llama_config["sample_keywords"],
                max_chars=llama_config["max_chars"]
            )
            
            llama_keywords = set()
            for _, row in llama_df.iterrows():
                if isinstance(row['keyphrases'], list):
                    llama_keywords.update(row['keyphrases'])
                elif isinstance(row['keyphrases'], str):
                    try:
                        keyphrases_list = ast.literal_eval(row['keyphrases'])
                        if isinstance(keyphrases_list, list):
                            llama_keywords.update(keyphrases_list)
                    except:
                        pass
            
            return llama_keywords
            
        except Exception as e:
            print(f" LLaMA extraction failed: {e}")
            return set()
    
    # def _extract_vocab_keywords(self, text: str) -> set:
    #     """Extract keywords using vocabulary matching."""
    #     try:
    #         vocab_config = self.config["extractors"]["vocab_matcher"]['embedding_model']
    #         print(f"{vocab_config =}")
    #         vocab_matches = matched_vocab_terms(text, self.config, threshold=vocab_config["threshold"])
    #         print(f"  Vocab matching found >>>> {vocab_matches}")
    #         # Handle both DataFrame and list returns from vocab_matcher
    #         if isinstance(vocab_matches, pd.DataFrame):
    #             if not vocab_matches.empty and 'matched_term' in vocab_matches.columns:
    #                 return set(vocab_matches['matched_term'].tolist())
    #         elif isinstance(vocab_matches, list):
    #             return set(vocab_matches)

    #         return set()

    #     except Exception as e:
    #         print(f"  Vocab matching failed: {e}")
    #         return set()
# DEFAULT_CONFIG = {
#     "extractors": {
#  "vocab_matcher": {
#             "threshold": 0.9,
#             "embedding_model": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\models\\paraphrase-MiniLM-L6-v2",
#             "embedding_store": "C:\\Users\\<USER>\\Desktop\\Anand\\chemindexing_2025\\data\\embeddings\\vocab_embeddings.pkl"
#         }


    def _extract_vocab_keywords(self, text: str) -> set:
        """Extract keywords using vocabulary matching."""
        try:
            vocab_config = self.config["extractors"]["vocab_matcher"]

            # Get embedding store path from the main config structure
            embedding_store_path = self.config.get("paths", {}).get("embedding_store", "vocab_embeddings.pkl")
            # print(f"embedding_store_path = '{embedding_store_path}'")

            if not os.path.exists(embedding_store_path):
                print(f" Embedding store not found: {embedding_store_path}")
                return set()

            # Get model path from the nested structure (matching working config)
            model_path = vocab_config.get("embedding_model", {}).get("model_path", "")
            # print(f"model_path = '{model_path}'")

            # Use the exact same config structure as the working vocab_matcher.py
            formatted_config = {
                "extractors": {
                    "vocab_matcher": {
                        "embedding_model": {
                            "model_path": model_path
                        },
                        "threshold": vocab_config.get("threshold", 0.90)
                    }
                },
                "paths": {
                    "embedding_store": embedding_store_path
                }
            }

            print(f"   Using model: {model_path}")
            print(f"   Using embedding store: {embedding_store_path}")

            vocab_matches = matched_vocab_terms(text, formatted_config, threshold=vocab_config["threshold"])
            print(f"  Vocab matching found >>>> {vocab_matches}")

            # Handle the return value (should be a list)
            if isinstance(vocab_matches, list):
                return set(vocab_matches)
            elif isinstance(vocab_matches, pd.DataFrame):
                if not vocab_matches.empty and 'matched_term' in vocab_matches.columns:
                    return set(vocab_matches['matched_term'].tolist())

            return set()

        except Exception as e:
            print(f"   Vocab matching failed: {e}")
            print(f"  Suggestion: Check if vocab_embeddings.pkl exists and model path is correct")
            return set()

    
    def get_summary_stats(self, df: pd.DataFrame) -> Dict:
        """Get summary statistics of extraction results."""
        if df.empty:
            return {"total_keywords": 0, "extractors_used": 0, "keywords_per_extractor": {}}

        stats = {
            "total_keywords": len(df),
            "unique_keywords": df['keyword'].nunique(),
            "extractors_used": df['extractor'].nunique(),
            "keywords_per_extractor": df['extractor'].value_counts().to_dict(),
            "avg_keywords_per_sentence": len(df) / df['source_sentence'].nunique() if df['source_sentence'].nunique() > 0 else 0
        }

        return stats

    def get_top_keywords_by_frequency(self, df: pd.DataFrame, top_n: int = 5) -> pd.DataFrame:
        """Get top keywords by frequency across all extractors."""
        if df.empty:
            return pd.DataFrame(columns=['keyword', 'frequency', 'extractors'])

        keyword_stats = df.groupby('keyword').agg({
            'extractor': ['count', lambda x: ', '.join(sorted(set(x)))],
            'source_sentence': 'nunique'
        }).reset_index()

        keyword_stats.columns = ['keyword', 'frequency', 'extractors', 'sentence_count']
        keyword_stats = keyword_stats.sort_values('frequency', ascending=False).head(top_n)

        return keyword_stats

    def filter_by_relevance(self, df: pd.DataFrame, title: str = "", abstract: str = "",
                        min_frequency: int = 1, min_length: int = 3) -> pd.DataFrame:
        """Filter keywords by relevance criteria."""
        if df.empty:
            return df

        # Basic filtering
        filtered_df = df[
            (df['keyword'].str.len() >= min_length) &
            (df['keyword'].str.strip() != "") &
            (~df['keyword'].str.lower().isin(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']))
        ].copy()

        # Frequency filtering
        keyword_counts = filtered_df['keyword'].value_counts()
        frequent_keywords = keyword_counts[keyword_counts >= min_frequency].index
        filtered_df = filtered_df[filtered_df['keyword'].isin(frequent_keywords)]

        return filtered_df

# Convenience functions for direct usage
def extract_unified_keyphrases(text: str, section_texts: Optional[Dict[str, str]] = None,
                            config: Optional[Union[str, Dict]] = None) -> pd.DataFrame:
    """
    Convenience function to extract keyphrases using all available extractors.

    Args:
        text: Raw text to extract keyphrases from
        section_texts: Dictionary of section texts for LLaMA extractor
        config: Configuration dictionary or path to config file

    Returns:
        DataFrame with extracted keyphrases from all extractors
    """
    extractor = UnifiedKeyphraseExtractor(config)
    return extractor.extract_from_text(text, section_texts)



def extract_keywords_with_sources_unified(raw_text: str, section_texts: Optional[Dict[str, str]] = None,
                                        config: Optional[Union[str, Dict]] = None,
                                        filter_results: bool = True,
                                        use_deduplication: bool = True) -> pd.DataFrame:
    """
    Enhanced version that matches main_source.py output format with additional filtering.

    Args:
        raw_text: Raw text to extract keyphrases from
        section_texts: Dictionary of section texts for LLaMA extractor
        config: Configuration dictionary or path to config file
        filter_results: Whether to apply relevance filtering
        use_deduplication: Whether to apply merge_and_deduplicate_keywords function

    Returns:
        DataFrame with columns: ['extractor', 'keyword', 'source_sentence', 'confidence']
        Similar to main_source.py but with all extractors integrated
    """
    extractor = UnifiedKeyphraseExtractor(config)
    df = extractor.extract_from_text(raw_text, section_texts)

    if filter_results and not df.empty:
        # Apply basic filtering
        df = extractor.filter_by_relevance(df, min_frequency=1, min_length=3)

        # # Remove very common words that might slip through
        # stop_words = {'study', 'analysis', 'method', 'result', 'conclusion', 'research',
        #             'data', 'using', 'based', 'approach', 'technique', 'process'}
        # df = df[~df['keyword'].str.lower().isin(stop_words)]

    # Apply deduplication and noise removal to prioritize keywords selected by multiple extractors
    if use_deduplication and not df.empty:
        df = merge_and_deduplicate_keywords(df)

    return df



def extract_keyphrases_structured_output(raw_text: str, section_texts: Optional[Dict[str, str]] = None,
                                    config: Optional[Union[str, Dict]] = None,
                                    filter_results: bool = True,
                                    use_deduplication: bool = True) -> pd.DataFrame:
    """
    Extract keyphrases and return in structured format with sentence-level grouping.

    Args:
        raw_text: Raw text to extract keyphrases from
        section_texts: Dictionary of section texts for LLaMA extractor
        config: Configuration dictionary or path to config file
        filter_results: Whether to apply relevance filtering
        use_deduplication: Whether to apply merge_and_deduplicate_keywords function

    Returns:
        DataFrame with columns: ['sentence', 'keyphrase_list', 'extractor_list', 'input_segment', 'keyphrase_count']
    """
    # Get the deduplicated keywords
    df = extract_keywords_with_sources_unified(
        raw_text, section_texts, config, filter_results, use_deduplication
    )

    if df.empty:
        return pd.DataFrame(columns=['sentence', 'keyphrase_list', 'extractor_list', 'input_segment', 'keyphrase_count'])

    # Determine input segment for each sentence
    def get_input_segment(sentence):
        if section_texts:
            for segment_name, segment_text in section_texts.items():
                if sentence in segment_text:
                    return segment_name
        return "main_text"

    # Group by sentence and aggregate keyphrases and extractors
    sentence_groups = []

    # Handle the case where source_sentence might contain multiple sentences separated by ' | '
    for _, row in df.iterrows():
        sentences = row['source_sentence'].split(' | ')
        extractors = row['extractor'].split('|')

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                sentence_groups.append({
                    'sentence': sentence,
                    'keyword': row['keyword'],
                    'extractor': '|'.join(extractors),
                    'input_segment': get_input_segment(sentence)
                })

    # Convert to DataFrame and group by sentence
    sentence_df = pd.DataFrame(sentence_groups)

    if sentence_df.empty:
        return pd.DataFrame(columns=['sentence', 'keyphrase_list', 'extractor_list', 'input_segment', 'keyphrase_count'])

    # Group by sentence and aggregate
    result = sentence_df.groupby(['sentence', 'input_segment']).agg({
        'keyword': lambda x: list(set(x)),  # Unique keyphrases per sentence
        'extractor': lambda x: '|'.join(sorted(set('|'.join(x).split('|'))))  # Unique extractors
    }).reset_index()

    # Rename columns to match requested format
    result.columns = ['sentence', 'input_segment', 'keyphrase_list', 'extractor_list']

    # Add keyphrase count
    result['keyphrase_count'] = result['keyphrase_list'].apply(len)

    # Reorder columns
    result = result[['sentence', 'keyphrase_list', 'extractor_list', 'input_segment', 'keyphrase_count']]

    # Sort by keyphrase count (descending) and then by sentence length
    result = result.sort_values(['keyphrase_count', 'sentence'], ascending=[False, True]).reset_index(drop=True)

    return result


import inflect

p = inflect.engine()

def merge_and_deduplicate_keywords_0(df):
    """
    Deduplicate and merge keywords:
    1. Drop partial matches (keep longer string)
    2. Drop singular/plural duplicates (keep longer)
    """
    # Step 1: Normalize
    df['keyword'] = df['keyword'].astype(str).str.strip()
    df['keyword_lower'] = df['keyword'].str.lower()

    # Step 2: Merge exact lowercase duplicates
    grouped = (
        df.groupby('keyword_lower')
        .agg({
            'keyword': lambda x: max(x, key=len),
            'extractor': lambda x: '|'.join(sorted(set('|'.join(x).split('|')))),
            'source_sentence': lambda x: ' | '.join(sorted(set(x)))
        })
        .reset_index(drop=True)
    )

    # Step 3: Drop partial matches (keep longer)
    to_remove = set()
    for i, kw in enumerate(grouped['keyword']):
        for j, other_kw in enumerate(grouped['keyword']):
            if i != j and kw.lower() in other_kw.lower() and len(other_kw) > len(kw):
                # Merge extractors/sentences into longer keyword
                grouped.loc[j, 'extractor'] = '|'.join(
                    sorted(set(grouped.loc[j, 'extractor'].split('|')) |
                        set(grouped.loc[i, 'extractor'].split('|')))
                )
                grouped.loc[j, 'source_sentence'] = ' | '.join(
                    sorted(set(grouped.loc[j, 'source_sentence'].split(' | ')) |
                        set(grouped.loc[i, 'source_sentence'].split(' | ')))
                )
                to_remove.add(i)

    grouped = grouped.drop(index=list(to_remove)).reset_index(drop=True)

    # Step 4: Drop singular/plural duplicates
    to_remove = set()
    for i, kw in enumerate(grouped['keyword']):
        singular = p.singular_noun(kw)
        plural = p.plural(kw)

        singular_str = singular if isinstance(singular, str) else kw
        plural_str = plural if isinstance(plural, str) else kw

        for j, other_kw in enumerate(grouped['keyword']):
            if i != j and (other_kw.lower() == plural_str.lower() or other_kw.lower() == singular_str.lower()):
                # Keep the longer term
                keep_idx = i if len(grouped.loc[i, 'keyword']) >= len(grouped.loc[j, 'keyword']) else j
                drop_idx = j if keep_idx == i else i

                grouped.loc[keep_idx, 'extractor'] = '|'.join(
                    sorted(set(grouped.loc[keep_idx, 'extractor'].split('|')) |
                        set(grouped.loc[drop_idx, 'extractor'].split('|')))
                )
                grouped.loc[keep_idx, 'source_sentence'] = ' | '.join(
                    sorted(set(grouped.loc[keep_idx, 'source_sentence'].split(' | ')) |
                        set(grouped.loc[drop_idx, 'source_sentence'].split(' | ')))
                )
                to_remove.add(drop_idx)

    grouped = grouped.drop(index=list(to_remove)).reset_index(drop=True)
    grouped = drop_rows_by_extractor_limit(grouped, limit=30)
    return grouped[['keyword', 'extractor', 'source_sentence']]



def drop_rows_by_extractor_limit(df, limit=30):
    """
    Drop rows based on extractor value priority until row count <= limit.

    Priority order:
    1. yake
    2. vocab_matches
    3. rakun_keywords
    4. keyphrase
    5. llama_keywords
    """
    drop_order = [
        "yake",
        "vocab_matches",
        "rakun_keywords",
        "keyphrase",
        "llama_keywords"
    ]
    
    df = df.copy()  # Avoid modifying original

    for extractor in drop_order:
        if len(df) <= limit:
            break
        # Drop rows where extractor exactly matches the given value
        mask = df['extractor'].str.strip() == extractor
        drop_count = min(len(df) - limit, mask.sum())
        if drop_count > 0:
            idx_to_drop = df[mask].head(drop_count).index
            df = df.drop(idx_to_drop)

    return df.reset_index(drop=True)


def analyze_keyphrase_consensus(df: pd.DataFrame) -> pd.DataFrame:
    """
    Analyze keyphrase consensus across different extractors.

    Args:
        df: DataFrame with columns ['extractor', 'keyword', 'source_sentence', 'confidence']

    Returns:
        DataFrame with consensus analysis including:
        - keyphrase: The keyphrase
        - extractor_count: Number of extractors that found this keyphrase
        - extractors: List of extractors that found this keyphrase
        - total_frequency: Total number of times this keyphrase was found
        - avg_confidence: Average confidence score
        - extractor_details: Detailed breakdown by extractor
    """
    if df.empty:
        return pd.DataFrame()

    # Group by keyphrase and analyze
    consensus_data = []

    for keyphrase in df['keyword'].unique():
        keyphrase_df = df[df['keyword'] == keyphrase]

        # Get extractors that found this keyphrase
        extractors = keyphrase_df['extractor'].unique().tolist()
        extractor_count = len(extractors)

        # Calculate statistics
        total_frequency = len(keyphrase_df)
        avg_confidence = keyphrase_df['confidence'].mean() if 'confidence' in keyphrase_df.columns else 0.0

        # Get detailed breakdown by extractor
        extractor_details = {}
        for extractor in extractors:
            extractor_instances = keyphrase_df[keyphrase_df['extractor'] == extractor]
            extractor_details[extractor] = {
                'frequency': len(extractor_instances),
                'avg_confidence': extractor_instances['confidence'].mean() if 'confidence' in extractor_instances.columns else 0.0
            }

        consensus_data.append({
            'keyphrase': keyphrase,
            'extractor_count': extractor_count,
            'extractors': extractors,
            'total_frequency': total_frequency,
            'avg_confidence': avg_confidence,
            'extractor_details': extractor_details
        })

    # Create DataFrame and sort by consensus level and frequency
    consensus_df = pd.DataFrame(consensus_data)
    consensus_df = consensus_df.sort_values(
        ['extractor_count', 'total_frequency'],
        ascending=[False, False]
    ).reset_index(drop=True)

    return consensus_df


def create_summary_report(df: pd.DataFrame, consensus_df: pd.DataFrame,
                        section_texts: dict, topic: str, topic_info: str):
    """
    Create a comprehensive summary report with multiple sheets.

    Args:
        df: Raw extraction results
        consensus_df: Consensus analysis results
        section_texts: Original section texts
        topic: Topic name
        topic_info: Topic information
    """
    with pd.ExcelWriter("extraction_summary_report.xlsx", engine='openpyxl') as writer:

        # Sheet 1: Executive Summary
        summary_data = {
            'Metric': [
                'Total Keyword Instances',
                'Unique Keywords',
                'Extractors Used',
                'High Consensus Keywords (2+ extractors)',
                'Medium Consensus Keywords (1 extractor)',
                'Average Keywords per Extractor',
                'Topic',
                'Sections Processed'
            ],
            'Value': [
                len(df),
                df['keyword'].nunique(),
                ', '.join(df['extractor'].unique()),
                len(consensus_df[consensus_df['extractor_count'] >= 2]),
                len(consensus_df[consensus_df['extractor_count'] == 1]),
                round(len(df) / df['extractor'].nunique(), 2),
                topic,
                ', '.join(section_texts.keys())
            ]
        }
        summary_sheet = pd.DataFrame(summary_data)
        summary_sheet.to_excel(writer, sheet_name='Executive_Summary', index=False)

        # Sheet 2: High Consensus Keywords (found by multiple extractors)
        high_consensus = consensus_df[consensus_df['extractor_count'] >= 2].copy()
        if not high_consensus.empty:
            # Flatten extractor details for better Excel display
            high_consensus_display = []
            for _, row in high_consensus.iterrows():
                extractor_info = []
                for extractor, details in row['extractor_details'].items():
                    extractor_info.append(f"{extractor}({details['frequency']})")

                high_consensus_display.append({
                    'Keyphrase': row['keyphrase'],
                    'Extractor_Count': row['extractor_count'],
                    'Total_Frequency': row['total_frequency'],
                    'Extractors': ', '.join(row['extractors']),
                    'Extractor_Details': ', '.join(extractor_info),
                    'Avg_Confidence': round(row['avg_confidence'], 3)
                })

            high_consensus_sheet = pd.DataFrame(high_consensus_display)
            high_consensus_sheet.to_excel(writer, sheet_name='High_Consensus_Keywords', index=False)

        # Sheet 3: All Keywords by Extractor
        extractor_summary = []
        for extractor in df['extractor'].unique():
            extractor_df = df[df['extractor'] == extractor]
            extractor_keywords = extractor_df['keyword'].value_counts()

            for keyword, frequency in extractor_keywords.items():
                extractor_summary.append({
                    'Extractor': extractor,
                    'Keyword': keyword,
                    'Frequency': frequency,
                    'Avg_Confidence': extractor_df[extractor_df['keyword'] == keyword]['confidence'].mean() if 'confidence' in extractor_df.columns else 0.0
                })

        extractor_sheet = pd.DataFrame(extractor_summary)
        extractor_sheet.to_excel(writer, sheet_name='Keywords_by_Extractor', index=False)

        # Sheet 4: Extractor Performance Comparison
        performance_data = []
        for extractor in df['extractor'].unique():
            extractor_df = df[df['extractor'] == extractor]
            unique_keywords = extractor_df['keyword'].nunique()
            total_instances = len(extractor_df)

            # Count how many of this extractor's keywords are also found by others
            consensus_keywords = 0
            for keyword in extractor_df['keyword'].unique():
                if len(consensus_df[consensus_df['keyphrase'] == keyword]['extractors'].iloc[0]) > 1:
                    consensus_keywords += 1

            performance_data.append({
                'Extractor': extractor,
                'Total_Instances': total_instances,
                'Unique_Keywords': unique_keywords,
                'Consensus_Keywords': consensus_keywords,
                'Consensus_Rate': round(consensus_keywords / unique_keywords * 100, 2) if unique_keywords > 0 else 0,
                'Avg_Confidence': round(extractor_df['confidence'].mean(), 3) if 'confidence' in extractor_df.columns else 0.0
            })

        performance_sheet = pd.DataFrame(performance_data)
        performance_sheet.to_excel(writer, sheet_name='Extractor_Performance', index=False)

        # Sheet 5: Complete Consensus Analysis
        consensus_display = consensus_df.copy()
        consensus_display['extractors'] = consensus_display['extractors'].apply(lambda x: ', '.join(x))
        consensus_display['extractor_details'] = consensus_display['extractor_details'].apply(str)
        consensus_display.to_excel(writer, sheet_name='Complete_Consensus_Analysis', index=False)

        # Sheet 6: Raw Extraction Results
        df.to_excel(writer, sheet_name='Raw_Results', index=False)



def final_keyword_reduction_copy(
    df: pd.DataFrame,
    preference = None,
    damping: float = 0.70
) -> pd.DataFrame:

    print(f"Input DataFrame shape: {df.shape}")
    print(f"Input DataFrame columns: {list(df.columns)}")

    if "keyphrase" not in df.columns:
        raise ValueError("Input DataFrame must contain a 'keyphrase' column.")

    if df.empty:
        print("Input DataFrame is empty, returning empty result")
        return pd.DataFrame(columns=["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors", "extractor_details"])

    # --- 1. Remove verbs/general stopwords (case-insensitive) ---
    general_words = set([
        "is","are","was","were","be","been","being","have","has","had","the","and","or","but","not",
        "in","on","at","to","for","with","by","from","of","as","it","this","that","these","those",
        "do","does","did","done","can","could","may","might","must","shall","should","will","would",
    ])

    def clean_keyword(kw: str) -> str:
        text = kw.strip()
        tokens = [t for t in text.split() if t.lower() not in general_words]
        cleaned = " ".join(tokens)
        return cleaned if cleaned.lower() not in general_words else ""

    # Clean keyphrases (preserving case in output)
    cleaned_keywords = [clean_keyword(str(k)) if pd.notna(k) else "" for k in df["keyphrase"]]
    technical_keywords = [kw for kw in cleaned_keywords if len(kw) > 3]

    print(f"Filtered {len(cleaned_keywords) - len(technical_keywords)} invalid/general words.")

    if not technical_keywords:
        df["keyphrase"] = None
        return df

    # --- 2. Normalization & Mapping with extractor tracking ---
    normalized_map = {}
    keyphrase_to_row_map = {}  # Map keyphrase to original DataFrame row

    # Create mapping from original keyphrases to their DataFrame rows
    for idx, row in df.iterrows():
        keyphrase = str(row["keyphrase"]).strip()
        if keyphrase in technical_keywords:
            # Store the row as a dictionary to avoid Series issues
            keyphrase_to_row_map[keyphrase] = row.to_dict()

    print(f"Created mapping for {len(keyphrase_to_row_map)} keyphrases")

    # Create normalized mapping
    for original_kw in technical_keywords:
        normalized_kw = re.sub(r'[\s-]+', ' ', original_kw.lower()).strip()
        normalized_map.setdefault(normalized_kw, []).append(original_kw)
    unique_normalized_kws = list(normalized_map.keys())

    # --- 3. Generate Embeddings ---
    # print("Generating keyword embeddings...")
    model = SentenceTransformer("allenai-specter")
    embeddings = model.encode(unique_normalized_kws, show_progress_bar=False)

    # --- 4. Clustering with Affinity Propagation ---
    if len(unique_normalized_kws) == 1:
        print("Only one keyword found, skipping clustering.")
        final_clusters = [[0]]
    else:
        similarity_matrix = cosine_similarity(embeddings)
        if preference is None:
            preference = np.median(similarity_matrix)

        clustering = AffinityPropagation(
            affinity="precomputed",
            preference=preference,
            damping=damping,
            random_state=42
        )
        labels = clustering.fit_predict(similarity_matrix)

        final_clusters = []
        for label in set(labels):
            cluster_indices = [i for i, l in enumerate(labels) if l == label]
            final_clusters.append(cluster_indices)

    print(f"Formed {len(final_clusters)} semantic clusters.")

    # --- Map each normalized keyword -> cluster phrases ---
    cluster_map = {}
    for c_id, cluster_indices in enumerate(final_clusters, 1):
        cluster_phrases = []
        for idx in cluster_indices:
            normalized_kw = unique_normalized_kws[idx]
            cluster_phrases.extend(normalized_map[normalized_kw])
        for idx in cluster_indices:
            cluster_map[unique_normalized_kws[idx]] = cluster_phrases
        # print(f"{c_id}: {cluster_phrases}")

    # --- 5. Prepare for clustering (no representative selection needed) ---
    # We'll process all clusters directly without selecting representatives

    # --- 6. Create clusters and return one row per cluster with representative keyphrase ---
    result_rows = []

    for cluster_indices in final_clusters:
        # Get all keyphrases in this cluster
        cluster_keyphrases = []
        cluster_extractors = []
        merged_extractor_details = {}
        total_frequency = 0

        for index in cluster_indices:
            normalized_kw = unique_normalized_kws[index]
            keyphrases_in_norm = normalized_map[normalized_kw]

            for keyphrase in keyphrases_in_norm:
                if keyphrase in keyphrase_to_row_map:
                    cluster_keyphrases.append(keyphrase)
                    row = keyphrase_to_row_map[keyphrase]

                    # Collect extractors for this keyphrase
                    try:
                        if 'extractors' in row:
                            extractors_value = row['extractors']

                            if extractors_value is not None and pd.notna(extractors_value):
                                if isinstance(extractors_value, str):
                                    # Handle both comma-separated strings and list strings
                                    if extractors_value.startswith('[') and extractors_value.endswith(']'):
                                        try:
                                            extractors_list = eval(extractors_value)
                                        except:
                                            extractors_list = [extractors_value]
                                    else:
                                        extractors_list = [ext.strip() for ext in extractors_value.split(',')]
                                elif isinstance(extractors_value, list):
                                    extractors_list = extractors_value
                                else:
                                    extractors_list = [str(extractors_value)]

                                cluster_extractors.extend(extractors_list)
                    except Exception as e:
                        print(f"Error processing extractors for {keyphrase}: {e}")
                        continue

                    # Merge extractor details
                    try:
                        if 'extractor_details' in row:
                            extractor_details_value = row['extractor_details']

                            if extractor_details_value is not None and pd.notna(extractor_details_value):
                                try:
                                    if isinstance(extractor_details_value, str):
                                        details = eval(extractor_details_value) if extractor_details_value.startswith('{') else {}
                                    else:
                                        details = extractor_details_value if isinstance(extractor_details_value, dict) else {}

                                    for ext, freq in details.items():
                                        merged_extractor_details[ext] = merged_extractor_details.get(ext, 0) + freq
                                except:
                                    pass
                    except Exception as e:
                        print(f"Error processing extractor_details for {keyphrase}: {e}")

                    # Add to total frequency
                    try:
                        if 'total_frequency' in row:
                            total_frequency_value = row['total_frequency']

                            if total_frequency_value is not None and pd.notna(total_frequency_value):
                                total_frequency += int(total_frequency_value)
                    except Exception as e:
                        print(f"Error processing total_frequency for {keyphrase}: {e}")
        # print(f"{cluster_keyphrases =}")
        # Only add cluster if it has keyphrases
        if cluster_keyphrases:
            # Remove duplicates while preserving order
            unique_cluster_keyphrases = []
            seen = set()
            for kp in cluster_keyphrases:
                if kp not in seen:
                    unique_cluster_keyphrases.append(kp)
                    seen.add(kp)

            # Select representative keyphrase (longest one)
            representative_keyphrase = max(unique_cluster_keyphrases, key=len)

            # Format extractors as comma-separated string for consistency with original format
            unique_extractors = list(set(cluster_extractors))
            extractors_str = ', '.join(sorted(unique_extractors))

            result_rows.append({
                'keyphrase': representative_keyphrase,
                'cluster_phrases': unique_cluster_keyphrases,
                'len_cluster': len(unique_cluster_keyphrases),
                'extractor_count': len(unique_extractors),
                'extractors': extractors_str,
                # 'extractor_details': merged_extractor_details,
                'total_frequency': total_frequency
            })

    # Create DataFrame with same structure as input but with cluster information
    print(f"Created {len(result_rows)} result rows")

    if not result_rows:
        print("No result rows created, returning empty DataFrame")
        return pd.DataFrame(columns=["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors"]) #"extractor_details"

    result_df = pd.DataFrame(result_rows)
    print(f"Result DataFrame shape: {result_df.shape}")
    print(f"Result DataFrame columns: {list(result_df.columns)}")

    # Order columns to match expected format
    columns_order = ["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors"] #"extractor_details"
    if 'total_frequency' in result_df.columns:
        columns_order.append('total_frequency')

    result_df = result_df[columns_order]
    print(f"Sample cluster_phrases: {result_df['cluster_phrases'].head()}")
    return result_df.reset_index(drop=True)




def final_keyword_reduction(
    df: pd.DataFrame,
    preference = None,
    damping: float = 0.70
) -> pd.DataFrame:

    print(f"Input DataFrame shape: {df.shape}")
    print(f"Input DataFrame columns: {list(df.columns)}")

    if "keyphrase" not in df.columns:
        raise ValueError("Input DataFrame must contain a 'keyphrase' column.")

    if df.empty:
        print("Input DataFrame is empty, returning empty result")
        return pd.DataFrame(columns=["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors", "extractor_details"])

    # --- 1. Remove verbs/general stopwords (case-insensitive) ---
    general_words = set([
        "is","are","was","were","be","been","being","have","has","had","the","and","or","but","not",
        "in","on","at","to","for","with","by","from","of","as","it","this","that","these","those",
        "do","does","did","done","can","could","may","might","must","shall","should","will","would",
    ])

    def clean_keyword(kw: str) -> str:
        text = kw.strip()
        tokens = [t for t in text.split() if t.lower() not in general_words]
        cleaned = " ".join(tokens)
        return cleaned if cleaned.lower() not in general_words else ""

    # Clean keyphrases (preserving case in output)
    cleaned_keywords = [clean_keyword(str(k)) if pd.notna(k) else "" for k in df["keyphrase"]]
    technical_keywords = [kw for kw in cleaned_keywords if len(kw) > 3]

    print(f"Filtered {len(cleaned_keywords) - len(technical_keywords)} invalid/general words.")

    if not technical_keywords:
        df["keyphrase"] = None
        return df

    # --- 2. Normalization & Mapping with extractor tracking ---
    normalized_map = {}
    keyphrase_to_row_map = {}  # Map keyphrase to original DataFrame row

    # Create mapping from original keyphrases to their DataFrame rows
    for idx, row in df.iterrows():
        keyphrase = str(row["keyphrase"]).strip()
        if keyphrase in technical_keywords:
            # Store the row as a dictionary to avoid Series issues
            keyphrase_to_row_map[keyphrase] = row.to_dict()

    print(f"Created mapping for {len(keyphrase_to_row_map)} keyphrases")

    # Create normalized mapping
    for original_kw in technical_keywords:
        normalized_kw = re.sub(r'[\s-]+', ' ', original_kw.lower()).strip()
        normalized_map.setdefault(normalized_kw, []).append(original_kw)
    unique_normalized_kws = list(normalized_map.keys())

    # --- 3. Generate Embeddings ---
    # print("Generating keyword embeddings...")
    model = SentenceTransformer("allenai-specter")
    embeddings = model.encode(unique_normalized_kws, show_progress_bar=False)

    # --- 4. Clustering with Affinity Propagation ---
    if len(unique_normalized_kws) == 1:
        print("Only one keyword found, skipping clustering.")
        final_clusters = [[0]]
    else:
        similarity_matrix = cosine_similarity(embeddings)
        if preference is None:
            preference = np.median(similarity_matrix)

        clustering = AffinityPropagation(
            affinity="precomputed",
            preference=preference,
            damping=damping,
            random_state=42
        )
        labels = clustering.fit_predict(similarity_matrix)

        final_clusters = []
        for label in set(labels):
            cluster_indices = [i for i, l in enumerate(labels) if l == label]
            final_clusters.append(cluster_indices)

    print(f"Formed {len(final_clusters)} semantic clusters.")

    # --- Map each normalized keyword -> cluster phrases ---
    cluster_map = {}
    for c_id, cluster_indices in enumerate(final_clusters, 1):
        cluster_phrases = []
        for idx in cluster_indices:
            normalized_kw = unique_normalized_kws[idx]
            cluster_phrases.extend(normalized_map[normalized_kw])
        for idx in cluster_indices:
            cluster_map[unique_normalized_kws[idx]] = cluster_phrases
        # print(f"{c_id}: {cluster_phrases}")

    # --- 5. Prepare for clustering (no representative selection needed) ---
    # We'll process all clusters directly without selecting representatives

    # --- 6. Create clusters and return one row per cluster with representative keyphrase ---
    result_rows = []

    for cluster_indices in final_clusters:
        # Get all keyphrases in this cluster
        cluster_keyphrases = []
        cluster_extractors = []
        merged_extractor_details = {}
        total_frequency = 0

        for index in cluster_indices:
            normalized_kw = unique_normalized_kws[index]
            keyphrases_in_norm = normalized_map[normalized_kw]

            for keyphrase in keyphrases_in_norm:
                if keyphrase in keyphrase_to_row_map:
                    cluster_keyphrases.append(keyphrase)
                    row = keyphrase_to_row_map[keyphrase]

                    # Collect extractors for this keyphrase
                    try:
                        if 'extractors' in row:
                            extractors_value = row['extractors']

                            # Handle pandas/numpy objects by converting to Python native types
                            if hasattr(extractors_value, 'item'):  # numpy scalar
                                extractors_value = extractors_value.item()
                            elif hasattr(extractors_value, 'tolist'):  # numpy array
                                extractors_value = extractors_value.tolist()
                            elif hasattr(extractors_value, 'iloc'):  # pandas Series
                                extractors_value = extractors_value.iloc[0] if len(extractors_value) > 0 else None

                            # Check for None and NaN using safe methods
                            if extractors_value is not None:
                                try:
                                    is_na = pd.isna(extractors_value)
                                    if hasattr(is_na, 'any'):  # Handle array-like results
                                        is_na = is_na.any()
                                except:
                                    is_na = False

                                if not is_na:
                                    if isinstance(extractors_value, str):
                                        # Handle both comma-separated strings and list strings
                                        if extractors_value.startswith('[') and extractors_value.endswith(']'):
                                            try:
                                                extractors_list = eval(extractors_value)
                                            except:
                                                extractors_list = [extractors_value]
                                        else:
                                            extractors_list = [ext.strip() for ext in extractors_value.split(',')]
                                    elif isinstance(extractors_value, list):
                                        extractors_list = extractors_value
                                    else:
                                        extractors_list = [str(extractors_value)]

                                    cluster_extractors.extend(extractors_list)
                    except Exception as e:
                        print(f"Error processing extractors for {keyphrase}: {e}")
                        continue

                    # Merge extractor details
                    try:
                        if 'extractor_details' in row:
                            extractor_details_value = row['extractor_details']

                            # Handle pandas/numpy objects by converting to Python native types
                            if hasattr(extractor_details_value, 'item'):  # numpy scalar
                                extractor_details_value = extractor_details_value.item()
                            elif hasattr(extractor_details_value, 'tolist'):  # numpy array
                                extractor_details_value = extractor_details_value.tolist()
                            elif hasattr(extractor_details_value, 'iloc'):  # pandas Series
                                extractor_details_value = extractor_details_value.iloc[0] if len(extractor_details_value) > 0 else None

                            if extractor_details_value is not None:
                                try:
                                    is_na = pd.isna(extractor_details_value)
                                    if hasattr(is_na, 'any'):  # Handle array-like results
                                        is_na = is_na.any()
                                except:
                                    is_na = False

                                if not is_na:
                                    try:
                                        if isinstance(extractor_details_value, str):
                                            details = eval(extractor_details_value) if extractor_details_value.startswith('{') else {}
                                        else:
                                            details = extractor_details_value if isinstance(extractor_details_value, dict) else {}

                                        for ext, freq in details.items():
                                            merged_extractor_details[ext] = merged_extractor_details.get(ext, 0) + freq
                                    except:
                                        pass
                    except Exception as e:
                        print(f"Error processing extractor_details for {keyphrase}: {e}")

                    # Add to total frequency
                    try:
                        if 'total_frequency' in row:
                            total_frequency_value = row['total_frequency']

                            # Handle pandas/numpy objects by converting to Python native types
                            if hasattr(total_frequency_value, 'item'):  # numpy scalar
                                total_frequency_value = total_frequency_value.item()
                            elif hasattr(total_frequency_value, 'tolist'):  # numpy array
                                total_frequency_value = total_frequency_value.tolist()
                            elif hasattr(total_frequency_value, 'iloc'):  # pandas Series
                                total_frequency_value = total_frequency_value.iloc[0] if len(total_frequency_value) > 0 else None

                            if total_frequency_value is not None:
                                try:
                                    is_na = pd.isna(total_frequency_value)
                                    if hasattr(is_na, 'any'):  # Handle array-like results
                                        is_na = is_na.any()
                                except:
                                    is_na = False

                                if not is_na:
                                    total_frequency += int(total_frequency_value)
                    except Exception as e:
                        print(f"Error processing total_frequency for {keyphrase}: {e}")
        # print(f"{cluster_keyphrases =}")
        # Only add cluster if it has keyphrases
        if cluster_keyphrases:
            # Remove duplicates while preserving order
            unique_cluster_keyphrases = []
            seen = set()
            for kp in cluster_keyphrases:
                if kp not in seen:
                    unique_cluster_keyphrases.append(kp)
                    seen.add(kp)

            # Select representative keyphrase (longest one)
            representative_keyphrase = max(unique_cluster_keyphrases, key=len)

            # Format extractors as comma-separated string for consistency with original format
            unique_extractors = list(set(cluster_extractors))
            extractors_str = ', '.join(sorted(unique_extractors))

            result_rows.append({
                'keyphrase': representative_keyphrase,
                'cluster_phrases': unique_cluster_keyphrases,
                'len_cluster': len(unique_cluster_keyphrases),
                'extractor_count': len(unique_extractors),
                'extractors': extractors_str,
                # 'extractor_details': merged_extractor_details,
                'total_frequency': total_frequency
            })

    # Create DataFrame with same structure as input but with cluster information
    print(f"Created {len(result_rows)} result rows")

    if not result_rows:
        print("No result rows created, returning empty DataFrame")
        return pd.DataFrame(columns=["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors"]) #"extractor_details"

    result_df = pd.DataFrame(result_rows)
    print(f"Result DataFrame shape: {result_df.shape}")
    print(f"Result DataFrame columns: {list(result_df.columns)}")

    # Order columns to match expected format
    columns_order = ["keyphrase", "cluster_phrases", "len_cluster", "extractor_count", "extractors"] #"extractor_details"
    if 'total_frequency' in result_df.columns:
        columns_order.append('total_frequency')

    result_df = result_df[columns_order]
    print(f"Sample cluster_phrases: {result_df['cluster_phrases'].head()}")
    return result_df.reset_index(drop=True)



def has_position_rank(val):
    try:
        # interpret the string as a list
        parsed = ast.literal_eval(val) if isinstance(val, str) else val
        if isinstance(parsed, list):
            return 'position_rank' in parsed
    except Exception:
        pass
    return False



def merge_and_deduplicate_keywords(df):
    """
    Comprehensive deduplication and merging of keywords with advanced processing:
    1. Normalize and clean keywords
    2. Merge exact case-insensitive duplicates
    3. Handle singular/plural variations
    4. Remove partial substring matches (keep longer terms)
    5. Combine extractors and source sentences
    6. Add frequency and consensus metrics
    7. Sort by importance (consensus count, frequency, length)

    Args:
        df (pd.DataFrame): DataFrame with columns ['extractor', 'keyword', 'source_sentence', 'confidence'].

    Returns:
        pd.DataFrame: Processed DataFrame with columns:
                    ['keyword', 'extractor_list', 'extractor_count', 'frequency',
                    'source_sentences', 'confidence_avg', 'keyword_length']
    """
    if df.empty:
        return pd.DataFrame(columns=['keyword', 'extractor_list', 'extractor_count', 'frequency',
                                'source_sentences', 'confidence_avg', 'keyword_length'])

    print(f"Starting keyword deduplication and merging...")
    # print(f"   Input: {len(df)} keyword instances")

    # Step 1: Normalize and clean keywords
    df = df.copy()
    df['keyword'] = df['keyword'].astype(str).str.strip()
    df['keyword'] = df['keyword'].str.replace(r'\s+', ' ', regex=True)  # Normalize whitespace
    df['keyword_lower'] = df['keyword'].str.lower()

    # Remove very short keywords (less than 3 characters)
    df = df[df['keyword'].str.len() >= 3].reset_index(drop=True)

    # Step 2: Group by lowercase keyword to merge exact duplicates
    # print(f"   Step 1: Merging exact case-insensitive duplicates...")
    grouped = (
        df.groupby('keyword_lower')
        .agg({
            'keyword': lambda x: max(x, key=len),  # Keep the longest form
            'extractor': lambda x: list(x),
            'source_sentence': lambda x: list(x),
            'confidence': lambda x: list(x) if 'confidence' in df.columns else [1.0] * len(x)
        })
        .reset_index()
    )

    # Step 3: Handle singular/plural variations using inflect
    # print(f"   Step 2: Handling singular/plural variations...")
    try:
        import inflect
        p = inflect.engine()

        # Create mapping of singular/plural forms
        singular_plural_map = {}
        for idx, row in grouped.iterrows():
            keyword = row['keyword']
            keyword_lower = row['keyword_lower']

            # Get singular and plural forms
            singular = p.singular_noun(keyword)
            plural = p.plural(keyword)

            singular_form = singular if singular else keyword
            plural_form = plural if plural else keyword

            # Use the longer form as the canonical form
            canonical = max([keyword, singular_form, plural_form], key=len)
            canonical_lower = canonical.lower()

            # Map all variations to canonical form
            for variant in [keyword_lower, singular_form.lower(), plural_form.lower()]:
                if variant not in singular_plural_map:
                    singular_plural_map[variant] = canonical_lower

        # Apply singular/plural mapping
        grouped['canonical_keyword'] = grouped['keyword_lower'].map(
            lambda x: singular_plural_map.get(x, x)
        )

        # Re-group by canonical form
        canonical_grouped = (
            grouped.groupby('canonical_keyword')
            .agg({
                'keyword': lambda x: max(x, key=len),
                'extractor': lambda x: [item for sublist in x for item in sublist],
                'source_sentence': lambda x: [item for sublist in x for item in sublist],
                'confidence': lambda x: [item for sublist in x for item in sublist]
            })
            .reset_index(drop=True)
        )
        grouped = canonical_grouped

    except ImportError:
        print(f" inflect not available, skipping singular/plural handling")

    # Step 4: Remove partial substring matches (keep longer terms)
    # print(f" Step 3: Removing partial substring matches...")
    final_keywords = []
    all_keywords = grouped['keyword'].tolist()

    for idx, row in grouped.iterrows():
        keyword = row['keyword']
        keyword_lower = keyword.lower()

        # Check if this keyword is a substring of any longer keyword
        is_substring = False
        for other_keyword in all_keywords:
            if (other_keyword != keyword and
                len(other_keyword) > len(keyword) and
                keyword_lower in other_keyword.lower()):
                is_substring = True
                break

        if not is_substring:
            final_keywords.append(row)

    # Step 5: Process final keywords and create output format
    # print(f"   Step 4: Creating final output format...")
    result_rows = []

    for row in final_keywords:
        keyword = row['keyword']
        extractors = row['extractor']
        source_sentences = row['source_sentence']
        confidences = row['confidence']

        # Process extractors
        unique_extractors = sorted(list(set(extractors)))
        extractor_count = len(unique_extractors)
        extractor_list = '|'.join(unique_extractors)

        # Process source sentences
        unique_sentences = sorted(list(set(source_sentences)))
        source_sentences_str = ' | '.join(unique_sentences)

        # Calculate metrics
        frequency = len(extractors)  # Total occurrences across all extractors
        confidence_avg = sum(confidences) / len(confidences) if confidences else 1.0
        keyword_length = len(keyword)

        result_rows.append({
            'keyword': keyword,
            'extractor_list': extractor_list,
            'extractor_count': extractor_count,
            'frequency': frequency,
            'source_sentences': source_sentences_str,
            'confidence_avg': round(confidence_avg, 3),
            'keyword_length': keyword_length
        })

    # Step 6: Create result DataFrame and sort by importance
    result_df = pd.DataFrame(result_rows)

    if not result_df.empty:
        # Sort by: extractor_count (desc), frequency (desc), keyword_length (desc)
        result_df = result_df.sort_values(
            ['extractor_count', 'frequency', 'keyword_length'],
            ascending=[False, False, False]
        ).reset_index(drop=True)

    print(f"   Deduplication complete!")
    # print(f"   Output: {len(result_df)} unique keywords")
    # print(f"   Reduction: {len(df) - len(result_df)} duplicates removed")

    return result_df


def run_unified_keyphrase_extraction_0(
    tan_name :str,
    topic: str,
    topic_information: str,
    sample_keyphrases: List[str],
    section_texts_dict: Dict[str, str],
    config: Optional[Union[str, Dict]] = None
) -> None:
    """
    Run the complete unified keyphrase extraction pipeline.

    Args:
        topic: The main topic/domain for extraction
        topic_information: Detailed information about the topic
        sample_keyphrases: List of sample keyphrases for the domain
        section_texts_dict: Dictionary containing document sections (title, abstract, etc.)
        config: Optional configuration dictionary or path to config file
    """
    print("UNIFIED KEYPHRASE EXTRACTION DEMO")
    print("=" * 60)

    # Create sample text from abstract for demonstration
    sample_text = section_texts_dict["abstract"]

    print(f"DOCUMENT INFO:")
    # print(f"Title: {section_texts_dict['title']}")
    # print(f"Topic: {topic}")
    # print(f"Topic Info: {topic_information[:100]}...")
    # print(f"Sample Keyphrases: {len(sample_keyphrases)} provided")
    print(f"Sections: {list(section_texts_dict.keys())}")

    try:
        # Initialize the unified extractor
        print(f"\n Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor(config)

        # Show which extractors were initialized
        # print(f"\n AVAILABLE EXTRACTORS:")
        # for extractor_name in extractor.extractors.keys():
            # print(f" {extractor_name}")

        total_extractors = len(extractor.extractors)
        print(f"Total extractors available: {total_extractors}")

        # Update configuration with topic information
        extractor.config["extractors"]["llama"]["topic_name"] = topic
        extractor.config["extractors"]["llama"]["topic_info"] = topic_information
        extractor.config["extractors"]["llama"]["sample_keywords"] = sample_keyphrases

        # print(f" Configuration updated with topic information")

        # Extract keyphrases using the new semantic filtering approach
        print(f"\nExtracting keyphrases with semantic filtering...")
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=section_texts_dict,
            use_cleaned_text=True  # Enable semantic filtering for all extractors
        )

        # Display results
        if not df.empty:
            print(f"\nEXTRACTION RESULTS:")
            print(f"Total keyword-sentence pairs: {len(df)}")
            print(f"Unique keywords: {df['keyword'].nunique()}")
            print(f"Extractors used: {', '.join(df['extractor'].unique())}")

            # Show extractor performance
            print(f"\nKEYWORDS BY EXTRACTOR:")
            extractor_counts = df['extractor'].value_counts()
            for extractor_name, count in extractor_counts.items():
                print(f"  {extractor_name}: {count} keywords")

            # Show top keywords overall
            print(f"\nTOP 10 KEYWORDS OVERALL:")
            top_keywords = df['keyword'].value_counts().head(10)
            for i, (keyword, count) in enumerate(top_keywords.items(), 1):
                print(f"  {i:2d}. {keyword} (found {count} times)")

            # Show sample keywords by extractor
            print(f"\nSAMPLE KEYWORDS BY EXTRACTOR:")
            for extractor_name in df['extractor'].unique():
                extractor_df = df[df['extractor'] == extractor_name]
                sample_keywords_list = extractor_df['keyword'].unique()[:5]
                print(f"  {extractor_name}: {', '.join(sample_keywords_list)}")

            # Show semantic filtering effectiveness
            if 'llama' in df['extractor'].unique():
                llama_df = df[df['extractor'] == 'llama']
                print(f"\nSEMANTIC FILTERING RESULTS:")
                print(f"  LLaMA extracted {len(llama_df)} keyword instances")
                print(f"  Unique LLaMA keywords: {llama_df['keyword'].nunique()}")

                # Check if filtering was applied
                filtered_segments = [col for col in section_texts_dict.keys()
                                if col not in ['title', 'abstract']]
                print(f"  Segments available for filtering: {filtered_segments}")

            # Analyze keyphrase consensus across extractors
            print(f"\nANALYZING KEYPHRASE CONSENSUS...")
            consensus_analysis = analyze_keyphrase_consensus(df)

            print(f"\nCONSENSUS ANALYSIS:")
            print(f"  Total unique keyphrases: {len(consensus_analysis)}")

            # Show keyphrases by consensus level
            consensus_levels = consensus_analysis['extractor_count'].value_counts().sort_index(ascending=False)
            for count, num_keyphrases in consensus_levels.items():
                print(f"  Keyphrases found by {count} extractors: {num_keyphrases}")

            # Show top consensus keyphrases
            high_consensus = consensus_analysis[consensus_analysis['extractor_count'] >= 2].sort_values(
                ['extractor_count', 'total_frequency'], ascending=[False, False]
            )

            if not high_consensus.empty:
                print(f"\nTOP CONSENSUS KEYPHRASES (found by multiple extractors):")
                for i, (_, row) in enumerate(high_consensus.head(10).iterrows(), 1):
                    extractors = ', '.join(row['extractors'])
                    print(f"  {i:2d}. {row['keyphrase']} ({row['extractor_count']} extractors: {extractors})")

            # Apply final keyword reduction before saving
            print(f"\nAPPLYING FINAL KEYWORD REDUCTION...")
            print(f"Before reduction: {len(consensus_analysis)} keyphrases")

            try:
                consensus_analysis_reduced = final_keyword_reduction(consensus_analysis, preference=None, damping=0.5)
                print(f"After reduction: {len(consensus_analysis_reduced)} keyphrases")
                print(f"Reduction: {len(consensus_analysis) - len(consensus_analysis_reduced)} keyphrases removed")
            except Exception as e:
                print(f"  Final keyword reduction failed: {e}")
                print(f"  Using original consensus analysis")
                consensus_analysis_reduced = consensus_analysis

            # Only print cluster_phrases if the column exists
            if 'cluster_phrases' in consensus_analysis_reduced.columns:
                print("Cluster phrases:")
                print(consensus_analysis_reduced['cluster_phrases'])
            else:
                print("No cluster_phrases column found - using original consensus analysis format")
            # Save detailed results
            print(f"\nSaving detailed results...")

            # Save raw extraction results
            df.to_excel(f"unified_extraction_results_{tan_name}.xlsx", index=False)
            print(f"Raw results saved to: unified_extraction_results_{tan_name}.xlsx")

            # Save consensus analysis (with final keyword reduction applied)
            consensus_analysis_reduced.to_excel(f"keyphrase_consensus_analysis_{tan_name}.xlsx", index=False)
            print(f"Consensus analysis (reduced) saved to: keyphrase_consensus_analysis_{tan_name}.xlsx")

            # Create summary report
            # create_summary_report(df, consensus_analysis_reduced, section_texts_dict, topic, topic_information)
            # print(f"Summary report saved to: extraction_summary_report.xlsx")

        else:
            print("No keywords extracted")

    except Exception as e:
        print(f"Error during extraction: {e}")
        import traceback
        traceback.print_exc()

        print(f"\nTROUBLESHOOTING:")
        print(f"1. Check if all required models are available")
        print(f"2. Verify LLaMA API endpoint is accessible")
        print(f"3. Ensure SentenceTransformer model is installed")
        print(f"4. Check configuration parameters")

    print(f"\nExecution completed!")


def run_unified_keyphrase_extraction(
    tan_name :str,
    topic: str,
    topic_information: str,
    sample_keyphrases: List[str],
    section_texts_dict: Dict[str, str],
    config: Optional[Union[str, Dict]] = None
) -> None:
    """
    Run the complete unified keyphrase extraction pipeline.

    Args:
        topic: The main topic/domain for extraction
        topic_information: Detailed information about the topic
        sample_keyphrases: List of sample keyphrases for the domain
        section_texts_dict: Dictionary containing document sections (title, abstract, etc.)
        config: Optional configuration dictionary or path to config file
    """
    print("UNIFIED KEYPHRASE EXTRACTION DEMO")
    print("=" * 60)

    # Create sample text from abstract for demonstration
    sample_text = section_texts_dict["abstract"]

    print(f"DOCUMENT INFO:")
    # print(f"Title: {section_texts_dict['title']}")
    # print(f"Topic: {topic}")
    # print(f"Topic Info: {topic_information[:100]}...")
    # print(f"Sample Keyphrases: {len(sample_keyphrases)} provided")
    print(f"Sections: {list(section_texts_dict.keys())}")

    try:
        # Initialize the unified extractor
        print(f"\n Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor(config)

        # Show which extractors were initialized
        # print(f"\n AVAILABLE EXTRACTORS:")
        # for extractor_name in extractor.extractors.keys():
            # print(f" {extractor_name}")

        total_extractors = len(extractor.extractors)
        print(f"Total extractors available: {total_extractors}")

        # Update configuration with topic information
        extractor.config["extractors"]["llama"]["topic_name"] = topic
        extractor.config["extractors"]["llama"]["topic_info"] = topic_information
        extractor.config["extractors"]["llama"]["sample_keywords"] = sample_keyphrases

        # print(f" Configuration updated with topic information")

        # Extract keyphrases using the new semantic filtering approach
        print(f"\nExtracting keyphrases with semantic filtering...")
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=section_texts_dict,
            use_cleaned_text=True  # Enable semantic filtering for all extractors
        )

        # Display results
        if not df.empty:
            print(f"\nEXTRACTION RESULTS:")
            print(f"Total keyword-sentence pairs: {len(df)}")
            print(f"Unique keywords: {df['keyword'].nunique()}")
            print(f"Extractors used: {', '.join(df['extractor'].unique())}")

            # Show extractor performance
            print(f"\nKEYWORDS BY EXTRACTOR:")
            extractor_counts = df['extractor'].value_counts()
            for extractor_name, count in extractor_counts.items():
                print(f"  {extractor_name}: {count} keywords")

            # Show top keywords overall
            print(f"\nTOP 10 KEYWORDS OVERALL:")
            top_keywords = df['keyword'].value_counts().head(10)
            for i, (keyword, count) in enumerate(top_keywords.items(), 1):
                print(f"  {i:2d}. {keyword} (found {count} times)")

            # Show sample keywords by extractor
            print(f"\nSAMPLE KEYWORDS BY EXTRACTOR:")
            for extractor_name in df['extractor'].unique():
                extractor_df = df[df['extractor'] == extractor_name]
                sample_keywords_list = extractor_df['keyword'].unique()[:5]
                print(f"  {extractor_name}: {', '.join(sample_keywords_list)}")

            # Show semantic filtering effectiveness
            if 'llama' in df['extractor'].unique():
                llama_df = df[df['extractor'] == 'llama']
                print(f"\nSEMANTIC FILTERING RESULTS:")
                print(f"  LLaMA extracted {len(llama_df)} keyword instances")
                print(f"  Unique LLaMA keywords: {llama_df['keyword'].nunique()}")

                # Check if filtering was applied
                filtered_segments = [col for col in section_texts_dict.keys()
                                if col not in ['title', 'abstract']]
                print(f"  Segments available for filtering: {filtered_segments}")

            # Analyze keyphrase consensus across extractors
            print(f"\nANALYZING KEYPHRASE CONSENSUS...")
            consensus_analysis = analyze_keyphrase_consensus(df)

            print(f"\nCONSENSUS ANALYSIS:")
            print(f"  Total unique keyphrases: {len(consensus_analysis)}")

            # Show keyphrases by consensus level
            consensus_levels = consensus_analysis['extractor_count'].value_counts().sort_index(ascending=False)
            for count, num_keyphrases in consensus_levels.items():
                print(f"  Keyphrases found by {count} extractors: {num_keyphrases}")

            # Show top consensus keyphrases
            high_consensus = consensus_analysis[consensus_analysis['extractor_count'] >= 2].sort_values(
                ['extractor_count', 'total_frequency'], ascending=[False, False]
            )

            if not high_consensus.empty:
                print(f"\nTOP CONSENSUS KEYPHRASES (found by multiple extractors):")
                for i, (_, row) in enumerate(high_consensus.head(10).iterrows(), 1):
                    extractors = ', '.join(row['extractors'])
                    print(f"  {i:2d}. {row['keyphrase']} ({row['extractor_count']} extractors: {extractors})")

            # Apply final keyword reduction before saving
            print(f"\nAPPLYING FINAL KEYWORD REDUCTION...")
            print(f"Before reduction: {len(consensus_analysis)} keyphrases")

            try:
                consensus_analysis_reduced = final_keyword_reduction(consensus_analysis, preference=None, damping=0.5)
                print(f"After reduction: {len(consensus_analysis_reduced)} keyphrases")
                print(f"Reduction: {len(consensus_analysis) - len(consensus_analysis_reduced)} keyphrases removed")
            except Exception as e:
                print(f"  Final keyword reduction failed: {e}")
                print(f"  Using original consensus analysis")
                consensus_analysis_reduced = consensus_analysis

            # Only print cluster_phrases if the column exists
            if 'cluster_phrases' in consensus_analysis_reduced.columns:
                print("Cluster phrases:")
                print(consensus_analysis_reduced['cluster_phrases'])
            else:
                print("No cluster_phrases column found - using original consensus analysis format")

            # Apply unique cluster phrases processing if cluster_phrases column exists
            if 'cluster_phrases' in consensus_analysis_reduced.columns:
                print(f"\nAPPLYING UNIQUE CLUSTER PHRASES PROCESSING...")
                try:
                    consensus_analysis_with_unique = add_unique_cluster_phrases(
                        consensus_analysis_reduced.copy(),
                        column="cluster_phrases",
                        threshold=0.85,
                        top_n=3
                    )
                    print(f"Unique cluster phrases processing completed successfully")
                    print(f"New columns added: unique_cluster_phrases, unique_cluster_phrases_length")

                    # Use the enhanced dataframe for saving
                    consensus_analysis_final = consensus_analysis_with_unique
                except Exception as e:
                    print(f"  Unique cluster phrases processing failed: {e}")
                    print(f"  Using consensus analysis without unique cluster phrases")
                    consensus_analysis_final = consensus_analysis_reduced
            else:
                consensus_analysis_final = consensus_analysis_reduced

            # Save detailed results
            print(f"\nSaving detailed results...")

            # Save raw extraction results
            df.to_excel(f"batch_extraction_unified\\unified_extraction_results_{tan_name}.xlsx", index=False)
            print(f"Raw results saved to: unified_extraction_results_{tan_name}.xlsx")

            # Save consensus analysis (with final keyword reduction and unique cluster phrases applied)
            consensus_analysis_final.to_excel(f"batch_extraction_unified\\keyphrase_consensus_analysis_{tan_name}.xlsx", index=False)
            print(f"Consensus analysis (enhanced) saved to: keyphrase_consensus_analysis_{tan_name}.xlsx")

            # Show final columns in the saved file
            print(f"Final Excel columns: {list(consensus_analysis_final.columns)}")

            # Create summary report
            # create_summary_report(df, consensus_analysis_reduced, section_texts_dict, topic, topic_information)
            # print(f"Summary report saved to: extraction_summary_report.xlsx")

        else:
            print("No keywords extracted")

    except Exception as e:
        print(f"Error during extraction: {e}")
        import traceback
        traceback.print_exc()

        print(f"\nTROUBLESHOOTING:")
        print(f"1. Check if all required models are available")
        print(f"2. Verify LLaMA API endpoint is accessible")
        print(f"3. Ensure SentenceTransformer model is installed")
        print(f"4. Check configuration parameters")

    print(f"\nExecution completed!")






# def clean_dict_values(input_dict):
#     """
#     Cleans dictionary values by:
#     - Removing numbers and alphanumeric tokens
#     - Removing special chars attached to numbers
#     - Keeping ',', ';', and '. ' intact
#     - Preserving sentence splitting at '. '
#     - Removing 2-character strings
#     """
#     cleaned_dict = {}
#     for key, value in input_dict.items():
#         # Ensure string type
#         text = str(value)

#         # Step 1: Remove numbers and alphanumeric tokens (with special chars attached)
#         text = re.sub(r'\b\w*\d\w*\b[^\w\s]*', '', text)

#         # Step 2: Remove standalone numbers with special chars
#         text = re.sub(r'\b\d+[^\w\s]*', '', text)

#         # Step 3: Remove 2-character words (but preserve .,; separators)
#         text = re.sub(r'\b\w{2}\b', '', text)

#         # Step 4: Normalize spaces
#         text = re.sub(r'\s+', ' ', text).strip()

#         # Step 5: Ensure sentence splitting remains with ". "
#         text = text.replace(". ", ". ")

#         cleaned_dict[key] = text
#     return cleaned_dict


def process_excel_for_unified_extraction(
    excel_file_path: str,
    topic: str,
    topic_information: str,
    sample_keyphrases: List[str],
    config: Optional[Union[str, Dict]] = None,
    output_prefix: str = "unified_extraction"
) -> None:
    """
    Process an Excel file with multiple rows and run unified keyphrase extraction for each unique tan_name.

    Args:
        excel_file_path: Path to the Excel file
        topic: The main topic/domain for extraction
        topic_information: Detailed information about the topic
        sample_keyphrases: List of sample keyphrases for the domain
        config: Optional configuration dictionary or path to config file
        output_prefix: Prefix for output files

    Expected Excel columns:
        - tan_name: Unique identifier for each document/experiment
        - experimental_procedures: Text content for experimental procedures
        - results: Text content for results section
        - conclusion: Text content for conclusion section
    """
    print("PROCESSING EXCEL FILE FOR UNIFIED KEYPHRASE EXTRACTION")
    print("=" * 70)

    try:
        # Read the Excel file
        print(f"Reading Excel file: {excel_file_path}")
        df = pd.read_excel(excel_file_path)
        df = df.head(2)
        print(f"Loaded {len(df)} rows from Excel file")
        print(f"Columns found: {list(df.columns)}")

        # Check required columns
        required_columns = ['title', 'abstract', 'tan_name', 'experimental_procedures', 'results', 'conclusion']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Get unique tan_names
        unique_tan_names = df['tan_name'].dropna().unique()
        print(f"Found {len(unique_tan_names)} unique tan_names: {list(unique_tan_names)}")

        # Process each unique tan_name
        all_results = []

        for i, tan_name in enumerate(unique_tan_names, 1):
            print(f"\n{'='*50}")
            print(f"PROCESSING {i}/{len(unique_tan_names)}: {tan_name}")
            print(f"{'='*50}")

            # Filter data for this tan_name
            tan_data = df[df['tan_name'] == tan_name]
            print(f"Found {len(tan_data)} rows for tan_name: {tan_name}")

            # Combine text from all rows for this tan_name
            combined_experimental_procedures = []
            combined_results = []
            combined_conclusion = []

            for _, row in tan_data.iterrows():
                if pd.notna(row['title']):
                    title = str(row['title']).strip()
                if pd.notna(row['abstract']):
                    abstract = str(row['abstract']).strip()
                if pd.notna(row['experimental_procedures']):
                    combined_experimental_procedures.append(str(row['experimental_procedures']).strip())
                if pd.notna(row['results']):
                    combined_results.append(str(row['results']).strip())
                if pd.notna(row['conclusion']):
                    combined_conclusion.append(str(row['conclusion']).strip())

            # Create section_texts_dict for this tan_name
            section_texts_dict = {
                'title': title,  
                'abstract': abstract,
                'experimental_procedures': ' '.join(combined_experimental_procedures),
                'results': ' '.join(combined_results),
                'conclusion': ' '.join(combined_conclusion)
            }

            # Remove empty sections
            section_texts_dict = {k: v for k, v in section_texts_dict.items() if v.strip()}

            print(f"Created section_texts_dict with sections: {list(section_texts_dict.keys())}")
            print(f"Total text length: {sum(len(v) for v in section_texts_dict.values())} characters")

            # Skip if no meaningful content
            if len(section_texts_dict.get('abstract', '').strip()) < 50:
                print(f"  Skipping {tan_name} - insufficient content")
                continue

            try:
                # Run unified keyphrase extraction for this tan_name
                print(f"\nRunning keyphrase extraction for: {tan_name}")

                # Temporarily change output file names to include tan_name
                original_cwd = os.getcwd()

                # Create a subdirectory for this tan_name
                output_dir = f"{output_prefix}_{tan_name.replace(' ', '_').replace('/', '_')}"
                os.makedirs(output_dir, exist_ok=True)
                os.chdir(output_dir)

                # Run the extraction
                run_unified_keyphrase_extraction(
                    tan_name = tan_name,
                    topic=topic,
                    topic_information=topic_information,
                    sample_keyphrases=sample_keyphrases,
                    section_texts_dict=section_texts_dict,
                    config=config
                )

                # Move back to original directory
                os.chdir(original_cwd)

                # Record successful processing
                all_results.append({
                    'tan_name': tan_name,
                    'status': 'success',
                    'output_dir': output_dir,
                    'sections_processed': list(section_texts_dict.keys()),
                    'total_text_length': sum(len(v) for v in section_texts_dict.values())
                })

                print(f" Successfully processed {tan_name}")
                print(f"   Output saved in: {output_dir}/")

            except Exception as e:
                print(f" Error processing {tan_name}: {e}")
                all_results.append({
                    'tan_name': tan_name,
                    'status': 'error',
                    'error': str(e),
                    'sections_processed': list(section_texts_dict.keys()),
                    'total_text_length': sum(len(v) for v in section_texts_dict.values())
                })
                continue

        # Create summary report
        print(f"\n{'='*70}")
        print("PROCESSING SUMMARY")
        print(f"{'='*70}")

        summary_df = pd.DataFrame(all_results)
        summary_file = f"{output_prefix}_processing_summary.xlsx"
        summary_df.to_excel(summary_file, index=False)

        successful = len([r for r in all_results if r['status'] == 'success'])
        failed = len([r for r in all_results if r['status'] == 'error'])

        print(f"Total tan_names processed: {len(unique_tan_names)}")
        print(f"Successful extractions: {successful}")
        print(f"Failed extractions: {failed}")
        print(f"Summary saved to: {summary_file}")

        if successful > 0:
            print(f"\n Successfully processed {successful} tan_names")
            print(f"   Check individual output directories for detailed results")

        if failed > 0:
            print(f"\n Failed to process {failed} tan_names")
            print(f"   Check the summary file for error details")

    except Exception as e:
        print(f" Error processing Excel file: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Define the topic and sample data
    topic = "Electrochemical, Radiational, and Thermal Energy Technology"

    topic_information = """This topic covers thermal, chemical, biochemical, electrochemical, photochemical, and engineering aspects of energy handling, transport, storage, and conversion, with a focus on non-fossil fuel sources and associated technologies. It includes heat storage and transport systems, such as sensible, latent, and thermochemical storage; solar energy collection, absorption, and pond systems; and heat pump technologies for the utilization of low-grade thermal energy sources. Hydrogen storage, distribution, and transport—both physical and materials-based—are also addressed.

    Energy sources in scope include solar energy, ocean thermal energy, geothermal energy, waste heat recovery, and fuels derived from fermentation, gasification, or pyrolysis of biomass and wastes. Studies involving hydrogen as a fuel, combustion of non-fossil fuels, and the integration of waste heat into usable energy streams are also covered.

    Energy conversion devices and technologies covered by this topic include batteries, fuel cells, solar cells, photoelectrochemical cells, solar thermophotovoltaic devices, thermoelectric devices, thermionic energy converters, magnetohydrodynamic and electrohydrodynamic generators, thermomagnetic energy converters, and other emerging energy conversion systems, including their components and materials.

    The topic further encompasses safety aspects of energy utilization, including thermal system safety, hydrogen safety, battery and electrical safety, and process hazard mitigation for non-fossil fuel systems. Cross-cutting considerations such as system integration, durability, control systems, and environmental impact assessments are also included."""

    sample_keyphrases = [
        "Fuel cell", "Lithium secondary batteries", "Battery cathode", "Fuel cells, polymer electrolyte","Secondary battery", "Battery anode", "Fluoropolymer", "Fuel cell electrolyte", "Solid oxide fuel cell", "Carbon black", "Battery electrolyte", "Solar cell", "proton exchange membrane", "Ionomer","Electric current-potential relationship", "Primary battery", "Carbon fiber"
    ]

    # ========================================================================
    # OPTION 1: Process Excel file with multiple tan_names (NEW FUNCTIONALITY)
    # ========================================================================

    excel_file_path = "dataset_with_title_abstract_982560_updated.xlsx"   
    process_excel_for_unified_extraction(
        excel_file_path=excel_file_path,
        topic=topic,
        topic_information=topic_information,
        sample_keyphrases=sample_keyphrases,
        output_prefix="batch_extraction"
    )

    # # ========================================================================
    # # OPTION 2: Process single document (EXISTING FUNCTIONALITY)
    # # ========================================================================

    # tan = "08130429K.article.002.pdf"


    # section_texts_dict = {
    #     "title": "Vacancy-engineered LiMn2O4 embedded in dual-heteroatom-dopedcarbon via metal-organic framework mediated synthesis towards longevous lithium ion battery",
    #     "abstract": """
    #         Spinel LiMn2O4 (LMO) is deemed to be a promising cathode material for commercial
    #         lithium-ion batteries (LIBs) in prospect of its cost-effectiveness, nontoxicity, fabulous rate capability, and high energy density. Nevertheless, the LMO is inevitably confronted with sluggish diffusion kinetics and drastic capacity degradation triggered by multiple issues, including Jahn Teller distortion, Mn dissolution, and structural attenuation. Thereinto, a metal organic framework (MOF) chemistry engineering for hierarchical micro-/nano-structural F, O-dual-doped carbon embedded oxygen vacancy enriched LiMn2O4 cathode (OV-LMO@FOC) is proposed for longevous LIBs. Bestowed by experimental and theoretical implementations, systematic investigations of OV-LMO@FOC endow that the meticulous integration of F, O-dual-doped carbon and oxygen vacancy in LMO-based cathode reconfigures the electronic structure, boosts electronic conductivity, expedites diffusion capability, facilitates energetically preferable Li+ adsorption, and suppresses Mn dissolution in the electrolyte, consequently achieving fabulous long-term cycling stability. As expected, the OV-LMO@FOC behaves with compelling electrochemical performance with prosperous reversible capacity (130.2 mAh g−1 at 0.2 C upon 200 cycles), exceptional rate capacity (93.7 mAh g−1 even at 20 C), and pronounced long-term cyclability (112.5 mAh g−1 after 1200 cycles with 77.6%
    #         capacity retention at 1 C). Even at the ultrahigh current density of 5 C, the OV-LMO@FOC bears a brilliant capacity of 96.9 mAh g−1 upon 1000 cycles with an extraordinary capacity retention of 90.7%, and maintains a discharge capacity of 70.9 mAh g−1 upon 4000 cycles. This work envisions the MOF-chemistry in surface modification and electronic modulation engineering of high-performance cathode materials towards industrialization in automotive market. Keywords: metal-organic framework, spinel LiMn2O4 cathode, oxygen vacancy engineering,
    #         dual-heteroatom-doped carbon, longevous lithium ion battery
    #         """,
    #     "results": """Figure 1 schematically illustrates the preparation strategy for
    #         MOF-mediated OV-LMO@FOC. Herein, the spherical Mn-
    #         MOF was assembled by trimesic acid and Mn2+, and figure
    #         S1 shows the XRD pattern to confirm the successful fabrication
    #         and phase purity of Mn-MOF. The TGA curve reveals the
    #         thermal stability of Mn-MOF (figure S2). To prepare the OVLMO@
    #         FOC, the Mn-MOF, ammonium fluoride, and lithium
    #         carbonate were homogeneously mixed and further sintering
    #         in argon flow via solid-state sintering strategy, among which
    #         the lithium carbonate and Mn ions of Mn-MOF render as lithium
    #         and manganese source to form the LiMn2O4, respectively.
    #         Additionally, the trimesic acid ligand of Mn-MOF transforms
    #         into the carbon layer and the ammonium fluoride as etching
    #         reagent contributes to the fluorine dopants into the carbon
    #         matrix. The hierarchical MOF-mediated LiMn2O4 embedded
    #         in O-doped carbon (OV-LMO@OC, without NH4F) micro/nanoparticles
    #         and MOF-mediated LiMn2O4 (LMO, without
    #         NH4F and sintering in air flow) were implemented as parallel
    #         experiments to investigate the electrochemical properties
    #         of OV-LMO@FOC.
    #         The morphological and crystal structures of OVLMO@
    #         FOC were observed by the SEM and TEM techniques.
    #         As shown in figures 2(a)–(c), the micro-sized spheres are consisted
    #         of tremendous nano-sized subunits, further leading to
    #         the hierarchical micro-/nano-structure of OV-LMO@FOC.
    #         Worth mentioning, the OV-LMO@FOC well-maintains the
    #         spherical hollow shape of the Mn-MOF template (figure
    #         S3), and has negligible difference with the OV-LMO@OC
    #         after fluoridation (figure S4), which could be ascribed to
    #         the MOF-derived carbon matrix functioning as the template
    #         and structure protector for the Mn-MOF precursor morphology.
    #         The TEM image in figure 2(d) reveals that the hollow
    #         cavity inside micro-sized spheres can be observed in OVLMO@
    #         FOC, demonstrating the hierarchical hollow micro-
    #         /nano-architecture. Furthermore, the nano-sized LiMn2O4
    #         subunits are coated with a ∼1.5 nm carbon layer (figure 2(e)),
    #         which could effectively avoid severe volume variation of LMO
    #         particles and the direct contact between LMO and electrolyte.
    #         Such a hierarchical hollow micro-/nano-architecture
    #         is conducive to improved electronic conductivity, extra
    #         lithium-ion storage sites, and alleviated volumetric expansion
    #         upon (dis)charge course [35]. According to figure S5,
    #         the average particle sizes of the primary LiMn2O4 particle
    #         for OV-LMO@OC and OV-LMO@FOC were calculated as
    #         ∼40.2 nm. Moreover, the inner cavity of the hollow structure
    #         facilitates the suppressed volumetric stress and increased contact
    #         between the electrode materials and electrolyte, in favor
    #         of structural integrity and prolonged cycling stability of active
    #         material [36]. Figure 2(f) presents the high-magnification
    #         TEM (HRTEM) image, where the contiguous lattice fringes
    #         with an interplanar distance of 0.47 nm can be indexed to
    #         the (111) crystal plane of LMO. Disordered regions in the
    #         carbon layer induced by carbon defects are also visible, indicating
    #         the successful heteroatom doping [37]. Additionally, as
    #         depicted in figure 2(g), the ring-like selected area electron
    #         diffraction (SAED) pattern with conspicuous diffraction rings
    #         correspond to the (111), (311), and (400) facets of LiMn2O4 in
    #         OV-LMO@FOC. HAADF scanning TEM (HAADF-STEM)
    #         and elemental mapping images demonstrate the existence and
    #         uniformity of Mn, O, F, and C (figure 2(h)). To precisely
    #         visualize the oxygen vacancies in the LiMn2O4 lattice, the
    #         spherical aberration-corrected HAADF-STEM image of OVLMO@
    #         FOC is disclosed in figure S6, where lattice distortion
    #         and atomic rearrangement triggered by oxygen vacancies
    #         are visible [25].
    #         As disclosed in figure 3(a), the crystallographic configurations
    #         of LMO, OV-LMO@OC, and OV-LMO@FOC were
    #         confirmed by XRD patterns. The characteristic diffraction
    #         peaks can be identified as the cubic spinel LiMn2O4 phase
    #         (JCPDS Card No. 35–0782) with the Fd ¯3 m space group
    #         without impurity. Simultaneously, OV-LMO@FOC characters
    #         have higher diffraction intensity, suggesting a higher degree
    #         of crystallinity than that of LMO and OV-LMO@OC, corresponding
    #         to the Rietveld-refinement results (figures 3(b), S7
    #         and table S1). Figure 3(c) schematically elaborates the crystallographic
    #         configuration of LiMn2O4, where the Li, Mn, and
    #         O are located at 8a, 16d, and 32e sites to construct the contractive
    #         MnO6 octahedral unit and expansive LiO4 tetrahedral
    #         environment. Especially, the ordered Mn2O4 edge-shared
    #         cages provide a three-dimensional open framework channel
    #         for lithium ion diffusion. According to the Raman spectra (figure 3(d)), a vibration peak centered at around 626 cm−1
    #         is designated to the Raman-active modes for LiMn2O4 phase,
    #         and two representative bands appeared at around 1336 and
    #         1593 cm−1 are correlated with the disordered sp3 carbon (Dband)
    #         and graphitic sp2 carbon (G-band), respectively. The
    #         D and G band intensity ratios (ID/IG) for OV-LMO@OC
    #         and OV-LMO@FOC are 0.92 and 1.1, respectively, manifesting
    #         that fluoridation modification induces more defective
    #         sites in the carbon matrix [37]. The Raman spectra preliminarily
    #         substantiate the higher disordered degree caused by fluorine doping, which can also be supported by the
    #         HRTEM observations. Furthermore, the electronic property
    #         of the electrode might be manipulated by both carbon layer
    #         and high electronegative fluorine/oxygen heteroatom-dopants,
    #         consequently propitious to the abundant lithium storage sites,
    #         boosted electronic conductivity, decreased transport resistances,
    #         and alleviated volumetric expansion upon (de)lithiation
    #         processes [24, 29]. Notably, the oxygen-deficient materials
    #         deliver lower peak intensity at 626 cm−1, which is associated
    #         with the ameliorated electronic configuration and boosted
    #         electronic conductivity triggered by the introduction of oxygen
    #         defects [30, 31]. XPS spectra were implanted to investigate
    #         the chemical valence and bonding modes of the LMO,
    #         OV-LMO@OC, and OV-LMO@FOC. Figure S8 records the
    #         survey spectra that all the Li, Mn, O, C, and F elements
    #         coexist in the OV-LMO@FOC samples. As the Li 1 s spectra
    #         elucidated in figure S9, the existence of lithium source
    #         for all samples demonstrates that successful sintering lithiation
    #         and transformation from the Mn-MOF into Li-integrated
    #         LiMn2O4, endowing the valid synthesis of LMO via the MOFmediated
    #         strategy. Additionally, the F 1 s spectrum of OVLMO@
    #         FOC can be deconvoluted into two subpeaks of C−F
    #         (684.3 eV) and CF2 (686.2 eV) bonds due to the F, O-dualdoped
    #         carbon matrix (figure 3(e)), conducive to the manipulation
    #         of electronic structure for OV-LMO@FOC electrode
    #         [38]. The distribution of F-dopants inside the OV-LMO@FOC
    #         was quantitatively investigated by Ar+ etching-assisted XPS
    #         within a depth of 30 nm (figure S10), where the F 1 s peak
    #         of OV-LMO@FOC varies due to the impact of Ar+ etching
    #         treatment. As a result, the peak intensity of F disappears after
    #         Ar+ etching down to 10, 20, and 30 nm. As mentioned earlier
    #         in TEM results, the carbon layer is ∼1.5 nm, which is much
    #         thinner than 10 nm. Therefore, the XPS spectra of F 1 s indicate
    #         that the F etches the carbon layer without penetrating into
    #         the LMO bulk. As depicted in figure 3(f), three O peaks are
    #         detected in the core-level O 1 s spectra in OV-LMO@FOC and
    #         OV-LMO@OC, while two peaks are available in the primitive
    #         LMO. The deconvoluted peaks at 533, 531.5, and 529.5 eV
    #         are ascribed to the existence of C–O–C or C=O, oxygen
    #         vacancy, and Mn–O, respectively, thereby indicating the successful
    #         doping of O into the carbon matrix and the introduction
    #         of oxygen vacancies [39]. Furthermore, the more pronounced
    #         characteristic peaks at 531.5 eV in OV-LMO@FOC and OVLMO@
    #         OC indicate higher oxygen vacancy levels [30]. The
    #         6 fitting core-level Mn 2p spectra hold four characteristic subpeaks,
    #         where the peaks with binding energies appeared at
    #         642.8 and 654.2 eV are correlative with the Mn 2p3/2 and Mn
    #         2p1/2 doublet for Mn4+, and the peaks with binding energies
    #         appeared at 641.2 and 652.7 eV correlate to the Mn 2p3/2 and
    #         Mn 2p1/2 doublet for Mn3+ (figure 3(g)) [19]. To expound the
    #         oxygen vacancies, the electron paramagnetic resonance (EPR)
    #         spectra in figure 3(h) explicitly confirm the oxygen vacancies
    #         in both OV-LMO@OC and OV-LMO@FOC with respect
    #         to the oxygen vacancy signals at g = 1.998. This g-factor
    #         indicates the coexistence of Mn4+ and Mn3+ ions (g = 1.98
    #         for sole LMO) leading to the shift of oxygen vacancy traits
    #         to lower g values [40]. Compared to OV-LMO@OC, OVLMO@
    #         FOC bears the higher intensity and concentration of
    #         oxygen vacancy, which can boost the electronic conductivity
    #         by reconfiguring the electronic structure in LiMn2O4 crystal
    #         lattice and provide extra electrochemical active sites for lithium
    #         ion storage. Additionally, more diffusion pathways can
    #         be provided by vacancies for lithium ions to diffuse into the
    #         bulk. The N2 sorption isothermal identifies the porosity nature
    #         (figure 3(i)), which the Brunauer–Emmett–Teller (BET) specific
    #         surface area takes up 48.4, 73.3, and 107.6 m2 g−1
    #         for LMO, OV-LMO@OC, and OV-LMO@FOC, respectively.
    #         Consequently, F-doping in carbon matrix can introduce the
    #         defective sites in the carbon framework, which is conducive to
    #         more active sites for charge storage. Additionally, the oxygen
    #         vacancies can increase the BET surface area with more lithium
    #         storage sites [41, 42], which can be ascribed to the surface
    #         reconstruction and lattice strain brought by the defect sites in
    #         the crystal structure. Herein, the F-doping and higher oxygen
    #         vacancy concentration contribute to higher BET surface area
    #         of OV-LMO@FOC. Figure S11 discloses the pore-size distribution
    #         of all the samples featuring hierarchical micro-mesomacro-
    #         pores characteristics, which endows extra lithium storage
    #         sites, shortened diffusion channels, efficient electrolyte
    #         infiltration, and boosted charge diffusivity.
    #         To comprehensively evaluate the electrochemical superiority
    #         of OV-LMO@FOC, the as-synthesized cathode materials
    #         were assembled as cathodes in coin cells. Figure 4(a)
    #         depicts the CV profiles of OV-LMO@FOC cathode with the
    #         voltage window of 3−4.5 V (vs. Li+/Li), which holds two
    #         pairs of characteristic redox peaks at around 4.07/3.95 V
    #         and 4.18/4.08 V related to the single-phase transformation of
    #         LiMn2O4/Li0.5Mn2O4 with Li−Li interaction and two-phase
    #         transformation of Li0.5Mn2O4/λ-MnO2 without Li−Li interaction,
    #         indicating the reversible (de)insertion of lithium ions
    #         from/into the tetrahedral sites of spinel structure by two-step
    #         conversion reactions, in which the lithium ion (de)insertion on
    #         8a sites occurs at ∼4 V maintaining the cubic spinel symmetry
    #         [43, 44]. It is noteworthy that the single CV peak for initial
    #         cycle charge of OV-LMO@FOC cathode may be attributed
    #         to the large polarization and the Li-poor phase of fresh OVLMO@
    #         FOC cathode, which the two-phase transformation of
    #         Li0.5Mn2O4/λ-MnO2 dominates at the 1st cycle charge. As
    #         the reversible (de)insertion proceeds, Li-poor phase transforms
    #         into Li-rich phase contributing to two typical redox
    #         peaks [45]. In all cases, the OV-LMO@FOC bears much more
    #         symmetric redox peaks and higher peak currents than that
    #         of LMO and OV-LMO@OC counterparts (figure S12). The
    #         CV profiles are superimposed since the second cycle onward,
    #         demonstrating the superior cycling reversibility of insertion
    #         and extraction of lithium ions. The minimized peak current
    #         of OV-LMO@FOC upon the second cycle can be ascribed
    #         to the cycle aging of cathode. Additionally, no extra redox
    #         peak is observed in all samples, confirming that the oxygen
    #         vacancy does not change the crystal structure and the
    #         lithium storage mechanism of LMO. Figure 4(b) records the
    #         galvanostatic charge-discharge (GCD) curves at the current
    #         density of 0.2 C for OV-LMO@FOC, which obviously features
    #         two potential plateaus at around 4.1 and 4.0 V concerning
    #         two peak pairs in the abovementioned CV results.
    #         Obviously, the OV-LMO@FOC shows the superior discharge
    #         specific capacity than that of LMO and OV-LMO@OC after
    #         the reversible 200th cycle course (figure S13). The corresponding
    #         cycling stability at 0.2 C is illustrated in figure 4(c),
    #         in which the OV-LMO@FOC outperforms other counterparts
    #         with prosperous specific capacity of 130.2 mAh g−1 upon
    #         200 cycles maintaining a capacity retention of 86.2%. It
    #         can be deduced that the enriched oxygen vacancy provides
    #         more extra electroactive sites and improves the practical electrode
    #         usage of OV-LMO@FOC. Additionally, the Coulombic
    #         efficiency of the OV-LMO@FOC approaches to ∼100%
    #         upon prolonged cycling, which the F, O-dual-doped carbonaceous
    #         surface modification effectively suppresses Jahn–
    #         Teller distortion and Mn dissolution induced by the direct
    #         contact between LMO and electrolyte, and thus contributing
    #         to the stable passivation membranes (cathode-electrolyte
    #         interphases, CEIs).
    #         In the interim, the rate capability of OV-LMO@FOC
    #         from low (0.05 C) to ultrahigh (20 C) rates is elaborated in
    #         figure 4(d). Plainly, the discharge capacity of OV-LMO@FOC
    #         exceeds that of LMO and OV-LMO@OC counterparts, with
    #         conspicuous capacity of 146, 147.8, 145.3, 144.3, 140.5, 140,
    #         137.2, 127.6, 120.4, 115.9, and 111.7 mAh g−1 at the upgrading
    #         current density of 0.1, 0.2, 0.4, 0.6, 0.8, 1, 2, 4, 6, 8,
    #         and 10 C, respectively. Even under the harsh current condition
    #         of 20 C, the OV-LMO@FOC achieves a brilliant discharge
    #         capacity of 93.7 mAh g−1, superior to that of LMO
    #         (49.8 mAh g−1) and OV-LMO@OC (72.2 mAh g−1). As
    #         the current density goes back to 0.1 C, OV-LMO@FOC
    #         maintains its initial capacity value, endowing the reversibility
    #         and superiority of lithium storage. The exceptional
    #         rate capability of OV-LMO@FOC could be associated with
    #         oxygen vacancy-induced boosted electronic conductivity and
    #         pseudocapacitive-controlled storage, as well as conductive
    #         F, O-dual-doped carbon matrix for elastic buffer layer for
    #         ameliorated structural disintegrity under high current density,
    #         which further facilitates the fast diffusion kinetics.
    #         Figures 4(e) and (f) illuminate the prolonged cycle performances
    #         at 1 C and 5 C for LMO, OV-LMO@OC, and
    #         OV-LMO@FOC cathodes, respectively. Impressively, OVLMO@
    #         FOC features an inciting long-term cyclability of
    #         112.5 mAh g−1 with 77.6% capacity retention at 1 C after
    #         1200 cycles (figure 4(e)), which is much higher than those of
    #         LMO (58 mAh g−1 with capacity retention of 54.2%) and OVLMO@
    #         OC (76.1 mAh g−1 with capacity retention of 66.6%).
    #         Furthermore, the high-temperature cycle performances under
    #         55 ◦C were investigated at the current rate of 1 C (Fig. S14),
    #         in which the OV-LMO@FOC shows superior electrochemical
    #         performance than that of LMO and OV-LMO@OC. The OVLMO@
    #         FOC shows a distinguished long-term cyclability of
    #         119.8 mAh g−1 with 74.4% capacity retention at 1 C after 1000
    #         cycles, much higher than that of LMO (66.2 mAh g−1) and
    #         OV-LMO@OC (105.7 mAh g−1). Even at the ultrahigh current
    #         density of 5 C, the OV-LMO@FOC bears a brilliant capacity
    #         of 96.9 mAh g−1 upon 1000 cycles with an extraordinary
    #         capacity retention of 90.7%, and maintains the discharge
    #         capacity of 70.9 mAh g−1 after 4000 cycles, manifesting
    #         the longevous cyclic stability and reversibility. However,
    #         severe capacity deterioration can be observed for both LMO
    #         (49.1 mAh g−1) and OV-LMO@OC (81.5 mAh g−1) after
    #         1000 cycles at 5 C, which highlights the benefits from both
    #         structural optimization of micro-/nano- multiscale structure
    #         and compositional optimization of surface modification and
    #         vacancy engineering. In brief, the distinguished electrochemical
    #         performance of OV-LMO@FOC can be credited to synergistic
    #         effects in what follows. (I) The OV considerably
    #         boosts electronic conductivity, provides extra electroactive
    #         sites, suppresses structural deformation, increases pseudocapacitive
    #         storage, and minimizes energy barrier upon reversible
    #         cycling, consequently inducing boosted diffusion kinetics,
    #         improved discharge capacity, as well as fabulous cycling
    #         stability and rate capability [46, 47]. (II) The F, O-dual-doped
    #         carbon matrix facilitates the overall conductivity of the electrode,
    #         mitigates electroactive particle agglomeration, immobilizes
    #         the structure expansion, and impedes the direct contact
    #         between LMO and corrosive electrolyte by jeopardizing Mn3+
    #         disproportionation and Jahn–Teller effect, thus contributing to
    #         the structural stability towards long-term endurance [48]. (III)
    #         The multi-hierarchy hollow micro-/nano-structure offers extra
    #         electroactive sites and diffusion pathways by the inner surfaces
    #         of cavity, and relieves electrode polarization by the direct Li+
    #         diffusion in outer shells instead of diffusion from outer shells
    #         to inner cores [49]. Nevertheless, the hierarchical secondary
    #         micro-/nano-structure can effectively alleviate the volume
    #         expansion stress by the hollow cavity and secondary nanobuilding
    #         block, where the stress-release behavior optimizes the
    #         structural integrity for long-term cycling and rate performance
    #         [50, 51]. To examine the structural integrity during cycling,
    #         the SEM image of OV-LMO@FOC after 4000 cycles at 5 C
    #         is disclosed in Fig. S15, where mechanical cracks and can
    #         hardly be detected and the primitive particle morphology is well retained, thereby indicating the stress-release effect of
    #         the heteroatom-doped carbon matrix. Benefitting from the
    #         pronounced structural/compositional optimizations, the rate
    #         capability and cycling property of OV-LMO@FOC outperform
    #         presently reported LMO-based cathodes in LIBs, as illustrated
    #         in figure S16 and table S2.
    #         As for the lithiation process, there are two lithium storage
    #         kinetics contributing to the total storage capacity: (a) numerous
    #         Li+ ions adsorb around the surface of the F, O-dualdoped
    #         carbonaceous layer and negative electrons theoretically
    #         accumulate on the opposite interface of the carbon layer resulting
    #         in a typical electric double layer capacitor for exceptional
    #         specific capacity (termed as pseudocapacitive-dominant
    #         mechanism) [51]; (b) another part of Li+ ions diffuse through
    #         the F, O-dual-doped carbonaceous layer and electrochemically
    #         participate in the reversible (de)insertion of lithium ions
    #         from/into the tetrahedral sites of LiMn2O4 spinel structure by
    #         two-step conversion reactions (denoted as diffusion-controlled
    #         mechanism) [43, 44]. To quantitative study the boosted diffusion
    #         kinetics of OV-LMO@FOC cathode, figures 5(a)–(c)
    #         record the CV profiles at different scan rates to identify the
    #         pseudocapacitive contribution for lithium storage kinetics.
    #         Obviously, the CV profiles bear similar shape with upgrading
    #         current intensity upon scan rates varying from 0.2 to
    #         0.6 mV s−1. To differentiate the pseudocapacitive contribution
    #         ratio, the peak current (i) was logarithmically fitted against
    #         the scan rates (v) using the equation of i = avb [52]. It is
    #         acknowledged that a pseudocapacitive-dominant or diffusioncontrolled
    #         mechanism can be observed when the value of b
    #         quals to 1 or 0.5, respectively. As depicted in figure 5(d), the
    #         b values of OV-LMO@FOC (0.68 for cathodic peak and 0.77
    #         for anodic peak) confirm that the pseudocapacitive-dominant
    #         and diffusion-controlled mechanisms concurrently contribute
    #         to the lithium storage course, which are higher than those of
    #         LMO (0.54 for cathodic peak and 0.67 for anodic peak, figure
    #         S17)(a)) and OV-LMO@OC (0.66 for cathodic peak and 0.67
    #         for anodic peak, figure S18(a)). Furthermore, the pseudocapacitive
    #         contribution (k1ν) and the diffusion-controlled contribution
    #         (k2ν1/ 2) can be quantitatively separated by the law
    #         of i(V) = k1ν + k2ν1/ 2 [52]. The pseudocapacitive contribution
    #         proportion takes up 86.1% at 0.5 mV s−1 for OVLMO@
    #         FOC (figure S19). The pseudocapacitive contribution
    #         ratios of OV-LMO@FOC increases as the scan rates increase
    #         (from 79.9% to 88.8%, figure 5(e)). It is of note that the pseudocapacitive-dominant kinetics of OV-LMO@FOC is larger
    #         than that of LMO (figure S17(c)) and OV-LMO@OC
    #         (figure S18(c)). It may be attributed to the abundant oxygen
    #         vacancy and elastic F, O-dual-doped carbon matrix for pseudocapacitive
    #         storage, boosted electronic conductivity, large specific
    #         surface area, and extra active sites towards the fabulous
    #         rate capability of OV-LMO@FOC.
    #         Simultaneously, the lithium-ion diffusion coefficient
    #         (DLi+) of OV-LMO@FOC was investigated by the galvanostatic
    #         intermittent titration technique (GITT) [53], as portrayed
    #         in figure S20. The detailed calculation method of the
    #         DLi+ value was depicted in figure S21. Figures 5(f) and (g)
    #         illustrate the DLi+ value of all the LMO, OV-LMO@OC, and
    #         OV-LMO@FOC electrodes upon lithiation and delithiation
    #         processes, respectively. It can be observed in the DLi+ profiles
    #         with a sharp drop upon the (dis)charge voltage platform
    #         section and an increase upon depth (dis)charge stage. Usually,
    #         structural/phase transformations occur upon platform region
    #         with complex redox reactions, leading to an abnormal DLi+
    #         [54]. Distinctly, as the (de)lithiation proceeds, the calculated
    #         DLi+ value for OV-LMO@FOC is numerically higher than that
    #         of LMO and OV-LMO@OC, demonstrating that the doping F
    #         in carbon matrix and oxygen vacancy could facilitate the boosted
    #         diffusion kinetics. Figure 5(h) presents the EIS analysis
    #         to study the resistance for interfacial reactions. The Nyquist
    #         plots are composed by depressed semicircles in the highand
    #         medium-frequency regime related to the charge-transfer
    #         resistance (Rct), and oblique line in low-frequency regime
    #         corresponded to the Warburg impedance (Ws) for lithium ion
    #         diffusion [55]. Accordingly, the OV-LMO@FOC bears smaller
    #         Rct and Ws than those of LMO and OV-LMO@OC, affirming
    #         fast charge transfer and lithium ion diffusion kinetics of
    #         OV-LMO@FOC. The heteroatom-doped carbon matrix constitutes
    #         a successive conductive network, while oxygen vacancies
    #         modulate the local charge distribution, thereby favoring
    #         the overall electron transportation [23, 25, 29]. Therefore, it
    #         can be credited with the modulation of electronic conductivity
    #         induced by the F, O-dual-doped carbonaceous matrix
    #         and oxygen vacancies. Simultaneously, the DLi+ value can be
    #         calculated by the relationship of DLi+=R2T2/2A2n4F4c2σ2,
    #         where the Warburg coefficient (σ) is in inverse proportion to
    #         the DLi+ in low frequency regime [56]. Consistent with the
    #         GITT results, the OV-LMO@FOC features the lowest σ value
    #         (13.8) than that of LMO (442.4) and OV-LMO@OC (184.4),
    #         whereas OV-LMO@OC endows the highest DLi+ (figure 5(i)).
    #         Figure 6 comprehensively deepens the boosted lithium-ion
    #         diffusivity and fabulous rate capability of OV-LMO@FOC.
    #         In terms of the primitive LiMn2O4 crystal lattice, it can be
    #         observed the conducive charge transfer phenomenon and lopsided
    #         imbalanced charge distribution alongside the OV regime
    #         (figure 6(a)), which synchronously incurs the surrounding
    #         atoms skewing ascribed to the oxygen defects, further inducing
    #         a positively charged regime in the oxygen vacancy center
    #         and an inversely negative region alongside the OV center [57].
    #         Whilst, the spontaneous formation of the locally built-in electric
    #         field around oxygen vacancy centers is commensurate with
    #         the Coulomb attractive force towards boosting lithium-ion
    #         diffusivity and thus kinetically facilitating rate performance.
    #         Upon lithiation course, the local electric field by pointing from
    #         the OV-free region to the negatively charged region expedites
    #         the lithium-ion diffusion and leads to Li+-agglomerated
    #         regime alongside the vacancy sites, as depicted in figure 6(b).
    #         The primitive negatively charged region will bear the electric
    #         neutrality as fully discharging. When the delithiation proceeds
    #         (figure 6(c)), the lithium ions migration is facilitated by
    #         the secondary local electric field directing from the positively
    #         charged region in the vacancy regime to the electroneutral lithiation
    #         region. Intriguingly, it can be expounded that the oxygen
    #         vacancy induced in-plane built-in electric field with the in-situ
    #         Li+ migratory engineering is indispensable for boosting Li+
    #         diffusivity for superior rate capability.
    #         DFT calculation was employed to unveil the synergistic
    #         effect on the superior electrochemical performance of OVLMO@
    #         FOC. The optimized geometric models of LMO,
    #         LMO@C, LMO@FC, LMO@OC, LMO@FOC, and OVLMO@
    #         FOC are shown in figure S22–S27, respectively.
    #         Figures 7(a)–(c) and S26 illustrate the density of states
    #         (DOS) together with the Fermi level (Ef), which reveals the
    #         electronic nature of the above-mentioned optimized models.
    #         The electronic property of LMO bulk behaves the discernible
    #         semiconductive characteristic with band-gap of 0.63 and
    #         0.49 eV (figure 7(a)). Nevertheless, the band-gap decreases
    #         to 0.17 eV after combining the LMO with pure carbon
    #         (LMO@C) as shown in figure 7(b), which could be ascribed
    #         10 to the electronic conductivity of carbon. As the carbon doped
    #         with F and O, the metallicity of LMO@FC, LMO@OC,
    #         LMO@FOC, and OV-LMO@FOC samples can be observed
    #         along with the conspicuous and continuous Ef through the
    #         conduction bands, as well as the band-gap gradually dissipates
    #         (figures 7(c) and S28), where the most electronegative F
    #         (χ=3.98) and O (χ=3.44) heteroatoms can in-depth regulate
    #         the surface defects and electron structure to decrease the interfacial
    #         resistance and induce more available active electrons in
    #         the carbon coated heterostructure, further facilitating boosted
    #         electronic conductivity [58]. It is of note that the introduction
    #         of F and O dual-dopants in carbonaceous matrix induce the
    #         much denser DOS around the Fermi level, confirming that the
    #         F and O dual-dopants and conductive carbon are conducive
    #         to the boosted electronic conductivity of electrode materials.
    #         Additionally, the conduction bands of OV-LMO@FOC are
    #         predominantly composed of Mn 3d states, while the valence
    #         bands are prominently taken up by C 2p, O 2p, and F 2p
    #         states. Consequently, OV-LMO@FOC exerts a considerable
    #         effect on improved electronic conductivity comparable to that
    #         of LMO, thus contributing to the boosted Li+ diffusion kinetic
    #         and prominent electrochemical capability.
    #         The Li atom migration mechanism and corresponding
    #         migration paths are presented in figures 7(d) and S29, where
    #         the boosted Li atom diffusivity contributes to extraordinary
    #         rate capability [59]. To investigate the explicit role of
    #         oxygen vacancy and F, O-dual-doped carbonaceous matrix,
    #         the Li atom migration path that diffuses along the interface between F, O-dual-doped carbonaceous matrix and LMO bulk
    #         were considered and is schematically depicted in the structural
    #         models of LMO, LMO@C, LMO@FC, LMO@OC,
    #         LMO@FOC, and OV-LMO@FOC (figure 7(d)). The diffusion
    #         energy curves in figure 7(e) endow the migration energy barrier
    #         of LMO@C (0.43 eV) is distinctly lower than that of LMO
    #         bulk (0.53 eV), illustrating the optimized migration barriers
    #         and boosted migration kinetics of electrode by carbonaceous
    #         encapsulation. Moreover, migration energy barrier gradually
    #         decreases as F and O dual-doped into the carbon matrix
    #         (0.36 eV for LMO@FC, 0.31 eV for LMO@OC, 0.24 eV
    #         for LMO@FOC), indicating the high electronegativity of F
    #         is energetically favorable for accelerating electrode/electrolyte
    #         interactions by surface functionalization of carbon [60].
    #         In addition, the C−F and C−O covalent bonds improve
    #         the electrode wettability and electrolyte permeation, further
    #         endowing accelerated charge migration [37]. Interestingly, by
    #         introducing the oxygen vacancy into the LMO bulk, the migration
    #         energy barrier of OV-LMO@FOC (0.20 eV) is superior
    #         to that of LMO@FOC (0.24 eV), demonstrating that the introduction
    #         of OV induces the local built-in electric field and confers
    #         the boosted lithium ion diffusivity and decreased energy
    #         barrier upon repeated (de)lithiation course, further decisive for
    #         fabulous rate capability.
    #         The adsorption energies (ΔEads) with different amounts
    #         of lithium ion were systematically investigated to reveal the
    #         F, O-dual-doped carbonaceous matrix and oxygen vacancy
    #         induced distinguished lithium storage mechanism. Four possible
    #         Li adsorption conditions of LMO bulk (figures 8(a) and
    #         S30) models were considered, including one/two/three/four Li ions adsorb on the intralayer of LMO bulk as Site I/II/III/IV,
    #         respectively. Likewise, there are our possible Li adsorption
    #         conditions of LMO@C (figures 8(b) and S31), LMO@FOC
    #         (figures 8(c) and S32), and OV-LMO@FOC (figures 8(d) and
    #         S33) similar to the Li adsorption sites of LMO bulk. Upon
    #         the Li adsorbs on Site I, II, II, and IV, the adsorption energies
    #         of OV-LMO@FOC are observed as−2.52,−1.73,−0.71,
    #         and −0.54 eV, respectively, which outperforms than those
    #         of LMO@C (−1.60 eV for Site I, −1.04 eV for Site II,
    #         −0.43 eV for Site III, and −0.27 eV for Site IV, respectively)
    #         and LMO@FOC (−2.01 eV for Site I, −1.63 eV for
    #         Site II, −0.53 eV for Site III, and −0.31 eV for Site IV,
    #         respectively); whereas those of LMO bulk are observed as
    #         −1.03, −0.77, −0.25, and −0.12 eV, accordingly. As recorded
    #         in figure S34, the adsorption energies of various adsorption
    #         sites of OV-LMO@FOC conformably increase at corresponding
    #         adsorption sites, highlighting that the synergistic
    #         effect on oxygen vacancy and F, O-dual-doped carbonaceous
    #         matrix is energetically favorable for Li+ adsorption and contributes
    #         to extra lithium ion active sites and boosted diffusion
    #         kinetics. Systematically, the FOC layer and oxygen
    #         vacancy are conducive to manipulate the electronic property
    #         of materials, exalt electrical conductivity, along with energetically
    #         favor Li+ adsorption, further boosting diffusion kinetics
    #         and rate performance. To further support the practicability
    #         of the OV-LMO@FOC cathode, commercial graphite was
    #         employed as the anode to assemble LIB full cells. As illustrated
    #         in figure S35, the as-assembled full cell exhibited satisfactory
    #         stability at 1 C (110.2 mAh g−1 with 87.7% capacity
    #         retention after 150 cycles), thereby indicating the feasibility
    #         and compatibility of the OV-LMO@FOC cathode in practical
    #         battery systems [61, 62]. """,
        
    #     "figure_caption": """Figure 1. Schematic illustration of the synthetic strategy for  hierarchical spinel LiMn2O4 cathode embedded in dual-heteroatom-doped carbon. Figure 2. (a)–(c) SEM images of OV-LMO@FOC. (d), (e) TEM images, (f) HRTEM image, (g) SAED pattern, and (h) HAADF-STEM and the relative elemental mapping images for Mn, O, F, and C of OV-LMO@FOC. Figure 3. (a) XRD patterns of the as-synthesized LMO, OV-LMO@OC, and OV-LMO@FOC. (b) Rietveld refinement of the OV-LMO@FOC. (c) Schematical illustration for the crystal structures of spinel LiMn2O4. (d) Raman spectra of the LMO, OV-LMO@OC, and OV-LMO@FOC. High-resolution XPS spectra of (e) F 1 s, (f) O 1 s, and (g) Mn 2p. The (h) EPR spectra and (i) nitrogen de/adsorption isotherms of the as-prepared LMO, OV-LMO@OC, and OV-LMO@FOC. Figure 4. (a) The CV profiles with a scan rate of 0.1 mV s−1 of OV-LMO@FOC cathode. (b) The galvanostatic charge/discharge curves at 0.2 C of OV-LMO@FOC. (c) The cycling performance at 0.2 C, (d) rate capability at different current densities, and long-term cyclability at (e) 1 C and (f) 5 C for LMO, OV-LMO@OC, and OV-LMO@FOC cathodes, respectively. Figure 5. CV curves of (a) LMO, (b) OV-LMO@OC, and (c) OV-LMO@FOC at different scan rates increasing from 0.2 to 0.6 mV s−1 for quantitative kinetics analysis of the pseudocapacitive contribution. (d) The fitting b values of main cathodic (I) and anodic (II) peaks, and (e) pseudocapacitive contribution at different scan rates. The calculated Li+ diffusion coefficients upon (f) lithiation, and (g) delithiation of LMO, OV-LMO@OC, and OV-LMO@FOC electrodes by GITT analysis. (h) The Nyquist plots (inset: equivalent circuit diagram) and (i) corresponding fitting relationship of Z’ and ω −1/2 for LMO, OV-LMO@OC, and OV-LMO@FOC cathodes, respectively. Figure 6. (a) The diagrammatic sketch of charge transfer phenomenon alongside OV regime. The boosted lithium ion diffusivity mechanism induced by locally built-in electric fields upon (b) lithiation and (c) delithiation. Figure 7. The DOS plots of (a) LMO, (b) LMO@C, and (c) OV-LMO@FOC. The Fermi levels (Ef) are set to be 0 eV. (d) The calculated Li atom migration paths of LMO (d1), LMO@C (d2), LMO@FC (d3), LMO@OC (d4), LMO@FOC (d5), and OV-LMO@FOC (d6) for Li atom migration alongside the depicted path, respectively. The optimized Li atom migration paths are indicated by series of aquamarine spheres. (e) The corresponding Li atom migration energy barrier curves. Figure 8. The different lithium ion adsorption sites alongside the side views and corresponding adsorption energies for (a) LMO, (b) LMO@C, (c) LMO@FOC, and (d) OV-LMO@FOC, respectively.""", 

    #     "experimental": """The OV-LMO@FOC was prepared via a Mn-MOFmediated
    #         sintering treatment methodology. 1 g of Mn-MOF,
    #         0.12 g of Li2CO3, and 0.12 g of NH4F were uniformly mixed
    #         for 10 min in a mortar. Afterwards, the grounded precursors
    #         were sintered at 500 ◦C for 4 h under Ar atmosphere with a
    #         temperature ramping rate of 2 ◦C min−1 in a tubular furnace,
    #         and then sintered at 800 ◦C under Ar atmosphere with a temperature
    #         ramping rate of 4 ◦C min−1 for another 2 h. After
    #         naturally cooling to room temperature, the OV-LMO@FOC
    #         was obtained.
    #         Additionally, the OV-LMO@OC was prepared in the similar
    #         way without adding NH4F. Whereas the LMO was synthesized
    #         by sintering the mixture of Mn-MOF and Li2CO3 at
    #         500 ◦C for 4 h in air, and then sintered at 800 ◦C under Ar
    #         atmosphere with a temperature ramping rate of 4 ◦C min−1
    #         for another 2 h.
    #         2.2. Morphological and structural characterization
    #         The crystal structure and phase information were collected
    #         by x-ray diffraction (XRD, Ultima IV, Cu Kα radiation)
    #         using a scanning angle of 10−90◦. The nitrogen adsorption/
    #         desorption isotherms (Micromeritics ASAP 2010, USA)
    #         at 77 K were conducted to specific surface area and pore size.
    #         Thermogravimetric analysis (TGA, NETZSCH TG 209 F1
    #         Libra) from 30 to 800 ◦C with a heating rate of 10 ◦C min−1
    #         under N2 atmosphere. The elemental configuration and chemical
    #         valence states were characterized via x-ray photoelectron
    #         spectroscopy (XPS, Thermo Scientific K-Alpha+) using Al
    #         Kα x-ray source. The morphological structure was obtained
    #         by Raman spectra (Renishaw inVia) via an argon ion laser
    #         beam. Surface morphology and microstructure were observed
    #         by scanning electron microscopy (SEM, FEI Quanta 250
    #         FEG) and transmission electron microscopy (TEM, FEI Talos
    #         F200X) with high-angle annular dark-field (HAADF) STEM
    #         and energy dispersive x-ray spectrometer.2.3. Electrochemical measurements
    #         The active material, acetylene black, and polyvinylidene fluoride
    #         were mixed in the mass ratio of 7:2:1, and then decentralized
    #         in the appropriate amount of N-methylpyrrolidone solvent
    #         to prepare a syrupy mixture, which was further coated on
    #         Al foil current collector as cathode films and further dried
    #         at 110 ◦C in the vacuum oven for 20 h. Lithium foil was
    #         employed as the counter-electrode, Celgard 2500-type membrane
    #         was applied as the separator, and 1 M LiPF6 dissolved
    #         in a mixture of ethylene carbonate, dimethyl carbonate,
    #         and diethyl carbonate organic solvents (EC: DMC: DEC,
    #         1: 1: 1, vol%) was functioned as the electrolyte for cell
    #         assembly. The electrochemical properties of OV-LMO@FOC,
    #         OV-LMO@OC, and LMO cathodes were investigated by the
    #         assembled CR2032 coin-type cells, where the assemble process
    #         of the cells was conducted in the argon-filled glove
    #         box (Mikrouna, H2O ⩽ 0.1 ppm, O2 ⩽ 0.1 ppm). The mass
    #         loading of the active material for typical electrodes was
    #         ∼2.54 mg cm−2. Afterwards, the galvanostatic (dis)charge (GCD) cycling tests were systematically performed by LAND 3001 A battery testing system at room-temperature within
    #         the potential range of 3−4.5 V at various rate densities
    #         (1 C = 148 mAh g−1). The cyclic voltammetry (CV) with
    #         different sweep rates between 3−4.5 V and the electrochemical
    #         impedance spectroscopy (EIS) in the frequency
    #         range from 105 kHz to 10−2 Hz with the AC signal amplitude
    #         of 5 mV at room temperature were measured via the
    #         electrochemical workstation (CHI760E).""",

    #     "conclusion": """
    #         In closing, by means of MOF chemistry and anionic defect
    #         engineering, we have successfully developed hierarchical F,
    #         O-dual-doped carbon embedded oxygen vacancy enriched
    #         LiMn2O4 as longevous cycling-stable cathode. The OVLMO@
    #         FOC achieves distinguished electrochemical properties
    #         concerning exceptional specific capacity (130.2 mAh g−1
    #         at 0.2 C after 200 cycles), prosperous rate capability
    #         (93.7 mAh g−1 even at 20 C), pre-eminent long-term cyclability
    #         (112.5 mAh g−1 after 1200 cycles with 77.6% capacity
    #         retention), and ultralong cycling stability (even at 5 C,
    #         96.9 mAh g−1 upon 1000 cycles with a compelling capacity
    #         retention of 90.7%, and 70.9 mAh g−1 upon 4000 cycles).
    #         Unraveled by experimental and theoretical implementations,
    #         both oxygen vacancy and F, O-dual-doped carbon reconfigure
    #         the electronic structure of OV-LMO@FOC, endowing
    #         enhanced electronic conductivity, tremendous active centers,
    #         expedited diffusion kinetics, easy Li+ adsorption, and suppressed
    #         Mn dissolution in the electrolyte, further achieving
    #         unparalleled rate capability and ultralong cyclability. This
    #         work broadens horizons for meticulous engineering for MOFchemistry
    #         in surface modification and electronic modulation
    #         towards longevous cycling-stable cathode materials."""
        
        
    #     }

    # # section_texts_dict = clean_dict_values(section_texts_dict)
    
    # # Run the unified keyphrase extraction pipeline
    # run_unified_keyphrase_extraction(
    #     topic=topic,
    #     topic_information=topic_information,
    #     sample_keyphrases=sample_keyphrases,
    #     section_texts_dict=section_texts_dict
    # )


