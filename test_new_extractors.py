#!/usr/bin/env python3
"""
Test script for the new PositionRank and TextRank extractors
Verifies that they are properly integrated into the unified keyphrase extractor.
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor

def test_new_extractors():
    """Test the newly added PositionRank and TextRank extractors."""
    
    print("🧪 TESTING NEW EXTRACTORS: PositionRank & TextRank")
    print("=" * 60)
    
    # Sample text for testing
    sample_text = """
    Renewable energy systems have become increasingly important for sustainable development. 
    Solar energy and wind energy are the most widely deployed renewable technologies. 
    Geothermal energy offers consistent baseload power generation capabilities. 
    Energy storage systems are crucial for grid stability and reliability. 
    Advanced control algorithms optimize energy efficiency in smart grid applications.
    """
    
    # Sample sections for comprehensive testing
    sections_texts_dict = {
        "title": "Advanced Renewable Energy Systems for Smart Grid Applications",
        "abstract": """
        This research investigates advanced renewable energy systems integrated with 
        smart grid technologies. The study focuses on optimizing energy efficiency 
        through intelligent control algorithms and energy storage solutions.
        """,
        "methods": """
        We analyzed various renewable energy configurations including solar panels, 
        wind turbines, and geothermal systems. Energy storage technologies were 
        evaluated for grid integration. Smart grid algorithms were developed for 
        optimal energy management and distribution.
        """,
        "results": """
        The integrated renewable energy system achieved 92% efficiency. Solar energy 
        contributed 50% of total generation. Wind energy provided 30% of power output. 
        Geothermal energy supplied 20% baseload power. Energy storage improved grid 
        stability by 65%. Smart grid algorithms reduced energy waste by 25%.
        """
    }
    
    topic = "Renewable Energy and Smart Grid Technologies"
    topic_information = """
    Research on renewable energy systems, smart grid technologies, energy storage, 
    grid integration, energy efficiency optimization, and sustainable power generation.
    """
    
    print(f"📄 TEST CONFIGURATION:")
    print(f"Sample text length: {len(sample_text)} characters")
    print(f"Sections: {list(sections_texts_dict.keys())}")
    print(f"Topic: {topic}")
    
    try:
        # Initialize the unified extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Check which extractors were initialized
        print(f"\n📊 INITIALIZED EXTRACTORS:")
        for extractor_name in extractor.extractors.keys():
            print(f"  ✅ {extractor_name}")
        
        # Check if new extractors are available
        new_extractors = ['position_rank', 'textrank']
        available_new_extractors = [name for name in new_extractors if name in extractor.extractors]
        missing_new_extractors = [name for name in new_extractors if name not in extractor.extractors]
        
        if available_new_extractors:
            print(f"\n🎉 NEW EXTRACTORS AVAILABLE:")
            for extractor_name in available_new_extractors:
                print(f"  ✅ {extractor_name}")
        
        if missing_new_extractors:
            print(f"\n⚠️  NEW EXTRACTORS NOT AVAILABLE:")
            for extractor_name in missing_new_extractors:
                print(f"  ❌ {extractor_name}")
        
        # Test individual extractors first
        print(f"\n🔍 TESTING INDIVIDUAL EXTRACTORS:")
        
        # Test PositionRank
        if 'position_rank' in extractor.extractors:
            try:
                print(f"\nTesting PositionRank...")
                position_rank_keywords = extractor.extractors['position_rank'].extract(sample_text)
                print(f"  PositionRank extracted {len(position_rank_keywords)} keywords")
                print(f"  Sample keywords: {position_rank_keywords[:5]}")
            except Exception as e:
                print(f"  ❌ PositionRank test failed: {e}")
        
        # Test TextRank
        if 'textrank' in extractor.extractors:
            try:
                print(f"\nTesting TextRank...")
                textrank_keywords = extractor.extractors['textrank'].extract(sample_text)
                print(f"  TextRank extracted {len(textrank_keywords)} keywords")
                print(f"  Sample keywords: {textrank_keywords[:5]}")
            except Exception as e:
                print(f"  ❌ TextRank test failed: {e}")
        
        # Update configuration with topic information
        print(f"\n📝 Updating configuration...")
        extractor.config["extractors"]["llama"]["topic_name"] = topic
        extractor.config["extractors"]["llama"]["topic_info"] = topic_information
        
        # Test full unified extraction
        print(f"\n🚀 TESTING FULL UNIFIED EXTRACTION...")
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=sections_texts_dict,
            use_cleaned_text=True
        )
        
        if not df.empty:
            print(f"\n✅ EXTRACTION SUCCESSFUL!")
            print(f"📊 Results Summary:")
            print(f"  Total keyword instances: {len(df)}")
            print(f"  Unique keywords: {df['keyword'].nunique()}")
            print(f"  Extractors used: {', '.join(df['extractor'].unique())}")
            
            # Show results by extractor
            print(f"\n🎯 RESULTS BY EXTRACTOR:")
            extractor_counts = df['extractor'].value_counts()
            for extractor_name, count in extractor_counts.items():
                unique_count = df[df['extractor'] == extractor_name]['keyword'].nunique()
                print(f"  {extractor_name}: {count} instances, {unique_count} unique keywords")
                
                # Show sample keywords for new extractors
                if extractor_name in ['position_rank', 'textrank']:
                    sample_keywords = df[df['extractor'] == extractor_name]['keyword'].unique()[:3]
                    print(f"    Sample: {', '.join(sample_keywords)}")
            
            # Check if new extractors contributed
            new_extractor_results = df[df['extractor'].isin(['position_rank', 'textrank'])]
            if not new_extractor_results.empty:
                print(f"\n🎉 NEW EXTRACTORS CONTRIBUTED:")
                print(f"  Total contributions: {len(new_extractor_results)} keyword instances")
                print(f"  Unique keywords from new extractors: {new_extractor_results['keyword'].nunique()}")
                
                # Show top keywords from new extractors
                top_new_keywords = new_extractor_results['keyword'].value_counts().head(5)
                print(f"  Top keywords from new extractors:")
                for keyword, count in top_new_keywords.items():
                    print(f"    • {keyword} ({count} times)")
            else:
                print(f"\n⚠️  New extractors did not contribute any keywords")
            
            # Perform consensus analysis
            print(f"\n🔍 CONSENSUS ANALYSIS WITH NEW EXTRACTORS:")
            from unified_keyphrase_extractor import analyze_keyphrase_consensus
            
            consensus_df = analyze_keyphrase_consensus(df)
            
            # Check if new extractors participate in consensus
            consensus_with_new = 0
            for _, row in consensus_df.iterrows():
                if any(extractor in row['extractors'] for extractor in ['position_rank', 'textrank']):
                    consensus_with_new += 1
            
            print(f"  Keyphrases with new extractor consensus: {consensus_with_new}")
            print(f"  Total consensus keyphrases: {len(consensus_df)}")
            
            if consensus_with_new > 0:
                print(f"  New extractors participate in {consensus_with_new}/{len(consensus_df)} consensus decisions")
            
            # Save results
            output_file = "test_new_extractors_results.xlsx"
            df.to_excel(output_file, index=False)
            print(f"\n💾 Results saved to: {output_file}")
            
            return True
            
        else:
            print("⚠️  No keywords extracted")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extractor_availability():
    """Test if the new extractor modules can be imported and initialized."""
    print(f"\n🔧 TESTING EXTRACTOR MODULE AVAILABILITY")
    print("=" * 50)
    
    # Test PositionRank
    try:
        from src.extractors.position_rank_extractor import PositionRankExtractor
        position_rank = PositionRankExtractor(top_n=5)
        test_text = "Renewable energy systems provide sustainable power generation."
        keywords = position_rank.extract(test_text)
        print(f"✅ PositionRank module: Working ({len(keywords)} keywords extracted)")
    except Exception as e:
        print(f"❌ PositionRank module: Failed - {e}")
    
    # Test TextRank
    try:
        from src.extractors.suma_extractor import TextRankExtractor
        textrank = TextRankExtractor(top_n=5)
        test_text = "Renewable energy systems provide sustainable power generation."
        keywords = textrank.extract(test_text)
        print(f"✅ TextRank module: Working ({len(keywords)} keywords extracted)")
    except Exception as e:
        print(f"❌ TextRank module: Failed - {e}")

if __name__ == "__main__":
    print("🚀 NEW EXTRACTORS INTEGRATION TEST")
    print("=" * 60)
    
    # Test module availability first
    test_extractor_availability()
    
    # Test full integration
    success = test_new_extractors()
    
    if success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ PositionRank and TextRank extractors are successfully integrated")
        print(f"✅ Unified extraction pipeline is working correctly")
        print(f"✅ New extractors contribute to consensus analysis")
        
        print(f"\n📋 SUMMARY:")
        print(f"• PositionRank: Graph-based keyphrase extraction using position information")
        print(f"• TextRank: Gensim-based TextRank algorithm for keyword extraction")
        print(f"• Both extractors are now part of the unified pipeline")
        print(f"• Semantic filtering applies to all extractors including new ones")
        print(f"• Consensus analysis includes results from all extractors")
        
    else:
        print(f"\n❌ TESTS FAILED")
        print(f"Please check the error messages above and ensure:")
        print(f"  • Required dependencies are installed (spacy, pytextrank, gensim)")
        print(f"  • spaCy English model is available: python -m spacy download en_core_web_sm")
        print(f"  • All extractor modules are properly implemented")
    
    print(f"\n📖 USAGE:")
    print(f"The new extractors are automatically included when using:")
    print(f"""
    from unified_keyphrase_extractor import UnifiedKeyphraseExtractor
    
    extractor = UnifiedKeyphraseExtractor()
    df = extractor.extract_from_text(text, section_texts, use_cleaned_text=True)
    """)
