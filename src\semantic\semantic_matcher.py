# src/semantic/semantic_matcher.py
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pandas as pd


def match_semantic_terms(input_phrases, input_embeddings, vocab_terms, vocab_embeddings, threshold=0.75):
    """
    For each phrase, compute similarity with vocab embeddings and return matches above threshold.
    """
    sim_matrix = cosine_similarity(input_embeddings, vocab_embeddings)
    results = []

    for i, phrase in enumerate(input_phrases):
        similarities = sim_matrix[i]
        for j, score in enumerate(similarities):
            if score >= threshold:
                results.append({
                    'input_phrase': phrase,
                    'matched_vocab_term': vocab_terms[j],
                    'similarity': score
                })

    return pd.DataFrame(results).sort_values(by='similarity', ascending=False).reset_index(drop=True)


def match_semantic_terms(
    term_texts,  # or sentence_texts
    term_embeddings,
    vocab_terms,
    vocab_embeddings,
    threshold=0.95
):
    results = []

    for i, emb in enumerate(term_embeddings):
        for j, vocab_emb in enumerate(vocab_embeddings):
            sim = np.dot(emb, vocab_emb)  # assuming embeddings are normalized
            if sim >= threshold:
                results.append({
                    'term': term_texts[i],
                    'matched_vocab_term': vocab_terms[j],
                    'similarity': sim
                })

    if not results:
        print("⚠️ No semantic matches found above threshold.")
        return pd.DataFrame(columns=['term', 'matched_vocab_term', 'similarity'])

    return pd.DataFrame(results).sort_values(by='similarity', ascending=False).reset_index(drop=True)


# from sklearn.metrics.pairwise import cosine_similarity
# import pandas as pd
# import numpy as np

# def match_semantic_terms(
#     term_texts,
#     term_embeddings,
#     vocab_terms,
#     vocab_embeddings,
#     threshold=0.75
# ):
#     """
#     For each input term, compute cosine similarity with vocab terms and return matches above threshold.
#     """
#     sim_matrix = cosine_similarity(term_embeddings, vocab_embeddings)
#     results = []

#     for i, term in enumerate(term_texts):
#         similarities = sim_matrix[i]
#         print(f"{similarities =}")
#         for j, score in enumerate(similarities):
#             if score >= threshold:
#                 results.append({
#                     'term': term,
#                     'matched_vocab_term': vocab_terms[j],
#                     'similarity': score
#                 })

#     if not results:
#         print("⚠️ No semantic matches found above threshold.")
#         return pd.DataFrame(columns=['term', 'matched_vocab_term', 'similarity'])

#     return pd.DataFrame(results).sort_values(by='similarity', ascending=False).reset_index(drop=True)
