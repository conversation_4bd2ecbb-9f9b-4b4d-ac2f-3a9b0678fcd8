# # src/extractors/llama_extractor_chunked.py

import re
import requests
import pandas as pd


def chunk_text(text, max_chars=1000):
    """
    Split long paragraph into smaller chunks, preserving sentence boundaries.
    """
    sentences = re.split(r'(?<=[.!?])\s+', text.strip())
    chunks = []
    current = ""

    for sentence in sentences:
        if len(current) + len(sentence) <= max_chars:
            current += " " + sentence
        else:
            chunks.append(current.strip())
            current = sentence
    if current:
        chunks.append(current.strip())

    return chunks


def chunk_segments(segments: dict, max_chars=1000):
    """
    Chunk long segments (except 'title' and 'abstract') into smaller blocks.
    
    Args:
        segments (dict): Original input with segment_name → text
        max_chars (int): Max characters per chunk

    Returns:
        dict: New dict with segment_name or segment_name_i → text
    """
    chunked = {}
    for seg_name, text in segments.items():
        seg_name_lower = seg_name.lower().strip()

        if seg_name_lower in ['title', 'abstract']:
            # Keep as-is
            chunked[seg_name_lower] = text.strip()
        else:
            para_chunks = []
            paragraphs = text.split("\n\n")

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                if len(para) > max_chars:
                    para_chunks.extend(chunk_text(para, max_chars))
                else:
                    para_chunks.append(para)

            for i, chunk in enumerate(para_chunks):
                chunk_key = f"{seg_name_lower}_{i+1}"
                chunked[chunk_key] = chunk

    return chunked




# src/extractors/llama_extractor_chunked.py

import re
import requests
import pandas as pd


def clean_llama_response(response_text):
    """
    Cleans the response text to return only a list of keyphrases.
    """
    lines = response_text.strip().splitlines()
    keywords = []

    for line in lines:
        matches = re.findall(r"[a-zA-Z0-9\- ]+", line)
        for match in matches:
            term = match.strip()
            if term and not term.lower().startswith("output") and not term.lower().startswith("input"):
                keywords.extend(term.split(','))

    return list({kw.strip() for kw in keywords if kw.strip()})


def chunk_text(text, max_chars=1000):
    """Split long text into sentence-based chunks under `max_chars`."""
    import re
    sentences = re.split(r'(?<=[.!?])\s+', text.strip())
    chunks, current = [], ""
    for sentence in sentences:
        if len(current) + len(sentence) <= max_chars:
            current += " " + sentence
        else:
            chunks.append(current.strip())
            current = sentence
    if current:
        chunks.append(current.strip())
    return chunks


def chunk_segments(segments: dict, max_chars=1000):
    """
    Chunk non-title/abstract segments into paragraph/sentence chunks.
    """
    chunked = {}
    for seg_name, text in segments.items():
        seg_name_lower = seg_name.lower().strip()

        if seg_name_lower in ['title', 'abstract']:
            chunked[seg_name_lower] = text.strip()
        else:
            para_chunks = []
            paragraphs = text.split("\n\n")

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                if len(para) > max_chars:
                    para_chunks.extend(chunk_text(para, max_chars))
                else:
                    para_chunks.append(para)

            for i, chunk in enumerate(para_chunks):
                chunk_key = f"{seg_name_lower}_{i+1}"
                chunked[chunk_key] = chunk

    return chunked


def build_llama_prompt(sentence, topic_name=None, topic_info=None, sample_keyphrases=None, examples=None):
    """
    Build a full LLaMA prompt with topic context, sample keyphrases, and few-shot examples.
    """
    instruction = (
        "You are an expert scientific keyphrase extractor.\n"
        "Given a sentence and a topic, extract the most relevant scientific keyphrases.\n"
        "Only return a comma-separated Python list of keyphrases.\n"
        "Do not include any explanation or commentary."
    )

    topic_block = f"\n\n### Topic Name:\n{topic_name.strip()}" if topic_name else ""
    topic_info_block = f"\n\n### Topic Information:\n{topic_info.strip()}" if topic_info else ""

    sample_kws_block = ""
    if sample_keyphrases:
        formatted_kws = ', '.join(sample_keyphrases[:15])
        sample_kws_block = f"\n\n### Example Keyphrases from Topic:\n{formatted_kws}"

    examples_block = examples or """
        \n\n### Examples:
        Input: "Magnesium-coated boron fullerenes exhibit high hydrogen uptake."
        Output: ['hydrogen uptake', 'magnesium-coated boron fullerenes']

        Input: "We used density functional theory to simulate the interactions."
        Output: ['density functional theory', 'simulate interactions']

        Input: "Ab initio molecular dynamics confirms stability of structures."
        Output: ['ab initio molecular dynamics', 'stability', 'structures']
        """

    prompt = (
        f"{instruction}"
        f"{topic_block}"
        f"{topic_info_block}"
        f"{sample_kws_block}"
        f"{examples_block}"
        f"\n\nInput: \"{sentence}\"\nOutput:"
    )

    return prompt


def extract_llama_keyphrases_from_segments(
    api_url: str,
    segments: dict,
    topic_name: str = None,
    topic_info: str = None,
    sample_keywords: list = None,
    max_chars: int = 1000
) -> pd.DataFrame:
    """
    Extract keyphrases for each segment or chunk using the LLaMA model.
    
    Args:
        api_url (str): URL to your LLaMA inference endpoint.
        segments (dict): Dictionary with keys like "title", "abstract", "methods", etc.
        topic_name (str): Name of the domain/topic.
        topic_info (str): Detailed topic background.
        sample_keywords (list): Example keywords from the topic.
        max_chars (int): Max chunk size for long segments.

    Returns:
        pd.DataFrame with columns: ['segment', 'text', 'keyphrases']
    """
    chunked_segments = chunk_segments(segments, max_chars=max_chars)
    results = []

    for segment_name, text_chunk in chunked_segments.items():
        prompt = build_llama_prompt(
            sentence=text_chunk,
            topic_name=topic_name,
            topic_info=topic_info,
            sample_keyphrases=sample_keywords
        )

        try:
            response = requests.post(api_url, json={"prompt": prompt})
            response.raise_for_status()
            raw_output = response.text.strip()
            phrases = clean_llama_response(raw_output)

            results.append({
                "segment": segment_name,
                "text": text_chunk,
                "keyphrases": phrases
            })

        except Exception as e:
            print(f"[⚠️] LLaMA error for segment: {segment_name[:30]}... | {e}")
            results.append({
                "segment": segment_name,
                "text": text_chunk,
                "keyphrases": []
            })

    return pd.DataFrame(results)





if __name__ == "__main__":
    api_url = "http://***********:5007/lamma_3_2_model"

    section_texts_dict = {
        "title": "Investigation of Electronic and Topological Properties of Magnesium-coated Boron Fullerenes and Their Interaction with Hydrogen Molecule",
        "abstract": "Various nanostructures have been widely investigated as alternative materials for hydrogen storage using experimental and computational techniques. In this research, adsorption, electronic, topological properties, and some molecular descriptors of magnesium-doped boron fullerenes and their interaction with H2 for hydrogen storage are investigated using density functional theory at B3LYP/6-31G//M062X/6-31G** theoretical level. Structures of B80, Mg12B80, Mg20B80 and Mg30B80 were optimized and their interaction with hydrogen molecule were analyzed. Results shows that charge transfer from Mg to B atoms is responsible for positive charge of Mg atoms. When hydrogen molecule approach to the system, it gets polarized and adsorbed to these boron fullerenes doped with Mg atoms. Also, it was found that Mg12B80 possesses the lowest energy gap (ΔEH-L), lowest hardness (η), and the highest adsorption energy, which indicates the reactivity and the hydrogen storage capability of this structure to adsorb hydrogen rather than B80, Mg20B80 and Mg30B80.",
        "results": "Fig. 1 shows optimized structures in which Mg atoms bind to the boron atoms in boron fullerene (B80). According to the structure presented by Szwacki et al. [31], when Mg atoms bind to the boron fullerene, charge transfer takes place from Mg atoms to B atoms. Tables 1 to 3 include amount of charge transfers for Mg12B80, Mg20B80 and Mg30B80 when Mg atoms bind to boron pentagonal and boron hexagonal rings, calculated in the context of Mulliken population analysis. This charge transfers make Mg atoms to become positively-charged, that leads to interaction with boron fullerene with negative charge. The binding energies for boron fullerene doped with Mg is calculated b. By using the energy of HOMO and LUMO orbitals, EH and EL, the values of ionization energy(IP), electron affinity (EA), electronegativity (χ), and hardness (ƞ) can be obtained:",
        "figure_caption": "Fig. 1. Optimized configurations of side view of a) B80, c) Mg12B80, e) Mg20B80 and g) Mg30B80. Optimized configurations of top view of b) B80, d) Mg12B80, f) Mg20B80 and h) Mg30B80. The blue ball for B, the green ball for Mg. The calculated binding energies are listed in Table 4. Our calculated results show that the average binding energy in the Mg12B80, Mg20B80 and Mg30B80 are -0.76, -1.13 and -0.35 eV/Mg, respectively. To investigate the interaction of the hydrogen molecule with the B80 coated with Mg, a hydrogen molecule is added to this system, that displaces the absorbed Mg atoms. The electric field due to the positively charged Mg atoms increases the polarizability of the H2 molecule which leads to adsorption of H2 molecule without dissociation.",
        "table_data": "",
        "table_caption": "Table 1. Mulliken charge distribution in the Mg and B atoms of the Mg12B80H2 and average charge transfer (Δq) from Mg to B atoms in pentagonal faces of B80 cage. Table 2. Mulliken charge distribution in the Mg and B atoms of the Mg20B80H2 and average charge transfer(Δq) from Mg to B atoms in hexagonal faces of the B80 cage. Table 3. Mulliken charge distribution in the Mg and B atoms of the Mg30B80H2 and average charge transfer (Δq)from Mg to B atoms in hexagonal faces of the B80 cage. Table 5. Hydrogen absorption energy (Ea) on Metal-coated Boron nanostructures calculated in previous papers and this research."
    }

    # raw_text = " ".join(section_texts_dict.values())

    topic = "Electrochemical, Radiational, and Thermal Energy Technology"

    topic_information = """This topic covers chemical, biochemical, electrochemical, photochemical, and chemical engineering aspects of energy sources including geothermal, solar energy, ocean thermal energy, and other non-fossil fuel energy sources. Also included are studies of photoinduced redox reactions and artificial photosynthesis when used in solar energy conversion and storage and recovery and usage of waste heat. Hydrogen manufacture for fuel use by photolytic, biophotolytic, photoelectrochemical, and thermochemical processes if solar energy is involved as well as fuel manufacture from biomass are also covered in this topic. Additionally, energy conversion technology and energy conversion devices, including device components (batteries, fuel cells, solar cells, thermoelectric devices, etc) are also found in this topic. This topic also includes studies on chemical and chemical engineering aspects of handling, transport, and storage of thermal, solar, and geothermal energy, and of non-fossil fuels."""

    sample_keyphrases = [
        "Fuel cell", "Lithium secondary batteries", "Battery cathode", "Fuel cells, polymer electrolyte",
        "Secondary battery", "Battery anode", "Fluoropolymer", "Fuel cell electrolyte", "Solid oxide fuel cell", "Fuel cell electrode", "Carbon black", "Battery electrolyte", "Fuel cell separator", "Solar cell", "Battery electrode", "Fuel cells, proton exchange membrane", "Ionomer", "Polyoxyalkylenes",
        "Electric current-potential relationship", "Fuel cell cathode", "Fuel cell anode", "Polymer electrolyte", "Primary battery", "Carbon fiber", "Lead-acid secondary battery"
    ]


    df = extract_llama_keyphrases_from_segments(
        api_url=api_url,
        segments = section_texts_dict,
        topic_name= topic,
        topic_info= topic_information,
        sample_keywords= sample_keyphrases,
        max_char = 1000
    )
    print(df)