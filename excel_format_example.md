# Excel File Format for Unified Keyphrase Extraction

## Required Columns

Your Excel file must contain the following columns:

| Column Name | Description | Example |
|-------------|-------------|---------|
| `tan_name` | Unique identifier for each document/experiment | "08927881G.article.001.pdf" |
| `experimental_procedures` | Text content describing experimental methods | "The synthesis was carried out using..." |
| `results` | Text content describing experimental results | "The battery showed improved capacity..." |
| `conclusion` | Text content with conclusions and findings | "In conclusion, the MOF-based cathode..." |

## Example Excel Structure

```
tan_name                    | experimental_procedures              | results                           | conclusion
08927881G.article.001.pdf  | The synthesis was carried out...     | The battery showed improved...    | In conclusion, the MOF-based...
08927881G.article.002.pdf  | Materials were prepared using...     | Electrochemical testing revealed... | The results demonstrate...
09123456H.article.001.pdf  | The cathode material was synthesized... | Cycling performance showed...    | This work presents...
```

## How It Works

1. **Grouping by tan_name**: The function groups all rows with the same `tan_name` value
2. **Text Combination**: For each unique `tan_name`, it combines all text from the three content columns
3. **Section Creation**: Creates a `section_texts_dict` with:
   - `title`: The `tan_name` value
   - `abstract`: Combined text from all three columns
   - `experimental_procedures`: Combined experimental procedures text
   - `results`: Combined results text
   - `conclusion`: Combined conclusion text
4. **Individual Processing**: Runs keyphrase extraction for each unique `tan_name`
5. **Separate Output**: Creates separate output directories for each `tan_name`

## Usage Example

```python
# Process Excel file with multiple documents
process_excel_for_unified_extraction(
    excel_file_path="your_research_data.xlsx",
    topic="Electrochemical, Radiational, and Thermal Energy Technology",
    topic_information="Your detailed topic description...",
    sample_keyphrases=["battery", "cathode", "fuel cell", "lithium"],
    output_prefix="batch_extraction"
)
```

## Output Structure

For each unique `tan_name`, the function creates:
- A separate directory: `batch_extraction_{tan_name}/`
- All standard unified extraction outputs in that directory
- A summary Excel file: `batch_extraction_processing_summary.xlsx`

## Notes

- Multiple rows with the same `tan_name` will be combined
- Empty or NaN values in content columns are safely handled
- Documents with insufficient content (< 50 characters) are skipped
- Processing continues even if individual documents fail
- Detailed error reporting in the summary file
