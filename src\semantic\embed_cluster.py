# embed_cluster.py

from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans
import pandas as pd
import numpy as np
import re
import yake
from collections import Counter

class EmbedCluster:
    def __init__(self, model_name='paraphrase-mpnet-base-v2', max_clusters=30):
        self.model = SentenceTransformer(model_name)
        self.max_clusters = max_clusters

    @staticmethod
    def clean_text(text):
        """Lowercase, remove special chars, keep alphanumeric & spaces."""
        text = text.lower()
        text = re.sub(r"[^a-z0-9\s]", "", text)
        return text.strip()

    @staticmethod
    def filter_general_terms(texts, top_k=6):
        """Filter out generic/unimportant terms using YAKE."""
        kw_extractor = yake.KeywordExtractor(top=top_k, stopwords=None)
        filtered = []
        for text in texts:
            if not text or text.isspace():
                continue
            keywords = [kw for kw, score in kw_extractor.extract_keywords(text)]
            if keywords:
                filtered.append(text)
        return filtered

    def embed(self, texts):
        """Generate embeddings for the provided texts."""
        return self.model.encode(texts, convert_to_numpy=True, normalize_embeddings=True)

    def cluster(self, texts):
        """Cluster texts into max N clusters."""
        # Clean and filter
        cleaned = [self.clean_text(t) for t in texts if isinstance(t, str)]
        cleaned = self.filter_general_terms(cleaned)

        if not cleaned:
            return pd.DataFrame(columns=["text", "cluster"])

        embeddings = self.embed(cleaned)

        # Limit clusters to min(len(texts), max_clusters)
        n_clusters = min(len(cleaned), self.max_clusters)

        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)

        return pd.DataFrame({
            "text": cleaned,
            "cluster": labels
        })

if __name__ == "__main__":
    
    data = [
        "Energy conversion devices and their components",
        "Batteries and energy storage",
        "Fuel cells for hydrogen power",
        "Solar cells and photoelectrochemical cells",
        "Wind energy conversion systems",
        "Nuclear safety and waste management",
    ]

    ec = EmbedCluster(max_clusters=30)
    clustered_df = ec.cluster(data)
    print(clustered_df)
