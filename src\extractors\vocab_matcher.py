# # src/extractors/vocab_matcher.py
import os
import re
import pickle
import yaml
import pandas as pd
import numpy as np
from nltk.util import ngrams
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import inflect



def load_config(config_path):
    # config_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml"
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)



def create_vocab_embeddings(input_excel: str, output_pkl: str, model_name: str = "paraphrase-mpnet-base-v2"):
    """
    Reads terms from an Excel file and generates embeddings.
    Saves as .pkl file with {'terms': list[str], 'embeddings': np.ndarray}.
    """

    # --- 1. Load Excel ---
    df = pd.read_excel(input_excel)

    if "terms" not in df.columns:
        raise ValueError("Input Excel must contain a column named 'terms'")

    # Clean terms
    terms = df["terms"].dropna().astype(str).str.strip().tolist()

    # --- 2. Load embedding model ---
    model = SentenceTransformer(model_name)

    # --- 3. Generate embeddings ---
    embeddings = model.encode(terms, convert_to_numpy=True, normalize_embeddings=True)

    # --- 4. Save to pickle ---
    with open(output_pkl, "wb") as f:
        pickle.dump({"terms": terms, "embeddings": embeddings}, f)

    print(f"✅ Saved {len(terms)} terms and embeddings to {output_pkl}")
    return terms, embeddings




def load_vocab(file_path):
    """Load vocab from Excel file."""
    df = pd.read_excel(file_path)
    vocab = df.iloc[:, 0].dropna().astype(str).str.lower().str.strip().tolist()
    return list(set(vocab))


def load_vocab_embeddings(pickle_path):
    """Load vocab terms and their embeddings from .pkl"""
    with open(pickle_path, "rb") as f:
        data = pickle.load(f)
    return data["terms"], data["embeddings"]  # terms: list[str], embeddings: np.ndarray


def match_terms_with_embeddings(input_texts, model, vocab_terms, vocab_embeddings, threshold=0.8):
    if isinstance(input_texts, str):
        input_texts = [input_texts]

    input_embeddings = model.encode(input_texts, convert_to_numpy=True, normalize_embeddings=True)

    if input_embeddings.ndim == 1:
        input_embeddings = input_embeddings.reshape(1, -1)

    similarity_matrix = cosine_similarity(input_embeddings, vocab_embeddings)

    matched_results = []
    for i, phrase in enumerate(input_texts):
        sim_scores = similarity_matrix[i]
        max_sim = np.max(sim_scores)
        top_idx = np.argmax(sim_scores)
        best_term = vocab_terms[top_idx]
        if max_sim >= threshold:
            matched_results.append({
                'input_phrase': phrase,
                'matched_term': best_term,
                'similarity': max_sim
            })

    return pd.DataFrame(matched_results)


config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")

# def matched_vocab_terms(input_texts, config, threshold=0.9):
#     """
#     Return a list of matched vocab terms using SentenceTransformer embeddings,
#     based on a similarity threshold and using paths from config.
#     """
#     # Ensure input is a list of words/phrases
#     if isinstance(input_texts, str):
#         input_phrases = input_texts.lower().strip().split()
#     elif isinstance(input_texts, list):
#         input_phrases = [w.lower().strip() for w in input_texts if isinstance(w, str)]
#     else:
#         raise ValueError("Input must be a string or list of strings.")

#     # Generate unigrams, bigrams, trigrams
#     ngrams_list = []
#     for n in range(1, 4):
#         ngrams_list.extend([' '.join(g) for g in ngrams(input_phrases, n)])

#     ngrams_list = list(set(ngrams_list))  # Remove duplicates

#     # Load model and vocab embeddings
#     # model = SentenceTransformer(config['extractors']['embedding_model']['model_path'])
#     model = SentenceTransformer(config['model_paths']['sentence_transformer'])

#     print(f"{model =}")
#     print(f"{config['paths']['embedding_store'] =}")

#     vocab_terms, vocab_embeddings = load_vocab_embeddings(config['paths']['embedding_store'])

#     # Match using cosine similarity
#     df = match_terms_with_embeddings(ngrams_list, model, vocab_terms, vocab_embeddings, threshold)

#     # Return sorted list of unique matched terms
#     if df.empty:
#         return []

#     return sorted(set(df["matched_term"].tolist()))

from typing import Union, List

def matched_vocab_terms(input_texts: Union[str, List[str]], config: dict, threshold = None):
    """
    Return a list of matched vocab terms using SentenceTransformer embeddings,
    based on a similarity threshold and using paths from config.
    """

    # Ensure input is a list of words/phrases
    if isinstance(input_texts, str):
        input_phrases = input_texts.lower().strip().split()
    elif isinstance(input_texts, list):
        input_phrases = [w.lower().strip() for w in input_texts if isinstance(w, str)]
    else:
        raise ValueError("Input must be a string or list of strings.")

    # Generate unigrams, bigrams, trigrams
    ngrams_list = []
    for n in range(1, 4):
        ngrams_list.extend([' '.join(g) for g in ngrams(input_phrases, n)])
    ngrams_list = list(set(ngrams_list))  # Remove duplicates 

    # --- Load model from config ---
    model_path = config["extractors"]["vocab_matcher"]["embedding_model"]["model_path"]
    model = SentenceTransformer(model_path)
    print(f"[INFO] Loaded embedding model from {model_path}")

    # --- Load vocab embeddings ---
    vocab_pkl_path = config["paths"]["embedding_store"]
    vocab_terms, vocab_embeddings = load_vocab_embeddings(vocab_pkl_path)
    print(f"[INFO] Loaded {len(vocab_terms)} vocab terms from {vocab_pkl_path}")

    # --- Determine threshold ---
    sim_threshold = threshold if threshold is not None else config["extractors"]["vocab_matcher"]["threshold"]

    # --- Match terms ---
    df = match_terms_with_embeddings(ngrams_list, model, vocab_terms, vocab_embeddings, sim_threshold)

    if df.empty:
        return []
    
    keyword_list = sorted(set(df["matched_term"].tolist()))
    keyword_list_reduced =reduce_keywords_by_meaning(keyword_list)

    return keyword_list_reduced

def reduce_keywords_by_meaning(keywords: list, max_reduction_factor: float = 1/2):
    if not keywords:
        return []

    # --- 1. Remove verbs/general stopwords (stored lowercase for matching) ---
    general_words = set([
        "is","are","was","were","be","been","being","have","has","had",
        "do","does","did","done","can","could","may","might","must","shall","should","will","would",
        "surface","study","result","approach","method","analysis","effect","data","system","model","work"
    ])

    def clean_keyword(kw):
        """Remove stopwords and generic terms, but preserve case in the returned string."""
        text = kw.strip()
        tokens = [t for t in text.split() if t.lower() not in general_words]
        cleaned = " ".join(tokens)
        return cleaned if cleaned.lower() not in general_words else ""

    cleaned_keywords = [clean_keyword(k) for k in keywords]
    technical_keywords = [kw for kw in cleaned_keywords if len(kw) > 2]

    print(f" Filtered {len(keywords) - len(technical_keywords)} invalid/general words.")

    if not technical_keywords:
        return [] 

    # --- 2. Normalization and Mapping (case-insensitive for clustering) ---
    normalized_map = {}
    for original_kw in technical_keywords:
        normalized_kw = re.sub(r'[\s-]+', ' ', original_kw.lower()).strip()
        normalized_map.setdefault(normalized_kw, []).append(original_kw)  # store original case
    unique_normalized_kws = list(normalized_map.keys())

    # --- 3. Generate Embeddings ---
    print(" Generating keyword embeddings...")
    model = SentenceTransformer('all-MiniLM-L6-v2')
    embeddings = model.encode(unique_normalized_kws, show_progress_bar=False)

    # --- 4. Iterative Clustering ---
    max_cluster_count = int(np.floor(len(technical_keywords) * max_reduction_factor))
    max_cluster_count = max(1, max_cluster_count)
    print(f" Clustering to get at most {max_cluster_count} unique concepts...")

    similarity_matrix = cosine_similarity(embeddings)
    final_clusters, final_threshold = [], None

    for threshold in np.arange(0.40, 0.95, 0.05):  # looser to stricter
        clusters, processed_indices = [], set()
        for i in range(len(unique_normalized_kws)):
            if i in processed_indices:
                continue
            similar_indices = np.where(similarity_matrix[i] > threshold)[0]
            current_cluster = {idx for idx in similar_indices if idx not in processed_indices}
            processed_indices.update(current_cluster)
            clusters.append(list(current_cluster))

        if len(clusters) <= max_cluster_count:
            final_clusters, final_threshold = clusters, threshold
            break

    if not final_clusters:
        final_clusters = [[i] for i in range(len(unique_normalized_kws))]
        final_threshold = 0.0

    print(f" Clustering complete with a similarity threshold of {final_threshold:.2f}")

    # --- 5. Select Representative ---
    p = inflect.engine()
    reduced_keywords = []

    for cluster_indices in final_clusters:
        original_candidates = []
        for index in cluster_indices:
            normalized_kw = unique_normalized_kws[index]
            original_candidates.extend(normalized_map[normalized_kw])  # keeps original casing

        if not original_candidates:
            continue

        def clean_for_match(text):
            return re.sub(r'[^a-z0-9]', '', text.lower())

        # Rule 1: Substring Filtering inside cluster (case-insensitive)
        to_remove = set()
        cleaned_candidates = {cand: clean_for_match(cand) for cand in original_candidates}
        for cand1 in original_candidates:
            for cand2 in original_candidates:
                if cand1 != cand2 and cleaned_candidates[cand1] and cleaned_candidates[cand1] in cleaned_candidates[cand2]:
                    to_remove.add(cand1)

        remaining_candidates = [cand for cand in original_candidates if cand not in to_remove]

        if not remaining_candidates:
            representative = max(original_candidates, key=len)
        else:
            # Rule 2: Plural Preference (case-insensitive check)
            plural_representatives = []
            for word1 in remaining_candidates:
                for word2 in remaining_candidates:
                    if word1 != word2 and p.plural(word1.lower()) == word2.lower():
                        plural_representatives.append(word2)

            if plural_representatives:
                representative = max(plural_representatives, key=len)
            else:
                representative = max(remaining_candidates, key=len)

        reduced_keywords.append(representative)

    # --- 6. Global Substring Removal (across clusters, case-insensitive) ---
    final_keywords = []
    for kw in sorted(set(reduced_keywords), key=len, reverse=True):
        if not any(kw.lower() in longer.lower() for longer in final_keywords if len(longer) > len(kw)):
            final_keywords.append(kw)

    return sorted(final_keywords)

def reduce_keywords_by_meaning_1(keywords: list, min_reduction_factor: float = 1/3):
    """
    Reduces keywords by filtering, clustering, and selecting the most complete term.

    The selection logic prioritizes:
    1. Keeping longer phrases over partial substrings.
    2. Keeping plural forms over singular ones.
    3. Keeping the longest string as a fallback.

    Args:
        keywords (list): The list of keyword strings to reduce.
        min_reduction_factor (float): The minimum fraction of keywords to retain.

    Returns:
        list: A final, reduced list of semantically unique keywords.
    """
    if not keywords:
        return []

    # --- 1. Filter out General Words ---
    general_words = {
        'competitive', 'concentration', 'conversion', 'design', 'efficiency',
        'evolution', 'improving', 'interface', 'interference', 'mechanism',
        'ordering', 'oxidation', 'reduction', 'relationships', 'stability',
        'stable', 'steam', 'structure', 'surface', 'synthesis', 'testing'
    }
    technical_keywords = [kw for kw in keywords if kw.lower() not in general_words]
    print(f"🔎 Filtered {len(keywords) - len(technical_keywords)} general words.")

    # --- 2. Normalization and Mapping ---
    normalized_map = {}
    for kw in technical_keywords:
        normalized_kw = re.sub(r'[\s-]+', ' ', kw.lower()).strip()
        if normalized_kw not in normalized_map:
            normalized_map[normalized_kw] = []
        normalized_map[normalized_kw].append(kw)
    unique_normalized_kws = list(normalized_map.keys())

    # --- 3. Generate Embeddings ---
    print("🧠 Generating keyword embeddings...")
    model = SentenceTransformer('all-MiniLM-L6-v2')
    embeddings = model.encode(unique_normalized_kws, show_progress_bar=False)

    # --- 4. Iterative Clustering ---
    min_cluster_count = int(np.ceil(len(technical_keywords) * min_reduction_factor))
    print(f"⚙️  Clustering to find at least {min_cluster_count} unique concepts...")
    final_clusters = []
    final_threshold = 0.0
    for threshold in np.arange(0.80, 1.0, 0.01):
        similarity_matrix = cosine_similarity(embeddings)
        clusters = []
        processed_indices = set()
        for i in range(len(unique_normalized_kws)):
            if i in processed_indices:
                continue
            similar_indices = np.where(similarity_matrix[i] > threshold)[0]
            current_cluster = {idx for idx in similar_indices if idx not in processed_indices}
            processed_indices.update(current_cluster)
            clusters.append(list(current_cluster))
        if len(clusters) >= min_cluster_count:
            final_clusters = clusters
            final_threshold = threshold
            break
    print(f"✅ Clustering complete with a similarity threshold of {final_threshold:.2f}")

    # --- 5. Select Representative (with Substring and Plural Logic) ---
    p = inflect.engine()
    reduced_keywords = []

    for cluster_indices in final_clusters:
        original_candidates = []
        for index in cluster_indices:
            normalized_kw = unique_normalized_kws[index]
            original_candidates.extend(normalized_map[normalized_kw])
        
        if not original_candidates:
            continue

        # Helper to clean strings for substring matching (alphanumeric only)
        def clean_for_match(text):
            return re.sub(r'[^a-z0-9]', '', text.lower())

        # **Rule 1: Substring Filtering**
        to_remove = set()
        cleaned_candidates = {cand: clean_for_match(cand) for cand in original_candidates}
        for cand1 in original_candidates:
            for cand2 in original_candidates:
                if cand1 == cand2:
                    continue
                # If the cleaned version of cand1 is a substring of cand2, mark it for removal
                if cleaned_candidates[cand1] in cleaned_candidates[cand2]:
                    to_remove.add(cand1)
        
        remaining_candidates = [cand for cand in original_candidates if cand not in to_remove]
        
        # If all candidates were substrings of each other, default to the longest one
        if not remaining_candidates:
            representative = max(original_candidates, key=len)
            reduced_keywords.append(representative)
            continue
        
        # **Rule 2: Plural Preference**
        plural_representatives = []
        for word1 in remaining_candidates:
            for word2 in remaining_candidates:
                if word1 != word2 and p.plural(word1) == word2:
                    plural_representatives.append(word2)
        
        if plural_representatives:
            representative = max(plural_representatives, key=len)
        else:
            # **Rule 3: Longest String Fallback**
            representative = max(remaining_candidates, key=len)
            
        reduced_keywords.append(representative)
        
    return sorted(reduced_keywords)





if __name__ == "__main__":
    config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")

    raw_text_0 = "Investigation of Electronic and Topological Properties of Magnesium-coated Boron Fullerenes and Their Interaction with Hydrogen Molecule. Various nanostructures have been widely investigated as alternative materials for hydrogen storage using experimental and computational techniques. In this research, adsorption, electronic, topological properties, and some molecular descriptors of magnesium-doped boron fullerenes and their interaction with H2 for hydrogen storage are investigated using density functional theory at B3LYP/6-31G//M062X/6-31G** theoretical level. Structures of B80, Mg12B80, Mg20B80 and Mg30B80 were optimized and their interaction with hydrogen molecule were analyzed. Results shows that charge transfer from Mg to B atoms is responsible for positive charge of Mg atoms. When hydrogen molecule approach to the system, it gets polarized and adsorbed to these boron fullerenes doped with Mg atoms. Also, it was found that Mg12B80 possesses the lowest energy gap (ΔEH-L), lowest hardness (η), and the highest adsorption energy, which indicates the reactivity and the hydrogen storage capability of this structure to adsorb hydrogen rather than B80, Mg20B80 and Mg30B80. The calculated binding energies are listed in Table 4. Our calculated results show that the average binding energy in the Mg12B80, Mg20B80 and Mg30B80 are -0.76, -1.13 and -0.35 eV/Mg, respectively. To investigate the interaction of the hydrogen molecule with the B80 coated with Mg, a hydrogen molecule is added to this system, that displaces the absorbed Mg atoms. The electric field due to the positively charged Mg atoms increases the polarizability of the H2 molecule which leads to adsorption of H2 molecule without dissociation. To determine the type of interaction of hydrogen molecule with B80, Mg12B80, Mg20B80 and Mg30B80, critical points of electron density distribution have been calculated by using AIM2000 software. The results of these calculations, such as the values of charge density, ρ(r), Laplacian of ρ, ∇2(ρ), Lagrangian Kinetic Energy, G(r), Hamiltonian V(r), for the hydrogen absorption bond and the corresponding atoms, have been shown in Tables 6 to 9. Our calculated results show that the ∇2(ρ) values, for the critical points between the hydrogen molecule and B80, Mg12B80, Mg20B80 and Mg30B80, are all positive and consequently of electrostatic type. The ∇2(ρ) value for Mg12B80 is lower than thatB80, Mg20B80 and Mg30B80, which is equal to 0.0006, 0.0065, 0.0028 and 0.0029, respectively. By plotting the molecular graph, we found that the critical point of H2 molecule adsorption on Mg12B80 is located between the H atom and the Mg atoms on the pentagonal boron ring, while the critical point of H2 molecule adsorption on Mg20B80 and Mg30B80 is located between the H and one of the B atoms in the hexagonal boron ring. By using the energy of HOMO and LUMO orbitals, EH and EL, the values of ionization energy (IP), electron affinity (EA), electronegativity (χ), and hardness (ƞ) can be obtained."

    
    section_texts_dict = {
        "title": "Eutectoid-structured WC/W2C supported WS2/WO3 as a highly efficient hydrogen evolution electrocatalyst",
        "abstract": """
            This study presents a method for preparing tungsten-based electrocatalysts using a steam assisted carburization strategy. The WC/W2C base with eutectoid-structured was prepared by a customized carburization strategy, and the WS2/WO3 heterojunction was successfully introduced by adjusting the steam content. Thanks to the synergies
            and electronic effects between the WS2/WO3 interface and the WC/W2C base, the prepared catalyst
            exhibited excellent HER activity, with an overpotential of only 89 mV at a current density of 10 mA/cm2, and the
            stability could be maintained for more than 60 h. This study not only discusses the synthesis mechanism of WS2/
            WO3-WC/W2C, but also provides a new idea for the study of high-performance tungsten-based HER catalysts,
            challenging the dominance of commercial Pt/C catalysts.
            """,
        "results": """OS-WC/W2C is prepared by a customized gas-solid reaction (Fig. 1a). The micron-scale precursor with spherical hollow structure was first prepared by spray drying, and then calcined under air to remove moisture and NH3. X-ray diffraction (XRD) results showed that the precursor was WO3 (Fig. S1). As we all know, the formation of tungsten carbide phase is largely dependent on the diffusion of carbon atoms [22]. In our synthesis method, we introduce steam as an unstable factor to regulate the carburizing process. In stage 1, carbon source is obtained by the disproportionation reaction of carbon monoxide, and the formation of gaseous WO2(OH)2 is driven in situ by steam [23]. The formation of WO2(OH)2 is the reason for the microstructure reconstruction. WC/W2C nanosheet clusters with eutectic structure were prepared by using hydrogen to regulate the competitive relationship between carburizing and steam oxidation. The WC/W2C prepared by steam assisted chemical vapor deposition (SACVD) was named as SACVD-WC/W2C, where, steam is introduced by placing a water-bearing porcelain boat at the mouth of the pipe 10 cm away from the high temperature zone (Fig. S2). In stage 2, we loaded WS2 on the surface of SACVD-WC/W2C, but it is difficult to convert WC to WS2 directly, so we introduced an appropriate amount of steam to provide WO3 by bubbling (Fig. S2), and used WO3 to load WS2 on the surface of WC/W2C with eutectic structure. The prepared WS2/WO3-WC/W2C was named OS-WC/W2C. Scanning electron microscopy (SEM) image showed that SACVD- WC/W2C maintained the overall microstructure of the precursor hollow sphere, which was composed of stacked nanosheet clusters (Fig. 1b). The characteristic diffraction peaks of SACVD-WC/W2C at 2θ =31.51◦, 35.64◦and 48.29◦are derived from the (001), (100) and (101) crystal planes of hexagonal WC (JCPDS No. 51–0939), respectively (Fig. 1c). The characteristic diffraction peaks at 2θ =34.46◦, 38.03◦and 39.51◦are derived from the (002), (200) and (121) crystal planes of orthorhombic W2C (JCPDS No. 89–2371), respectively. Therefore, XRDsults show that WC and W2C phases coexist in the synthesized SACVD- WC/W2C. Transmission electron microscopy (TEM) results confirmed the presence of a eutectic structure (Fig. S3), which is formed by a CO and H2 dominated carburizing process competing with a steam dominated oxidation deposition process. The eutectic structure region is rich in low-energy defects [20,24], which enhances the synergy between WC and W2C. The formation of nanosheet clusters and eutectic structure is due to the design of carbonization atmosphere, therefore, we discuss the role of different gas in the carburizing process (Fig. 1d). When the calcination atmosphere is only steam (N2 is used as carrier gas), the precursor is etched into a smooth polyhedron, which is formed by the steam etched WO3 into gaseous WO2(OH)2, and WO2(OH)2 is redeposited, at which time the sample is still WO3 (Fig. S4). When CO is used as a single gas source or when CO and H2 are introduced at the same time, WO3 can effectively carburize to form pure WC (Fig. S4). It is worth noting that H2 can accelerate the carburizing rate [25], so the sphere cracks under the action of surface stress due to excessive carburizing. When CO and steam are used as the gas source, the sample presents a rod-like structure, but due to the limited reductibility of CO, it cannot effectively offset the oxidation of steam, so it cannot be effectively carburized. At this time, the main component of the sample is W18O49 (Fig. S4). It can be seen
        that the simultaneous introduction of three gas sources as carbonization
        atmosphere is necessary for the regulation of microstructure and the
        formation of eutectic structure.
        In addition, we studied the carburizing process of SACVD-WC/W2C.
        At 600℃, the microstructure of the sample did not change significantly
        (Fig. S5a). At this time, there was no effective carburizing, and XRD
        results showed that the sample was O29W10 (Fig. S6). After being held at
        600℃ for one hour, a rod-like structure appeared on the surface of the
        sample (Fig. S5b), which was the same as that of the sample prepared
        under the co-existence of CO and steam, indicating that steam could
        drive the migration of WO2(OH)2 at 600℃, and the sample was mainly
        W18O49 and WO2(Fig. S6). When the temperature rises to 700℃, H2
        inhibits the oxidation of steam, and the rod-like structure disappears, so
        the sample becomes WO2. When the temperature rises to 800℃, nanosheet
        clusters begin to appear on the surface of the sample (Fig. S5d),
        and XRD results show that the sample has a characteristic diffraction
        peak of W2C (Fig. S6), which indicates that carburizing can be effective
        at 800℃. Therefore, SACVD-WC/W2C can be obtained by holding it at
        800℃ for 6 h. At this temperature, both the migration characteristics of
        W are retained and carburizing can be effective, so the end temperature
        of 800℃ is appropriate.
        In stage 2, OS-WC/W2C was prepared by introducing sulfur source.
        As mentioned above, OS-WC/W2C retained the structure of nanosheet
        clusters after sulfuration (Fig. 2a, Fig. s7). The sheet structure of OS-WC/
        W2C can be observed in the TEM image (Fig. 2b). If the area in the red
        box of Fig. 2b is enlarged, it can be seen in Fig. 2c that the crystal plane
        spacing of WC is 0.284 nm, corresponding to the (001) crystal plane of
        WC, and the crystal plane spacing of W2C is 0.259 nm, corresponding to
        the (002) crystal plane of W2C. WC, W2C and eutectic structure exist
        alternately, and the eutectic region is in the middle of WC and W2C and
        there are a lot of lattice distortions, which indicates that the sulfuration
        process of stage 2 does not destroy the microstructure and eutectic
        structure formed in stage 1. On the WC/W2C base, we observed the
        existence of WS2 and WO3 (Fig. 2d, e). The crystal plane spacing of WS2
        is 0.267 nm, corresponding to the (101) crystal plane of WS2, and the
        crystal plane spacing of WO3 is 0.382 nm, corresponding to the (001)
        crystal plane of WO3. Among them, WO3 grows in the form of an island
        on the WC/W2C base surface, while WS2 grows near the WO3 interface,
        which is caused by the introduced steam, which forms an island of WO3
        on the WC/W2C base surface, and then, under the action of thiourea,
        WO3 is sulfurated and WS2 is formed near WO3. It is important to note
        that due to the presence of steam, the WC/W2C base may be overoxidized,
        such as amorphous WO3 (Fig. S8), which is inevitable. The
        XRD results also demonstrate the presence of WO3, and the characteristic
        diffraction peaks of OS-WC/W2C at 2θ = 23.24◦, 23.70◦ and 24.45◦
        are derived from the (001), (020) and (200) crystal plane of monoclinic
        WO3 (JCPDS No. 75–2072), respectively (Fig. 2f). However, due to the
        small content of WS2, the characteristic diffraction peak of WS2 was not
        observed. In addition, the HAADF-STEM-EDS mapping images
        confirmed the uniform distribution of C, W, O, and S elements, which
        also indicated that OS-WC/W2C was successfully sulfurated (Fig. 2g).
        Based on the above results, we proposed the possible growth mechanism
        of OS-WC/W2C (Fig. 3). In the insulation stage of stage 1,
        oxidation and carbonization are competitive. When the steam concentration
        at the WC(W2C) interface reaches a critical concentration, the
        WC(W2C) interface preferentially oxidizes, at this time, the WC(W2C)
        interface is oxidized to WO3, and the steam at the interface is consumed.
        When the steam concentration is lower than the critical concentration,
        carbonization preferentially occurs at the interface; however, due to the
        interference of steam induced WO2(OH)2, an eutectic structure (ES) is
        formed between WC and W2C. With the diffusion of steam, the concentration
        of steam at the interface again reaches a critical concentration,
        and the diffusion of steam follows Fick’s law, at which time the
        oxidation reaction will again take precedence. Therefore, the formation of the WC-ES-W2C interface is caused by the constant cycle competition between oxidation and carbonization. In stage 2, under the action of
        steam, island-like WO3 is formed at the interface of SACVD-WC/W2C. In
        the subsequent sulfuration process, WS2 is formed near island-like WO3
        and OS-WC/W2C is finally obtained.
        In order to investigate the electronic state and bonding form of OSWC/
        W2C and SACVD-WC/W2C, Raman and X-ray photoelectron spectroscopy
        (XPS) characterization were performed. Raman spectra
        (Fig. S9) show that the characteristic peaks of WO3 can be observed at
        802, 707, 325, 260, 183, 129 cm􀀀 1. The peaks at 802 and 707 cm􀀀 1 are
        assigned as W-O-W stretching frequencies. The shorter W-O-W bonds is
        responsible for the stretching mode of 802 cm􀀀 1, while the longer bonds
        is the source of the 707 cm􀀀 1 peak [26,27]. The peaks at 325 and
        260 cm􀀀 1 can be attributed to the W-O-W bending mode of bridged
        oxygen, while the peaks observed at 129 and 183 cm􀀀 1 can be attributed
        to the lattice vibration of the WO3 crystals [28,29]. However, the peak
        positions of WC and WO3 overlap very much [27,30,31], the peak at
        802 cm􀀀 1 can also be attributed to the WC stretching mode, and the
        peak at 777, 325, 260 cm􀀀 1 can be attributed to the early oxidation of
        the WC stretching mode (named as WC-O). Therefore, it is necessary to
        further explore the chemical state of OS-WC/W2C surface with XPS. It is
        worth noting that compared with SACVD-WC/W2C, the WO3 lattice
        vibration peak of OS-WC/W2C is significantly higher, which is caused by
        the stage 2 of steam oxidation. In addition, because OS-WC/W2C contains
        a small amount of WS2, the characteristic peaks of W-S bonds are
        also observed at 354 and 419 cm􀀀 1 [27,32].
        The surface chemical state of OS-WC/W2C was further investigated
        by XPS. The full-scan XPS survey spectrum proves the presence of the
        expected elements W, C, O (Fig. 4a). Fig. 4b shows the spectra of C1s.
        The C1s spectra of OS-WC/W2C and SACVD-WC/W2C can be deconvolved
        into four characteristic peaks: C––
        O (288.49 eV), C-O (286.56 eV), C-C (284.80 eV) and C-W (282.69 eV). Compared with
        SACVD-WC/W2C, the C-C peak height of OS-WC/W2C is significantly
        reduced, which is caused by the removal of carbon deposits by steam
        introduced in the stage 2 [25,33]. The peak of O1s can be assigned to
        C-O of 532.72 eV, C––
        O of 531.59 eV and O-W of 530.45 eV (Fig. 4c).
        The presence of O-W peak proves that the samples have different degrees
        of oxidation before and after sulfuration. Fig. 4d shows the spectra of
        W4f. The characteristic peaks at 35.65 eV and 37.82 eV can be attributed to W4f5/2 and W4f7/2 of W-O. Compared with
        SACVD-WC/W2C, the W-O peak is significantly increased due to the
        secondary oxidation of OS-WC/W2C. In the W4f spectra of OS-WC/W2C,
        the characteristic peaks located at 33.77 eV and 31.60 eV belong to
        W4f5/2 and W4f7/2 of W-C, which are negatively shifted by 0.1 eV
        compared with the WC characteristic peaks of SACVD-WC/W2C
        (33.87 eV and 31.70 eV, respectively). This indicates that the introduction
        of WO3 and WS2 on the surface of SACVD-WC/W2C improves
        the electron cloud density of WC/W2C base, and there is a strong
        interaction between WS2/WO3 interface and WC/W2C base [34,35]. In
        addition, after sulfuration, W-S characteristic peaks were observed at
        32.81 eV and 34.98 eV for OS-WC/W2C.
        Encouragingly, both SACVD-WC/W2C and OS-WC/W2C exhibit
        excellent acidic HER activity. First, we evaluated the effect of calcination
        atmosphere on HER activity of the sample in 0.5 M H2SO4 solution
        using a typical three-electrode system. Here, we named the sample after
        the calcination atmosphere. N2H2O and COH2O do not exhibit HER
        activity because they are not carburized efficiently (Fig. 5a). It is noteworthy
        that samples capable of effective carburization all exhibited a
        HER overpotential lower than that of commercial WC (particle size:
        200 nm). CO and COH2 can carburize effectively, and the overpotential
        (η10) at 10 mA/cm2 current density is 257 mV and 230 mV, respectively,
        while the η10 of SACVD-WC/W2C is only 129 mV, which is nearly
        100 mV lower than pure WC. In addition, compared with pure WC-phase
        catalysts, SACVD-WC/W2C has a lower Tafel slope (Fig. 5b), with a Tafel
        slope of 78.9 mV/dec. Low Tafel slope means faster HER kinetic process
        [36,37]. This indicates that the eutectic structure of nanosheet clusters
        can effectively improve HER performance of tungsten carbide. It is
        worth noting that the HER performance of OS-WC/W2C after sulfuration
        is further improved, and the η10 of OS-WC/W2C is only 89 mV (Fig. 5c),
        compared with Pt/C (30 mV), the difference is only 59 mV. Furthermore,
        OS-WC/W2C also has better HER kinetic performance, and the
        Tafel slope is only 58.3 mV/dec (Fig. 5d), which indicates that the HER
        mechanism of OS-WC/W2C may be the Volmer-Heyrovsky mechanism,
        and the Heyrovsky mechanism is the rate-determining step [38,39].
        Here, we summarized the overpotential and Tafel slope of the above
        catalysts (Fig. 5e). It is not difficult to see that the introduction of
        eutectic structure and WS2/WO3 interface effectively reduced the
        overpotential and improved the electrode kinetic performance.
        Although there is still a certain gap compared with commercial Pt/C, it
        still shows great potential in tungsten-based catalysts (Table S1).
        The Electrochemical impedance spectroscopy (Fig. 5f) based on
        equivalent circuit diagram at 150 mV shows that OS-WC/W2C has the
        smallest Rct value (35.7 Ω), it is obviously better than SACVD-WC/W2C
        (60.9 Ω), which reveals the improvement of charge transfer kinetics of
        OS-WC/W2C at the acidic electrolyte interface HER process [40,41]. In
        addition, there is no significant difference between the electrochemical
        surface area (ECSA) of OS-WC/W2C and SACVD-WC/W2C calculated by
        measuring the double-layer capacitance (Cdl) (Fig. S10), which indicates
        that the improvement of the performance of OS-WC/W2C may
        not be due to the change of microstructure after sulfuration process, but
        the improvement of intrinsic activity. In order to evaluate the HER
        stability of OS-WC/W2C catalyst in acidic media, continuous long-term
        cyclic voltammetry (CV) tests were conducted. As shown in Fig. 5g, the
        overpotential of OS-WC/W2C did not change significantly after 1000
        CVs. In addition, we performed long-term chronopotentiometry of
        OS-WC/W2C at a current density of 10 mA/cm2. As shown in Fig. 5h,
        after continuous operation for 60 h, the overpotential of OS-WC/W2C changes negligible. This indicates that OS-WC/W2C not only has excellent
        acidic HER activity, but also has excellent acidic HER stability. The
        improvement of HER performance may be due to the electronic effect
        and synergistic effect between eutectoid-structured WC/W2C and loaded
        WS2/WO3 interface. 
        """,
        "figure_caption": """  """,

        "table_data": """""",

        "conclusion": """
            In summary, OS-WC/W2C was prepared by a simple two-step calcination
        using the steam assisted carbonization strategy. OS-WC/W2C
        shows excellent HER performance, with an overpotential of only 89 mV
        at a current density of 10 mA/cm2, a Tafel slope of only 58.3 mV/dec,
        and stable operation of more than 60 h at a current density of 10 mA/
        cm2. The reason for the improved performance of OS-WC/W2C may be
        due to the electronic effects and synergistic effect between the eutectoidstructured
        WC/W2C base and the WS2/WO3 interface. This design idea
        for tungsten-based compounds provides a new idea for the preparation
        of novel HER electrocatalysts."""
        }

    raw_text = " ".join(section_texts_dict.values())
    input_phrases = raw_text.lower().strip().split()


    matched_keywords = matched_vocab_terms(input_phrases, config, threshold=0.98)
    print(f"{matched_keywords=}")
    print(f"{len(matched_keywords)=}")




########################################################
output_received = [
    'absorption', 'adsorber', 'adsorption', 'adsorption energy', 
    'affinity', 'atoms', 'binding energies', 'binding energy',
    'capability','charge', 'charge density', 'charge transfer', 
    'critical point',
    'density', 'density distribution', 'density functional', 'density functional theory', 'dissociation', 'distributions', 
    'doped', 'materials','molecules', 'nanostructures', 'coated', 
    'electric field', 'electron affinity', 'electron density', 'electron density distribution', 'electron structure', 'electronegativity', 'electrostatics', 'energies', 'energy', 'energy gap', 'fields', 'kinetic energy', 'structure', 'structures',  'transfer',  
    'fullerenes',
    'hamiltonian', 'hardness', 'hardnesses', 'hexagonal',
    'homo', 'homo energy', 'homo-lumo', 'homos', 'lumo', 'molecular orbital (lumo)',  
    'hydrogen storage', 'hydrogen storage capacity', 'storage', 'storage capacity', 
    'interactions', 'ionization', 'ionization energy', 
    'orbitals', 'polarizability', 'polarization', 'reactivity', 
    'optimizing', 'computational', 'research', 'software', 'techniques', 'theoretical',
    ]

matched_cth = [
    'density functional theory',
    'energy gap', 
    'hardness',
    'adsorption energy', 
    'electric field',  ## ("electric field effects" NOT FOUND in Article)
    'binding energy',
    'kinetic energy',
    'ionization energy', 
    'electron affinity',
    'electronegativity',
    'homo', ##("highest occupied molecular orbitals" NOT FOUND in Article)
    'lumo', ##("lowest unoccupied molecular orbitals" NOT FOUND in Article)
]

missed_cth = [
    'electric field',  ## ("electric field effects" NOT FOUND in Article)
    'homo', ##("highest occupied molecular orbitals" NOT FOUND in Article)
    'lumo', ##("lowest unoccupied molecular orbitals" NOT FOUND in Article)
]

noise_cth = [55]


###########################################
Curation_output = [
"density functional theory",
"energy gap",
"hardness",
"adsorption energy",
"electric field effects",
"binding energy",
"Kinetic Energy",
"ionization energy",
"electron affinity",
"electronegativity",
"highest occupied molecular orbitals",
"lowest unoccupied molecular orbitals",
]