#!/usr/bin/env python3
"""
Demo script for Keyphrase Consensus Analysis
Shows how to analyze which keyphrases are found by multiple extractors
and assess the accuracy/reliability of extraction results.
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor, analyze_keyphrase_consensus, create_summary_report
import pandas as pd

def demo_consensus_analysis():
    """Demonstrate keyphrase consensus analysis across extractors."""
    
    print("🔍 KEYPHRASE CONSENSUS ANALYSIS DEMO")
    print("=" * 60)
    
    # Sample data for demonstration
    topic = "Renewable Energy Systems"
    topic_information = """
    Research on renewable energy technologies including solar, wind, geothermal, 
    and biomass energy systems. Covers energy storage, grid integration, 
    efficiency optimization, and sustainable energy applications.
    """
    
    sample_keyphrases = [
        "renewable energy", "solar energy", "wind energy", "geothermal energy",
        "energy storage", "energy efficiency", "grid integration", "sustainability"
    ]
    
    sections_texts_dict = {
        "title": "Advanced Renewable Energy Systems for Sustainable Power Generation",
        "abstract": """
        This study investigates advanced renewable energy systems for sustainable 
        power generation. The research focuses on integrating solar, wind, and 
        geothermal energy sources with advanced energy storage technologies to 
        create reliable and efficient power systems.
        """,
        "introduction": """
        Renewable energy systems have become increasingly important for sustainable 
        development. Solar energy and wind energy are the most widely deployed 
        renewable technologies. Geothermal energy offers consistent baseload power. 
        Energy storage systems are crucial for grid stability. The weather was 
        excellent during our field studies.
        """,
        "methodology": """
        We analyzed various renewable energy configurations using simulation software. 
        Energy efficiency metrics were calculated for different system designs. 
        Grid integration challenges were assessed through modeling studies. 
        Economic analysis included lifecycle cost assessments. The research team 
        enjoyed coffee breaks during long simulation runs.
        """,
        "results": """
        The integrated renewable energy system achieved 85% efficiency. Solar energy 
        contributed 45% of total generation. Wind energy provided 35% of power output. 
        Geothermal energy supplied consistent baseload at 20%. Energy storage systems 
        improved grid stability by 60%. Cost analysis showed favorable economics 
        with 12-year payback period.
        """,
        "conclusion": """
        Advanced renewable energy systems demonstrate significant potential for 
        sustainable power generation. The combination of solar, wind, and geothermal 
        energy with energy storage creates reliable power systems. Future research 
        should focus on improving energy efficiency and reducing costs.
        """
    }
    
    print(f"📄 DEMO CONFIGURATION:")
    print(f"Topic: {topic}")
    print(f"Sections: {list(sections_texts_dict.keys())}")
    print(f"Sample keyphrases: {len(sample_keyphrases)}")
    
    try:
        # Initialize extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Update configuration
        extractor.config["extractors"]["llama"]["topic_name"] = topic
        extractor.config["extractors"]["llama"]["topic_info"] = topic_information
        extractor.config["extractors"]["llama"]["sample_keywords"] = sample_keyphrases
        
        # Extract keyphrases
        print(f"\n🚀 Extracting keyphrases with semantic filtering...")
        sample_text = sections_texts_dict["abstract"]
        
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=sections_texts_dict,
            use_cleaned_text=True
        )
        
        if df.empty:
            print("⚠️  No keyphrases extracted. Cannot perform consensus analysis.")
            return
        
        print(f"\n📊 EXTRACTION RESULTS:")
        print(f"Total keyword instances: {len(df)}")
        print(f"Unique keywords: {df['keyword'].nunique()}")
        print(f"Extractors used: {', '.join(df['extractor'].unique())}")
        
        # Perform consensus analysis
        print(f"\n🔍 PERFORMING CONSENSUS ANALYSIS...")
        consensus_df = analyze_keyphrase_consensus(df)
        
        print(f"\n📈 CONSENSUS STATISTICS:")
        print(f"Total unique keyphrases: {len(consensus_df)}")
        
        # Show distribution by consensus level
        consensus_levels = consensus_df['extractor_count'].value_counts().sort_index(ascending=False)
        print(f"\nDistribution by consensus level:")
        for count, num_keyphrases in consensus_levels.items():
            percentage = (num_keyphrases / len(consensus_df) * 100)
            print(f"  {count} extractors: {num_keyphrases} keyphrases ({percentage:.1f}%)")
        
        # Show high consensus keyphrases
        high_consensus = consensus_df[consensus_df['extractor_count'] >= 2].sort_values(
            ['extractor_count', 'total_frequency'], ascending=[False, False]
        )
        
        if not high_consensus.empty:
            print(f"\n🏆 HIGH CONSENSUS KEYPHRASES (found by multiple extractors):")
            for i, (_, row) in enumerate(high_consensus.head(10).iterrows(), 1):
                extractors = ', '.join(row['extractors'])
                freq_details = []
                for extractor, details in row['extractor_details'].items():
                    freq_details.append(f"{extractor}({details['frequency']})")
                freq_str = ', '.join(freq_details)
                
                print(f"  {i:2d}. {row['keyphrase']}")
                print(f"      Found by {row['extractor_count']} extractors: {extractors}")
                print(f"      Frequencies: {freq_str}")
                print(f"      Total frequency: {row['total_frequency']}")
                print()
        
        # Show medium consensus keyphrases
        medium_consensus = consensus_df[consensus_df['extractor_count'] == 1].sort_values(
            'total_frequency', ascending=False
        )
        
        if not medium_consensus.empty:
            print(f"📋 SINGLE EXTRACTOR KEYPHRASES (top 5):")
            for i, (_, row) in enumerate(medium_consensus.head(5).iterrows(), 1):
                extractor = row['extractors'][0]
                frequency = row['total_frequency']
                print(f"  {i}. {row['keyphrase']} (only by {extractor}, {frequency} times)")
        
        # Calculate accuracy metrics
        total_keyphrases = len(consensus_df)
        high_consensus_count = len(high_consensus)
        consensus_rate = (high_consensus_count / total_keyphrases * 100) if total_keyphrases > 0 else 0
        
        print(f"\n📊 ACCURACY ASSESSMENT:")
        print(f"Consensus Rate: {consensus_rate:.1f}% ({high_consensus_count}/{total_keyphrases})")
        print(f"This indicates the percentage of keyphrases validated by multiple extractors")
        
        if consensus_rate >= 70:
            print(f"✅ HIGH ACCURACY: Strong agreement between extractors")
        elif consensus_rate >= 50:
            print(f"⚠️  MODERATE ACCURACY: Reasonable agreement between extractors")
        else:
            print(f"❌ LOW ACCURACY: Limited agreement between extractors")
        
        # Show extractor performance comparison
        print(f"\n🎯 EXTRACTOR PERFORMANCE COMPARISON:")
        for extractor in df['extractor'].unique():
            extractor_df = df[df['extractor'] == extractor]
            unique_keywords = extractor_df['keyword'].nunique()
            total_instances = len(extractor_df)
            
            # Count consensus keywords for this extractor
            consensus_keywords = 0
            for keyword in extractor_df['keyword'].unique():
                consensus_row = consensus_df[consensus_df['keyphrase'] == keyword]
                if not consensus_row.empty and consensus_row.iloc[0]['extractor_count'] > 1:
                    consensus_keywords += 1
            
            consensus_rate_extractor = (consensus_keywords / unique_keywords * 100) if unique_keywords > 0 else 0
            
            print(f"  {extractor}:")
            print(f"    Total instances: {total_instances}")
            print(f"    Unique keywords: {unique_keywords}")
            print(f"    Consensus keywords: {consensus_keywords}")
            print(f"    Consensus rate: {consensus_rate_extractor:.1f}%")
        
        # Save results
        print(f"\n💾 SAVING COMPREHENSIVE RESULTS...")
        
        # Save raw results
        df.to_excel("demo_consensus_raw_results.xlsx", index=False)
        print(f"✅ Raw results: demo_consensus_raw_results.xlsx")
        
        # Save consensus analysis
        consensus_display = consensus_df.copy()
        consensus_display['extractors'] = consensus_display['extractors'].apply(lambda x: ', '.join(x))
        consensus_display['extractor_details'] = consensus_display['extractor_details'].apply(str)
        consensus_display.to_excel("demo_consensus_analysis.xlsx", index=False)
        print(f"✅ Consensus analysis: demo_consensus_analysis.xlsx")
        
        # Create comprehensive summary report
        create_summary_report(df, consensus_df, sections_texts_dict, topic, topic_information)
        print(f"✅ Summary report: extraction_summary_report.xlsx")
        
        print(f"\n🎉 CONSENSUS ANALYSIS COMPLETED!")
        print(f"Check the Excel files for detailed analysis and insights.")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

def explain_consensus_benefits():
    """Explain the benefits of consensus analysis."""
    print(f"\n📚 BENEFITS OF CONSENSUS ANALYSIS")
    print("=" * 50)
    
    print("""
    🎯 WHY CONSENSUS ANALYSIS MATTERS:
    
    1. 📊 ACCURACY ASSESSMENT:
       • Keyphrases found by multiple extractors are more reliable
       • Higher consensus = higher confidence in results
       • Helps identify the most important terms
    
    2. 🔍 QUALITY VALIDATION:
       • Cross-validation between different extraction methods
       • Reduces false positives from individual extractors
       • Identifies method-specific biases
    
    3. 📈 PERFORMANCE EVALUATION:
       • Compare extractor effectiveness
       • Identify which methods work best for your domain
       • Optimize extractor selection and configuration
    
    4. 🎛️ RESULT PRIORITIZATION:
       • High consensus keyphrases = highest priority
       • Medium consensus = domain-specific terms
       • Single extractor = potential noise or specialized terms
    
    📋 EXCEL OUTPUTS EXPLAINED:
    
    • Raw Results: All extracted keyphrases with source information
    • Consensus Analysis: Detailed consensus statistics for each keyphrase
    • Summary Report: Multi-sheet comprehensive analysis including:
      - Executive Summary: Key metrics and overview
      - High Consensus Keywords: Terms found by multiple extractors
      - Keywords by Extractor: Performance breakdown by method
      - Extractor Performance: Comparative analysis
      - Complete Analysis: Full consensus data
    
    🚀 HOW TO USE THE RESULTS:
    
    1. Focus on HIGH CONSENSUS keyphrases for core concepts
    2. Review MEDIUM CONSENSUS for domain-specific terms
    3. Investigate SINGLE EXTRACTOR terms for potential noise
    4. Use consensus rate to assess overall extraction quality
    5. Compare extractor performance to optimize your pipeline
    """)

if __name__ == "__main__":
    print("🚀 KEYPHRASE CONSENSUS ANALYSIS DEMONSTRATION")
    print("=" * 60)
    
    # Run consensus analysis demo
    demo_consensus_analysis()
    
    # Explain benefits
    explain_consensus_benefits()
    
    print(f"\n✅ DEMO COMPLETED!")
    print(f"The consensus analysis provides valuable insights into:")
    print(f"• Which keyphrases are most reliable (found by multiple extractors)")
    print(f"• How well different extraction methods agree")
    print(f"• Overall quality and accuracy of your extraction pipeline")
    print(f"• Performance comparison between different extractors")
