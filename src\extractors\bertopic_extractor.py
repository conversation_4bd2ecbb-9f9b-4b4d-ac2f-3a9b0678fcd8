# src/extractors/bertopic_extractor.py

from bertopic import BERTopic
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.cluster import KMeans
from sentence_transformers import SentenceTransformer
import re


class BERTopicExtractor:
    def __init__(self, top_n=30, n_clusters=5, embedding_model="all-MiniLM-L6-v2"):
        """
        BERTopic-based keyword extractor without HDBSCAN.
        Uses KMeans for clustering.
        """
        self.top_n = top_n
        self.n_clusters = n_clusters
        self.embedding_model = SentenceTransformer(embedding_model)

    def clean_phrase(self, phrase: str) -> str:
        """Remove extra spaces, newlines, and unwanted articles at start/end."""
        phrase = re.sub(r'\s+', ' ', phrase).strip()
        phrase = re.sub(r'^(a|an|the)\s+', '', phrase, flags=re.IGNORECASE)
        phrase = re.sub(r'\s+(a|an|the)$', '', phrase, flags=re.IGNORECASE)
        return phrase

    def extract(self, text: str):
        try:
            # Split text into sentences
            sentences = [s.strip() for s in re.split(r'[.!?]', text) if s.strip()]
            if not sentences:
                return []

            # Adjust cluster count based on available sentences
            n_clusters = min(self.n_clusters, len(sentences))
            if n_clusters < 2:
                return [] 

            # Quick token check before BERTopic
            test_vectorizer = CountVectorizer(ngram_range=(1, 3), stop_words="english")
            token_matrix = test_vectorizer.fit_transform(sentences)
            if token_matrix.shape[1] == 0:  # No valid features
                print("BERTopic skipped: no valid tokens after vectorization")
                return []

            # Create embeddings
            embeddings = self.embedding_model.encode(sentences)

            # Prepare models
            vectorizer_model = CountVectorizer(ngram_range=(1, 3), stop_words="english")
            kmeans_model = KMeans(
                n_clusters=n_clusters,
                random_state=42,
                n_init=10
            )

            # Run BERTopic with KMeans
            topic_model = BERTopic(
                embedding_model=self.embedding_model,
                vectorizer_model=vectorizer_model,
                verbose=False,
                nr_topics=n_clusters,
                calculate_probabilities=False,
                min_topic_size=1,
                hdbscan_model=kmeans_model
            )

            topics, _ = topic_model.fit_transform(sentences, embeddings)
            print(f"{topics =}")
            # Extract keyphrases
            keyphrases = set()
            for topic in topic_model.get_topics().values():
                for kw, _ in topic[:self.top_n]:
                    cleaned = self.clean_phrase(kw)
                    if cleaned:
                        keyphrases.add(cleaned)

            return list(keyphrases)[:self.top_n]

        except Exception as e:
            print(f"BERTopic error: {e}")
            return []





if __name__ == "__main__":
    text = """
    Solar energy storage systems are advancing rapidly, including the development of hydrogen fuel storage, 
    advanced batteries, and thermal energy conversion devices. Applications in renewable energy systems are 
    critical for sustainable energy development.
    """

    extractor = BERTopicExtractor(top_n=5, n_clusters =3) #, nr_topics=5
    phrases = extractor.extract(text)
    print("BERTopic Keyphrases:", phrases)
