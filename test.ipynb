{"cells": [{"cell_type": "code", "execution_count": null, "id": "c2185533", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import yaml\n", "import yake\n", "import spacy\n", "from sentence_transformers import SentenceTransformer, util\n", "from keybert import KeyBERT\n", "from pathlib import Path\n", "import os\n", "import json\n", "import pprint\n", "\n", "def debug(msg):\n", "    print(f\"[DEBUG] {msg}\")\n", "\n", "# ==========================\n", "# Load config\n", "# ==========================\n", "def load_config(config_path):\n", "    debug(f\"Loading config from: {config_path}\")\n", "    with open(config_path, \"r\", encoding=\"utf-8\") as f:\n", "        config = yaml.safe_load(f)\n", "    debug(f\"Config keys loaded: {list(config.keys())}\")\n", "    return config\n", "\n", "# ==========================\n", "# Sentence Splitting\n", "# ==========================\n", "def split_into_sentences(text, spacy_model=\"en_core_web_sm\"):\n", "    nlp = spacy.load(spacy_model)\n", "    doc = nlp(text)\n", "    return [sent.text.strip() for sent in doc.sents if sent.text.strip()]\n", "\n", "# ==========================\n", "# Embedding & Retrieval\n", "# ==========================\n", "def check_model_structure(model_path):\n", "    modules_json_path = Path(model_path) / \"modules.json\"\n", "    if not modules_json_path.exists():\n", "        debug(f\"⚠ modules.json not found in {model_path}\")\n", "        return False\n", "    try:\n", "        with open(modules_json_path, \"r\", encoding=\"utf-8\") as f:\n", "            modules_data = json.load(f)\n", "        if not all(\"path\" in m and \"name\" in m for m in modules_data):\n", "            debug(f\"⚠ modules.json missing 'path' or 'name' fields: {modules_data}\")\n", "            return False\n", "    except Exception as e:\n", "        debug(f\"⚠ Error reading modules.json: {e}\")\n", "        return False\n", "    return True\n", "\n", "def get_embedding_model(model_path):\n", "    debug(f\"Loading embedding model from: {model_path}\")\n", "    if not check_model_structure(model_path):\n", "        debug(\"⚠ Model folder is not in SentenceTransformers format. Model load may fail.\")\n", "    return SentenceTransformer(str(Path(model_path)))\n", "\n", "def retrieve_relevant_sentences_0(title, abstract, section_texts, model, top_k=5, similarity_threshold=0.35, spacy_model=\"en_core_web_sm\"):\n", "    query = f\"{title} {abstract}\"\n", "    query_emb = model.encode(query, convert_to_tensor=True)\n", "    relevant_sentences_by_section = {}\n", "\n", "    for section_name, text in section_texts.items():\n", "        sentences = split_into_sentences(text, spacy_model=spacy_model)\n", "        if not sentences:\n", "            continue\n", "        sentence_embs = model.encode(sentences, convert_to_tensor=True)\n", "        cos_scores = util.pytorch_cos_sim(query_emb, sentence_embs)[0]\n", "        top_results = torch.topk(cos_scores, k=min(top_k, len(sentences)))\n", "\n", "        filtered_sentences = []\n", "        for score, idx in zip(top_results.values, top_results.indices):\n", "            if score >= similarity_threshold:\n", "                filtered_sentences.append(sentences[idx])\n", "\n", "        debug(f\"Section '{section_name}': {len(filtered_sentences)} relevant sentences found.\")\n", "        relevant_sentences_by_section[section_name] = filtered_sentences\n", "\n", "    return relevant_sentences_by_section\n", "\n", "def retrieve_relevant_sentences(\n", "    title,\n", "    abstract,\n", "    section_texts,\n", "    model,\n", "    top_k=5,\n", "    similarity_threshold=0.35,\n", "    spacy_model=\"en_core_web_sm\"\n", "):\n", "    \"\"\"\n", "    Returns: dict[str, list[str]]\n", "        Mapping section_name -> list of top sentences (strings), sorted by score desc.\n", "        Guaranteed at least 1 sentence per non-empty section.\n", "    \"\"\"\n", "    query = f\"{title} {abstract}\".strip()\n", "    # Use list to ensure shape (1, dim); normalize for cosine\n", "    query_emb = model.encode([query], convert_to_tensor=True, normalize_embeddings=True)\n", "    relevant_sentences_by_section = {}\n", "\n", "    for section_name, text in section_texts.items():\n", "        sentences = split_into_sentences(text, spacy_model=spacy_model)\n", "        if not sentences:\n", "            debug(f\"Section '{section_name}': no sentences after splitting.\")\n", "            relevant_sentences_by_section[section_name] = []\n", "            continue\n", "\n", "        # Encode and normalize sentence embeddings\n", "        sentence_embs = model.encode(sentences, convert_to_tensor=True, normalize_embeddings=True)\n", "\n", "        # Cosine similarity with normalized vectors = dot product\n", "        cos_scores = util.cos_sim(query_emb, sentence_embs)[0]  # shape: (num_sentences,)\n", "        if cos_scores.numel() == 0:\n", "            debug(f\"Section '{section_name}': cosine scores empty.\")\n", "            relevant_sentences_by_section[section_name] = []\n", "            continue\n", "\n", "        # Debug stats\n", "        max_score = float(torch.max(cos_scores).item())\n", "        min_score = float(torch.min(cos_scores).item())\n", "        avg_score = float(torch.mean(cos_scores).item())\n", "        debug(f\"Section '{section_name}': sim scores -> min={min_score:.3f}, avg={avg_score:.3f}, max={max_score:.3f}\")\n", "\n", "        # Take top-k indices\n", "        k = min(max(1, top_k), len(sentences))\n", "        top_results = torch.topk(cos_scores, k=k)\n", "        top_pairs = [(float(score), sentences[int(idx)]) for score, idx in zip(top_results.values, top_results.indices)]\n", "\n", "        # Filter by threshold but keep at least 1\n", "        filtered_pairs = [(s, sent) for s, sent in top_pairs if s >= similarity_threshold]\n", "        if not filtered_pairs:\n", "            # keep the best one even if below threshold\n", "            best_score, best_sent = max(top_pairs, key=lambda x: x[0])\n", "            filtered_pairs = [(best_score, best_sent)]\n", "\n", "        # Sort by score desc and return ONLY the sentences (strings)\n", "        filtered_pairs.sort(key=lambda x: x[0], reverse=True)\n", "        selected_sentences = [sent for _, sent in filtered_pairs]\n", "\n", "        relevant_sentences_by_section[section_name] = selected_sentences\n", "        debug(f\"Section '{section_name}': kept {len(selected_sentences)} sentence(s) (threshold={similarity_threshold}).\")\n", "\n", "    return relevant_sentences_by_section\n", "\n", "# ==========================\n", "# Keyphrase Extraction\n", "# ==========================\n", "def extract_keyphrases_yake(text, yake_params):\n", "    kw_extractor = yake.KeywordExtractor(\n", "        lan=yake_params.get(\"lan\", \"en\"),\n", "        n=yake_params.get(\"n\", 2),\n", "        dedupLim=yake_params.get(\"dedup_threshold\", 0.9),\n", "        top=yake_params.get(\"max_keywords\", 30)\n", "    )\n", "    keywords = kw_extractor.extract_keywords(text)\n", "    return [kw for kw, score in keywords]\n", "\n", "# def extract_keyphrases_keybert(text, model_path, top_n=10, ngram_range=(1, 3)):\n", "#     kw_model = KeyBERT(model=SentenceTransformer(str(Path(model_path))))\n", "#     print(f\"{kw_model =}\")\n", "#     keywords = kw_model.extract_keywords(\n", "#         text,\n", "#         keyphrase_ngram_range=ngram_range,\n", "#         stop_words='english',\n", "#         top_n=top_n\n", "#     )\n", "#     return [kw for kw, score in keywords]\n", "\n", "# ==========================\n", "# Main Pipeline\n", "# ==========================\n", "def process_pdf_segments(config, title, abstract, section_texts, top_k_sentences=20):\n", "    embedding_model = get_embedding_model(config[\"model_paths\"][\"sentence_transformer\"])\n", "    relevant_sentences_by_section = retrieve_relevant_sentences(\n", "        title,\n", "        abstract,\n", "        section_texts,\n", "        embedding_model,\n", "        top_k=top_k_sentences,\n", "        similarity_threshold=config.get(\"embedding\", {}).get(\"similarity_threshold\", 0.5),\n", "        spacy_model=config.get(\"extractors\", {}).get(\"positionrank\", {}).get(\"spacy_model\", \"en_core_web_sm\")\n", "    )\n", "\n", "    print(f\"{relevant_sentences_by_section =}\")\n", "\n", "    all_relevant_sentences = []\n", "    for sec, sents in relevant_sentences_by_section.items():\n", "        all_relevant_sentences.extend(sents)\n", "\n", "    combined_relevant_text = \" \".join(all_relevant_sentences)\n", "    combined_title_abstract = f\"{title} {abstract}\"\n", "\n", "    keyphrases_relevant_yake = extract_keyphrases_yake(combined_relevant_text, config[\"extractors\"][\"yake\"])\n", "    # keyphrases_relevant_keybert = extract_keyphrases_keybert(\n", "    #     combined_relevant_text,\n", "    #     config[\"extractors\"][\"keybert\"][\"model_path\"],\n", "    #     top_n=config[\"extractors\"][\"keybert\"][\"top_n\"]\n", "    # )\n", "\n", "    keyphrases_title_abs_yake = extract_keyphrases_yake(combined_title_abstract, config[\"extractors\"][\"yake\"])\n", "    # keyphrases_title_abs_keybert = extract_keyphrases_keybert(\n", "    #     combined_title_abstract,\n", "    #     config[\"extractors\"][\"keybert\"][\"model_path\"],\n", "    #     top_n=config[\"extractors\"][\"keybert\"][\"top_n\"]\n", "    # )\n", "\n", "    return {\n", "        \"relevant_sentences_by_section\": relevant_sentences_by_section,\n", "        \"keyphrases_relevant_sentences\": {\n", "            \"yake\": keyphrases_relevant_yake,\n", "            # \"keybert\": keyphrases_relevant_keybert\n", "        },\n", "        \"keyphrases_title_abstract\": {\n", "            \"yake\": keyphrases_title_abs_yake,\n", "            # \"keybert\": keyphrases_title_abs_keybert\n", "        }\n", "    }\n", "\n", "# ==========================\n", "#  MAIN block\n", "# # ==========================\n", "if __name__ == \"__main__\":\n", "    CONFIG_PATH = \"config/config.yaml\"\n", "    config = load_config(CONFIG_PATH)\n", "\n", "    title = \"Sarcopenia in chronic kidney disease: prevalence by different definitions and relationship with adipositye\"\n", "    abstract = \"This was a cross-sectional study with chronic kidney disease (CKD) patients under non-dialysis-dependent (NDD), hemodialysis (HD), and kidney transplant (KTx) treatment aimed to evaluate the prevalence of sarcopenia using the European Working Group on Sarcopenia in Older People (EWGSOP2) and the Foundation for the National Institutes of Health (FNIH) guidelines, and to analyze the relationship between sarcopenia and its components and body adiposity. Body composition was assessed by dual-energy X-ray absorptiometry and anthropometry. Bioelectrical impedance provided data on the phase angle and body water. The prevalence of sarcopenia in the total sample (n = 243; 53% men, 48 ± 10 years) was 7% according to the FNIH and 5% according to the EWGSOP2 criteria, and was low in each CKD group independently of the criteria applied (maximum 11% prevalence). Low muscle mass was present in 39% (FNIH) and 36% (EWGSOP2) and dynapenia in 10% of the patients. Patients who were sarcopenic according to the EWGSOP2 criteria presented low body adiposity. Conversely, patients who were sarcopenic according to the FNIH criteria presented high adiposity. This study suggests that in CKD (i) sarcopenia and low muscle mass prevalence varies according to the diagnostic criteria; (ii) sarcopenia and low muscle mass are common conditions; (iii) the association with body adiposity depends on the criteria used to define low muscle mass; and (iv) the FNIH criteria detected higher adiposity in individuals with sarcopenia.\"\n", "    section_texts = {\n", "        \"results\": \"Out of 458 patients in NDD, 113 met the eligibility criteria and 83 agreed to participate; out of 304 patients in HD, 107 met the eligibility criteria and 80 agreed to participate; and out of 578 patients in KTx, 150 met the eligibility criteria and 81 agreed to participate. Subjects had a mean age of 48 ± 10 years and 53% were men. For those that met the eligibility criteria but did not agree to participate (HD, n = 27; NDD, n = 30; and KTx, n = 69), the mean age was 51 ± 9 yearsand 55% were men. Of the total sample, 29% had diabetes mellitus, 68% had systemic arterial hypertension, and 22% had dyslipidemia. The eGFRs of the NDD and KTx groups were 19 ± 9 and 70±18 mL/min/1.73m2, respectively. Transplant time had a median of 82 months, from 7 to 307, with an interquartile range of 79 months. For the HD group, the total weekly Kt/V had a mean of 2 ± 1 with a median time of HD of 64 months, from 4 to 373, with an interquartile range of 72 months. The prevalence of sarcopenia, low muscle mass, and dynapenia according to both guidelines is presented in Fig. 1 for the total, male, and female samples and in SupplementalFig. S1 for each CKD subgroup. The frequency of low muscle mass was higher for women (p = 0.02) and for HD (NDD vs. HD, p = 0.00) on applying the EWGSOP2 criteria. Dynapenia prevalence was similar for the two criteria, and higher for NDD and HD compared with KTx for the EWGSOP2 (NDD vs. KTx, p = 0.03; HD vs. KTx, p = 0.04) and the FNIH criteria (NDD vs. KTx, p = 0.03; HD vs. KTx, p = 0.04). There was no difference for sarcopenia prevalence between sexes and CKD subgroups for both criteria. The agreement, according to kappa values (Table 1), for sarcopenia, low muscle mass, and dynapenia between the EWGSOP2 and FNIH criteria was moderate, slight, and almost perfect, respectively. Only for the HD group was the agreement for sarcopenia substantial and better than in the other CKD subgroups. Also, the correlation between ASMI and ASM/BMI, in the total sample, is presented in Supplemental Fig. S2. The participants’ demographic and clinical characteristics according to the presence of sarcopenia are summarized in Table 2. Supplementary Tables S1, S2, and S3 present the same analysis stratified by CKD subgroups NDD, HD, and KTx, respectively. Considering the EWGSOP2 criteria, participants presenting with sarcopenia had lower body weight, BCM, body water parameters, and PhA, with significantly more patients presenting with low PhA compared with those without sarcopenia. According to the FNIH criteria, participants with sarcopenia had lower ICW and BCM. Body adiposity parameters evaluated by anthropometry (BMI, WC, and WHtR) were significantly lower in the sarcopenia group compared with the control group, according to the EWGSOP2 criteria (including both sexes in the analyses) (Table 3). Conversely, according to the FNIH criteria, the sarcopenia group compared with the control group (including both sexes in the analyses) presented significantly higher values for the WHtR and the total and trunk body fat as assessed by DXA (Table 3). The frequency of sarcopenia, lowmusclemass, and dynapenia according to body adiposity is shown in Table 4. Individuals presenting with excessive total and abdominal body adiposity based on anthropometric measures and BF% by DXA presented a lower frequency of low muscle mass when it was defined by the EWGSOP2 criteria and a higher frequency when it was defined by the FNIH criteria. The frequency of dynapenia was not associated with body adiposity. The association between sarcopenia, lowmusclemass, and dynapenia with body adiposity was evaluated by odds ratio analyses (Table 5). With regard to low muscle mass diagnosis, when applying the EWGSOP2 criteria the odds ratios were significantly lower in individuals presenting with excess adiposity, whereas when applying the FNIH criteria there was an inverse association with higher odds ratios. Additionally, the correlation analysis between HGS and ASMI was 0.61 (p ≤ 0.000) and between HGS and ASM/BMI was 0.76 (p ≤ 0.000).\"\n", "        \n", "    }\n", "\n", "    output = process_pdf_segments(config, title, abstract, section_texts, top_k_sentences=3)\n", "    pprint.pprint(output)"]}, {"cell_type": "code", "execution_count": 1, "id": "cf6d8776", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["33\n", "20\n"]}], "source": ["raw_list = ['geothermal cogeneration', 'zero energy building', 'energy efficiency','cogeneration', 'exergy efficiency', 'geothermal energy', 'energy consumption', 'Rankine cycle','XAFS spectra', 'Fuel cell', 'Battery cathode', 'Thermoelectric devices', 'Density of states','XAFS spectra', 'battery cathode', 'electronic structure', 'intercalation', 'Fermi level','Solar energy', 'Fuel cell', 'Battery cathode', 'Thermoelectric devices', 'Hydrogen storage','XAFS spectra', 'battery cathode', 'intercalation', 'thermoelectric devices', 'Fermi level','XAFS spectra', 'battery cathode', 'electronic structure', 'intercalation', 'Fermi level']\n", "\n", "print(len(raw_list))\n", "print(len(set(raw_list)))\n"]}, {"cell_type": "code", "execution_count": null, "id": "1f5a90a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}