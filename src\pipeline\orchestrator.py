# # src/pipeline/orchestrator.py
# import os
# import ssl
# import yaml
# import pandas as pd
# from pathlib import Path

# from src.utils.file_io import read_text_file, ensure_dir
# from src.preprocess.cleaner import clean_and_split_sections
# from src.extractors.vocab_matcher import load_vocab, match_terms
# from src.extractors.yake_extractor import YakeExtractor
# from src.extractors.rake_extractor import RakeExtractor
# from src.semantic.embedder import EmbeddingModel
# from src.semantic.semantic_matcher import match_semantic_terms
# from sentence_transformers import SentenceTransformer

# # Disable SSL verification
# os.environ['PYTHONHTTPSVERIFY'] = '0'
# ssl._create_default_https_context = ssl._create_unverified_context

# def load_local_model(model_path):
#     """Load model from local directory with proper configuration"""
#     try:
#         # Set offline mode
#         os.environ['TRANSFORMERS_OFFLINE'] = '1'
#         os.environ['HF_HUB_OFFLINE'] = '1'
        
#         # Load model from local path
#         model = SentenceTransformer(model_path, device='cpu')
#         return model
#     except Exception as e:
#         print(f"Error loading local model: {e}")
#         # Fallback: create model with default pooling
#         from sentence_transformers.models import Transformer, Pooling
        
#         transformer = Transformer(model_path)
#         pooling = Pooling(
#             word_embedding_dimension=transformer.get_word_embedding_dimension(),
#             pooling_mode='mean'
#         )
#         model = SentenceTransformer(modules=[transformer, pooling])
#         return model



# def load_config(config_path):
#     with open(config_path, 'r') as f:
#         return yaml.safe_load(f)


# def run_pipeline_on_document(section_texts: dict, config: dict):
#     results = []

#     # Step 1: Clean & tokenize
#     cleaned_sentences = clean_and_split_sections(section_texts)
#     raw_full_text = " ".join(section_texts.values())

#     # Step 2: Vocab Match
#     vocab = load_vocab(config['paths']['curated_vocab'])
#     vocab_matches = match_terms(cleaned_sentences, vocab, config['extractors']['fuzzy_match_threshold'])
#     vocab_matches['method'] = 'vocab_fuzzy'

#     # Step 3: Statistical Extraction
#     yake_keywords = YakeExtractor(**config['extractors']['yake']).extract(raw_full_text)
#     # rake_keywords = RakeExtractor(**config['extractors']['rake']).extract(raw_full_text)

#     # Step 4: Semantic Embedding Match - USE LOCAL MODEL
#     try:
#         local_model = load_local_model(config['model_paths']['sentence_transformer'])
        
#         # Load vocab embeddings and encode input
#         vocab_terms, vocab_embeddings = load_vocab_embeddings(config['paths']['embedding_store'])
#         input_embeddings = local_model.encode(cleaned_sentences)
        
#         semantic_matches = match_semantic_terms(
#             cleaned_sentences, input_embeddings,
#             vocab_terms, vocab_embeddings,
#             config['embedding']['similarity_threshold']
#         )
#         # print(f"{semantic_matches =}")
#         semantic_matches['method'] = 'semantic_embed'
#     except Exception as e:
#         print(f"Warning: Semantic matching failed: {e}")
#         semantic_matches = pd.DataFrame()

#     # Step 5: Combine and return
#     if not vocab_matches.empty:
#         results.append(vocab_matches)
#     if not semantic_matches.empty:
#         results.append(semantic_matches)

#     if results:
#         combined_df = pd.concat(results, ignore_index=True)
#     else:
#         combined_df = pd.DataFrame()

#     combined_df['yake_keywords'] = ", ".join(yake_keywords)
#     # combined_df['rake_keywords'] = ", ".join(rake_keywords)
#     combined_df.drop_duplicates(inplace=True)
#     return combined_df

# def load_vocab_embeddings(embedding_store_path):
#     """Load pre-computed vocab embeddings from local storage"""
#     # Implementation depends on how embeddings are stored
#     # This might be a pickle file, numpy file, or other format
#     import pickle
#     with open(embedding_store_path, 'rb') as f:
#         data = pickle.load(f)
#     return data['terms'], data['embeddings']


##########################################

# src/pipeline/orchestrator.py
import os
import ssl
import yaml
import pandas as pd
# from pathlib import Path

# from src.utils.file_io import read_text_file, ensure_dir
from src.preprocess.cleaner import clean_and_split_sections
from src.extractors.vocab_matcher import  matched_vocab_terms  #load_vocab,
from src.extractors.yake_extractor import YakeExtractor
from src.extractors.keybert_extractor import KeyBertExtractor
# from src.extractors.textrank_extractor import TextRankExtractor
# from src.extractors.position_rank_extractor import PositionRankExtractor
# from src.extractors.keyphrase_extractor import KeyphraseExtractor

# from src.semantic.embedder import EmbeddingModel
from src.semantic.semantic_matcher import match_semantic_terms
from sentence_transformers import SentenceTransformer

# Disable SSL verification
os.environ['PYTHONHTTPSVERIFY'] = '0'
ssl._create_default_https_context = ssl._create_unverified_context


def load_local_model(model_path):
    """Load model from local directory with proper configuration"""
    try:
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_HUB_OFFLINE'] = '1'
        model = SentenceTransformer(model_path, device='cpu')
        return model
    except Exception as e:
        print(f"Error loading local model: {e}")
        from sentence_transformers.models import Transformer, Pooling
        transformer = Transformer(model_path)
        pooling = Pooling(
            word_embedding_dimension=transformer.get_word_embedding_dimension(),
            pooling_mode='mean'
        )
        return SentenceTransformer(modules=[transformer, pooling])


def load_config(config_path):
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def run_pipeline_on_document(section_texts: dict, config: dict):
    results = []

    # Step 1: Clean & tokenize
    cleaned_sentences = clean_and_split_sections(section_texts)
    raw_full_text = " ".join(section_texts.values())

    # Step 2: Vocab Match
    # vocab = load_vocab(config['paths']['curated_vocab'])
    # vocab_matches = matched_vocab_terms(cleaned_sentences, config['extractors']['fuzzy_match_threshold'])
    vocab_matches = matched_vocab_terms(cleaned_sentences, config, threshold=config['extractors']['fuzzy_match_threshold'])

    vocab_matches['method'] = 'vocab_fuzzy'

    # Step 3: Statistical Extraction (multiple extractors)
    yake_keywords = YakeExtractor(**config['extractors']['yake']).extract(raw_full_text)
    keybert_keywords = KeyBertExtractor(**config['extractors']['keybert']).extract(raw_full_text)
    # textrank_keywords = TextRankExtractor(**config['extractors']['textrank']).extract(raw_full_text)
    # positionrank_keywords = PositionRankExtractor(**config['extractors']['positionrank']).extract(raw_full_text)
    #keyphrase_keywords = KeyphraseExtractor(
        #model_path=config['extractors']['keyphrase']['model_path'],
        #top_n=config['extractors']['keyphrase']['top_n']
    # ).extract(raw_full_text)

    # Step 4: Semantic Matching
    local_model = load_local_model(config['model_paths']['sentence_transformer'])
    vocab_terms, vocab_embeddings = load_vocab_embeddings(config['paths']['embedding_store'])
    input_embeddings = local_model.encode(cleaned_sentences)

    semantic_matches = match_semantic_terms(
        cleaned_sentences, input_embeddings,
        vocab_terms, vocab_embeddings,
        config['embedding']['similarity_threshold']
    )
    semantic_matches['method'] = 'semantic_embed'

    # Step 5: Combine
    if not vocab_matches.empty:
        results.append(vocab_matches)
    if not semantic_matches.empty:
        results.append(semantic_matches)

    combined_df = pd.concat(results, ignore_index=True) if results else pd.DataFrame()

    # Add keyword columns
    combined_df['yake_keywords'] = " | ".join(yake_keywords)
    combined_df['keybert_keywords'] = " | ".join(keybert_keywords)

    
    # combined_df['textrank_keywords'] = " | ".join(textrank_keywords)
    # combined_df['positionrank_keywords'] = " | ".join(positionrank_keywords)
    # combined_df['keyphrase_keywords'] = " | ".join(keyphrase_keywords)

    combined_df.drop_duplicates(inplace=True)
    return combined_df


def load_vocab_embeddings(embedding_store_path):
    import pickle
    with open(embedding_store_path, 'rb') as f:
        data = pickle.load(f)
    return data['terms'], data['embeddings']
