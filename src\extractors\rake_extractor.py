# # src/extractors/rake_extractor.py
# from rake_nltk import Rake
# # import nltk
# # nltk.download('punkt')

# import nltk

# nltk.download('punkt', download_dir='nltk_data')
# nltk.download('stopwords', download_dir='nltk_data')

# class RakeExtractor:
#     def __init__(self, max_words=3):
#         self.rake = Rake(max_length=max_words)

#     def extract(self, text):
#         self.rake.extract_keywords_from_text(text)
#         ranked_phrases = self.rake.get_ranked_phrases()
#         return [phrase.strip() for phrase in ranked_phrases if phrase.strip() != ""]



# src/extractors/rake_extractor.py

from rake_nltk import Rake
import nltk
import os

# Append local nltk_data path to avoid online download
NLTK_DATA_PATH = os.path.join(os.path.dirname(__file__), "..", "..", "nltk_data")
nltk.data.path.append(NLTK_DATA_PATH)

# No downloads here; assumes you've placed the resources in nltk_data
# For example:
# nltk_data/
# ├── tokenizers/punkt
# └── corpora/stopwords

# class RakeExtractor:
#     def __init__(self, max_words=3):
#         self.rake = Rake(max_length=max_words)

#     def extract(self, text):
#         self.rake.extract_keywords_from_text(text)
#         ranked_phrases = self.rake.get_ranked_phrases()
#         return [phrase.strip() for phrase in ranked_phrases if phrase.strip() != ""]


# class RakeExtractor:
#     def __init__(self, max_length=4):  # match config keyword
#         self.rake = Rake(max_length=max_length)

#     def extract(self, text):
#         self.rake.extract_keywords_from_text(text)
#         ranked_phrases = self.rake.get_ranked_phrases()
#         return [phrase.strip() for phrase in ranked_phrases if phrase.strip()]


from rake_nltk import Rake

class RakeExtractor:
    def __init__(self, max_length=4, max_keywords=30):  # match config keys
        self.rake = Rake(max_length=max_length)
        self.max_keywords = max_keywords

    def extract(self, text):
        self.rake.extract_keywords_from_text(text)
        ranked_phrases = self.rake.get_ranked_phrases()
        cleaned_phrases = [phrase.strip() for phrase in ranked_phrases if phrase.strip()]
        return cleaned_phrases[:self.max_keywords]  # limit to top N
