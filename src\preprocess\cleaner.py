# src/preprocess/cleaner.py
import re
import string

import nltk
from nltk.corpus import stopwords
# nltk.download('stopwords')

stop_words = set(stopwords.words('english'))


def clean_text(text):
    """Lowercase, remove punctuation, extra spaces, and stopwords."""
    text = text.lower()
    text = re.sub(r'\s+', ' ', text)
    text = text.translate(str.maketrans('', '', string.punctuation))
    tokens = text.split()
    tokens = [word for word in tokens if word not in stop_words]
    return ' '.join(tokens)


def clean_and_split_sections(section_dict):
    """
    Clean multiple sections from a dictionary (e.g., title, abstract, etc.).
    Return a flat list of cleaned sentences.
    """
    cleaned = []
    for section, content in section_dict.items():
        if not content:
            continue
        for line in content.split(". "):
            cleaned.append(clean_text(line.strip()))
    return cleaned
