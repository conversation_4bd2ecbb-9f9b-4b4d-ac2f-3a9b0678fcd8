# # src/extractors/suma_extractor.py

# from summa import keywords
# from summa import keywords as summa_keywords

# class SumaKeyphraseExtractor:
#     def __init__(self, top_n=10):
#         self.top_n = top_n

#     def extract(self, text):
#         try: 
#             kw = keywords.keywords(text, words=self.top_n, split=True)
#             return [k.strip() for k in kw if k.strip()]
#         except Exception as e:
#             print(f"Error extracting keywords: {e}")
#             return []
# 



# # src/extractors/suma_extractor.py
from summa import keywords
from summa import keywords as summa_keywords
import yaml


class SumaKeyphraseExtractor:
    def __init__(self, top_n=5):
        self.top_n = top_n

    def extract(self, text):
        try:
            kw = summa_keywords.keywords(text, words=self.top_n, split=True)
            return [k.strip() for k in kw if k.strip()]
        except Exception as e:
            print(f"Summa error: {e}")
            return []

if __name__ == "__main__":

    def load_config(config_path):
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
        
    sample_text = """
    Solar energy is one of the most promising renewable energy sources due to its abundance 
    and sustainability. Recent advances in photovoltaic cell design, energy storage systems, 
    and thermal collectors have significantly improved solar energy efficiency. Moreover, 
    integrating solar power with hydrogen fuel storage offers a pathway for long-term, 
    large-scale energy solutions. However, challenges remain in storage technology, 
    distribution infrastructure, and overall system costs.
    """
    config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")


    extractor = SumaKeyphraseExtractor(**config["extractors"]["suma"])
    # extractor = SumaKeyphraseExtractor(top_n=10)
    key_phrases = extractor.extract(sample_text)

    print("Extracted Keyphrases:")
    for i, phrase in enumerate(key_phrases, start=1):
        print(f"{i}. {phrase}")
