# src/extractors/keyphrase_extractor.py

from transformers import (
    AutoTokenizer,
    AutoModelForTokenClassification,
    TokenClassificationPipeline
)
from transformers.pipelines import AggregationStrategy
import numpy as np

class KeyphraseExtractionPipeline(TokenClassificationPipeline):
    def __init__(self, model_path, *args, **kwargs):
        model = AutoModelForTokenClassification.from_pretrained(model_path, local_files_only=True)
        tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)
        super().__init__(model=model, tokenizer=tokenizer, *args, **kwargs)

    def postprocess(self, all_outputs):
        results = super().postprocess(
            all_outputs=all_outputs,
            # aggregation_strategy=AggregationStrategy.SIMPLE,
            aggregation_strategy=AggregationStrategy.FIRST,
            # aggregation_strategy=AggregationStrategy.MAX,

        )
        return np.unique([result.get("word").strip() for result in results if result.get("word")])


class KeyphraseExtractor:
    def __init__(self, model_path, top_n=30):
        self.pipeline = KeyphraseExtractionPipeline(model_path=model_path)
        self.top_n = top_n

    def extract(self, text):
        keywords = self.pipeline(text)
        return list(keywords)[:self.top_n]
