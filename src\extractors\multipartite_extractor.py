
import pke
import yaml

class MultipartiteRankExtractor:
    def __init__(self, top_n=10):
        self.top_n = top_n

    def extract(self, text):
        try:
            extractor = pke.unsupervised.MultipartiteRank()
            extractor.load_document(input=text, language="en")
            pos = {'NOUN', 'PROPN', 'ADJ'}
            extractor.candidate_selection(pos=pos)
            extractor.candidate_weighting(alpha=1.1, threshold=0.75, method='average')
            return [kw for kw, score in extractor.get_n_best(n=self.top_n)]
        except Exception as e:
            print(f"MultipartiteRank error: {e}")
            return []


if __name__ == "__main__":

    def load_config(config_path):
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
        
    sample_text = """
    Solar energy is one of the most promising renewable energy sources due to its abundance 
    and sustainability. Recent advances in photovoltaic cell design, energy storage systems, 
    and thermal collectors have significantly improved solar energy efficiency. Moreover, 
    integrating solar power with hydrogen fuel storage offers a pathway for long-term, 
    large-scale energy solutions. However, challenges remain in storage technology, 
    distribution infrastructure, and overall system costs.
    """
    config = load_config(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml")


    extractor = MultipartiteRankExtractor(**config["extractors"]["multipartiterank"])
    # extractor = SumaKeyphraseExtractor(top_n=10)
    key_phrases = extractor.extract(sample_text)

    print("Extracted Keyphrases:")
    for i, phrase in enumerate(key_phrases, start=1):
        print(f"{i}. {phrase}")