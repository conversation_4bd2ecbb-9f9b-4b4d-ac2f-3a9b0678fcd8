# src/extractors/keyphrase_extractor.py

from transformers import (
    AutoTokenizer,
    AutoModelForTokenClassification,
    TokenClassificationPipeline
)
from transformers.pipelines import AggregationStrategy
import numpy as np
import os


class KeyphraseExtractionPipeline(TokenClassificationPipeline):
    def __init__(self, model_path, *args, **kwargs):
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model path not found: {model_path}")
        model = AutoModelForTokenClassification.from_pretrained(model_path, local_files_only=True)
        tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)
        super().__init__(model=model, tokenizer=tokenizer, *args, **kwargs)

    def postprocess(self, all_outputs):
        results = super().postprocess(
            all_outputs=all_outputs,
            aggregation_strategy=AggregationStrategy.SIMPLE,
        )

        # Extract (word, score) pairs
        filtered = []
        for result in results:
            word = result.get("word", "").strip()
            score = result.get("score", 0.0)
            if word:
                filtered.append((word, score))

        # Keep highest score per unique keyword
        keyword_score_map = {}
        for word, score in filtered:
            if word not in keyword_score_map or score > keyword_score_map[word]:
                keyword_score_map[word] = score

        # Sort by descending score
        sorted_keywords = sorted(keyword_score_map.items(), key=lambda x: x[1], reverse=True)
        return sorted_keywords


class KeyphraseExtractor:
    def __init__(self, model_path: str, top_n: int = 30, score_threshold: float = 0.0):
        """
        Args:
            model_path (str): Path to local HuggingFace model directory.
            top_n (int): Number of top keywords to extract.
            score_threshold (float): Minimum score to include a keyword.
        """
        self.pipeline = KeyphraseExtractionPipeline(model_path=model_path)
        self.top_n = top_n
        self.score_threshold = score_threshold

    def extract(self, text, return_scores=False):
        keyword_scores = self.pipeline(text)
        # Filter by score threshold
        keyword_scores = [(kw, score) for kw, score in keyword_scores if score >= self.score_threshold]
        # Limit to top N
        keyword_scores = keyword_scores[:self.top_n]
        if return_scores:
            return keyword_scores
        return [kw for kw, _ in keyword_scores]
