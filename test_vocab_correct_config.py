#!/usr/bin/env python3
"""
Test vocab_matcher with the correct config structure that matches working vocab_matcher.py
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor
import os

def test_vocab_with_correct_config():
    """Test vocab_matcher with the corrected config structure."""
    
    print("🧪 TESTING VOCAB_MATCHER WITH CORRECT CONFIG")
    print("=" * 55)
    
    # Sample text with terms that should match vocabulary
    sample_text = """
    Chemical compounds and catalysts are used in renewable energy systems.
    Solar panels and wind turbines generate sustainable power.
    Energy storage technologies improve grid stability and efficiency.
    Polymer materials and synthesis methods are important for development.
    """
    
    print(f"📄 Sample text: {sample_text.strip()}")
    
    try:
        # Initialize extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Check the corrected config structure
        print(f"\n📊 CORRECTED CONFIG STRUCTURE:")
        vocab_config = extractor.config["extractors"]["vocab_matcher"]
        paths_config = extractor.config.get("paths", {})
        
        print(f"  vocab_matcher.threshold: {vocab_config.get('threshold')}")
        print(f"  vocab_matcher.embedding_model: {vocab_config.get('embedding_model')}")
        print(f"  paths.embedding_store: {paths_config.get('embedding_store')}")
        
        # Check if files exist
        model_path = vocab_config.get("embedding_model", {}).get("model_path", "")
        embedding_store = paths_config.get("embedding_store", "")
        
        print(f"\n📁 FILE EXISTENCE CHECK:")
        if model_path and os.path.exists(model_path):
            print(f"  ✅ Model path exists: {model_path}")
        elif model_path:
            print(f"  ❌ Model path not found: {model_path}")
        else:
            print(f"  ⚠️  No model path configured")
            
        if embedding_store and os.path.exists(embedding_store):
            print(f"  ✅ Embedding store exists: {embedding_store}")
        elif embedding_store:
            print(f"  ❌ Embedding store not found: {embedding_store}")
        else:
            print(f"  ⚠️  No embedding store configured")
        
        # Test vocab extraction
        print(f"\n🔍 TESTING VOCAB_MATCHER EXTRACTION:")
        vocab_keywords = extractor._extract_vocab_keywords(sample_text)
        
        print(f"\n📊 VOCAB_MATCHER RESULTS:")
        print(f"  Keywords found: {len(vocab_keywords)}")
        if vocab_keywords:
            print(f"  Keywords: {sorted(list(vocab_keywords))}")
            print(f"  ✅ SUCCESS: Vocab_matcher is working with correct config!")
            return True
        else:
            print(f"  ⚠️  No keywords found")
            print(f"  💡 This could be due to:")
            print(f"     • No vocabulary terms match the input text")
            print(f"     • Similarity threshold too high (try lowering to 0.5-0.7)")
            print(f"     • Missing or incompatible embedding store file")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_configs():
    """Compare the unified extractor config with working vocab_matcher config."""
    print(f"\n🔍 COMPARING CONFIGURATIONS")
    print("=" * 35)
    
    try:
        # Load working config
        from src.extractors.vocab_matcher import load_config
        config_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml"
        
        if os.path.exists(config_path):
            working_config = load_config(config_path)
            
            print(f"📋 WORKING CONFIG (vocab_matcher.py):")
            print(f"  Model path: {working_config['extractors']['vocab_matcher']['embedding_model']['model_path']}")
            print(f"  Embedding store: {working_config['paths']['embedding_store']}")
            print(f"  Threshold: {working_config['extractors']['vocab_matcher']['threshold']}")
            
            # Load unified extractor config
            extractor = UnifiedKeyphraseExtractor()
            
            print(f"\n📋 UNIFIED EXTRACTOR CONFIG:")
            vocab_config = extractor.config["extractors"]["vocab_matcher"]
            paths_config = extractor.config.get("paths", {})
            
            print(f"  Model path: {vocab_config.get('embedding_model', {}).get('model_path', 'NOT SET')}")
            print(f"  Embedding store: {paths_config.get('embedding_store', 'NOT SET')}")
            print(f"  Threshold: {vocab_config.get('threshold', 'NOT SET')}")
            
            # Check if they match
            working_model = working_config['extractors']['vocab_matcher']['embedding_model']['model_path']
            unified_model = vocab_config.get('embedding_model', {}).get('model_path', '')
            
            working_store = working_config['paths']['embedding_store']
            unified_store = paths_config.get('embedding_store', '')
            
            print(f"\n🔍 COMPARISON:")
            if working_model == unified_model:
                print(f"  ✅ Model paths match")
            else:
                print(f"  ❌ Model paths differ:")
                print(f"     Working: {working_model}")
                print(f"     Unified: {unified_model}")
            
            if working_store == unified_store:
                print(f"  ✅ Embedding store paths match")
            else:
                print(f"  ❌ Embedding store paths differ:")
                print(f"     Working: {working_store}")
                print(f"     Unified: {unified_store}")
                
        else:
            print(f"❌ Working config file not found: {config_path}")
            
    except Exception as e:
        print(f"❌ Config comparison failed: {e}")

def test_standalone_for_reference():
    """Test standalone vocab_matcher for reference."""
    print(f"\n🔧 TESTING STANDALONE VOCAB_MATCHER (REFERENCE)")
    print("=" * 50)
    
    try:
        from src.extractors.vocab_matcher import matched_vocab_terms, load_config
        
        config_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml"
        
        if os.path.exists(config_path):
            config = load_config(config_path)
            
            sample_text = "chemical compounds catalysts renewable energy solar wind polymer synthesis"
            print(f"📄 Testing with: {sample_text}")
            
            matches = matched_vocab_terms(sample_text, config, threshold=0.5)
            print(f"📊 Standalone results: {len(matches)} matches")
            if matches:
                print(f"  Matches: {matches[:10]}...")  # Show first 10
            
        else:
            print(f"❌ Config file not found")
            
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")

if __name__ == "__main__":
    print("🚀 VOCAB_MATCHER CORRECT CONFIG TEST")
    print("=" * 45)
    
    # Compare configurations first
    compare_configs()
    
    # Test the corrected implementation
    success = test_vocab_with_correct_config()
    
    # Test standalone for reference
    test_standalone_for_reference()
    
    print(f"\n📋 SUMMARY:")
    if success:
        print(f"✅ Vocab_matcher is working with the corrected config structure")
        print(f"✅ Config now matches the working vocab_matcher.py format")
    else:
        print(f"⚠️  Vocab_matcher still not returning results")
        print(f"   Check the comparison above to ensure configs match exactly")
    
    print(f"\n🔧 KEY CHANGES MADE:")
    print(f"• Updated config structure to match working vocab_matcher.py")
    print(f"• Using paraphrase-mpnet-base-v2 model (not MiniLM)")
    print(f"• Proper nested embedding_model.model_path structure")
    print(f"• Separate paths.embedding_store configuration")
    
    print(f"\n💡 IF STILL NOT WORKING:")
    print(f"• Verify the embedding store file contains relevant vocabulary")
    print(f"• Try lowering the similarity threshold to 0.5 or 0.6")
    print(f"• Ensure the model path points to a valid SentenceTransformer model")
    print(f"• Check that input text contains terms similar to vocabulary")
