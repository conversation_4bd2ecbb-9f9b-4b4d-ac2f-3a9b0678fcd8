#!/usr/bin/env python3
"""
Simple test to verify vocab_matcher fix with standard SentenceTransformer model
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor
import os

def test_vocab_matcher_simple():
    """Test vocab_matcher with the fixed configuration."""
    
    print("🧪 TESTING VOCAB_MATCHER WITH STANDARD MODEL")
    print("=" * 55)
    
    # Sample text with common terms
    sample_text = """
    Renewable energy systems include solar panels and wind turbines.
    Energy storage technologies are important for grid stability.
    Chemical compounds and catalysts are used in energy conversion.
    """
    
    print(f"📄 Sample text: {sample_text.strip()}")
    
    try:
        # Initialize extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Check vocab config
        vocab_config = extractor.config["extractors"]["vocab_matcher"]
        print(f"\n📊 VOCAB_MATCHER CONFIG:")
        print(f"  Threshold: {vocab_config.get('threshold')}")
        print(f"  Original embedding_model: {vocab_config.get('embedding_model')}")
        print(f"  Embedding store: {vocab_config.get('embedding_store')}")
        
        # Check if embedding store exists
        embedding_store = vocab_config.get('embedding_store')
        if embedding_store and os.path.exists(embedding_store):
            print(f"  ✅ Embedding store found: {embedding_store}")
        else:
            print(f"  ❌ Embedding store not found: {embedding_store}")
            print(f"  💡 This test will fail without the embedding store file")
        
        # Test vocab extraction directly
        print(f"\n🔍 TESTING VOCAB_MATCHER EXTRACTION:")
        vocab_keywords = extractor._extract_vocab_keywords(sample_text)
        
        print(f"\n📊 VOCAB_MATCHER RESULTS:")
        print(f"  Keywords found: {len(vocab_keywords)}")
        if vocab_keywords:
            print(f"  Keywords: {sorted(list(vocab_keywords))}")
            print(f"  ✅ SUCCESS: Vocab_matcher is working!")
        else:
            print(f"  ⚠️  No keywords found (may be normal if no matches)")
        
        return len(vocab_keywords) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_standalone_vocab_matcher():
    """Test the standalone vocab_matcher for comparison."""
    print(f"\n🔧 TESTING STANDALONE VOCAB_MATCHER")
    print("=" * 40)
    
    try:
        from src.extractors.vocab_matcher import matched_vocab_terms, load_config
        
        config_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml"
        
        if os.path.exists(config_path):
            print(f"✅ Config file found")
            config = load_config(config_path)
            
            sample_text = "renewable energy solar wind chemical catalyst"
            print(f"📄 Testing with: {sample_text}")
            
            matches = matched_vocab_terms(sample_text, config, threshold=0.5)
            print(f"📊 Standalone results: {len(matches)} matches")
            if matches:
                print(f"  Matches: {matches}")
            
        else:
            print(f"❌ Config file not found")
            
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")

if __name__ == "__main__":
    print("🚀 VOCAB_MATCHER STANDARD MODEL FIX TEST")
    print("=" * 50)
    
    # Test the fix
    success = test_vocab_matcher_simple()
    
    # Test standalone for comparison
    test_standalone_vocab_matcher()
    
    print(f"\n📋 SUMMARY:")
    if success:
        print(f"✅ Vocab_matcher is working with standard SentenceTransformer model")
        print(f"✅ The fix successfully resolves the Pooling dimension error")
    else:
        print(f"⚠️  Vocab_matcher returned no results")
        print(f"   This could be normal if:")
        print(f"   • No vocabulary terms match the input text")
        print(f"   • Similarity threshold is too high")
        print(f"   • Embedding store file is missing")
    
    print(f"\n🔧 KEY FIX APPLIED:")
    print(f"• Changed from local model path to standard model name")
    print(f"• Using 'paraphrase-MiniLM-L6-v2' instead of local directory")
    print(f"• This avoids the SentenceTransformer loading issues")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"• Ensure vocab_embeddings.pkl exists in the correct location")
    print(f"• Adjust similarity threshold if needed (try 0.5-0.8)")
    print(f"• Verify vocabulary terms are relevant to your input text")
