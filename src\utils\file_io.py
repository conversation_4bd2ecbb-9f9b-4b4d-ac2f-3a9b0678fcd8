# src/utils/file_io.py
import os
import pandas as pd
import json
from pathlib import Path


def read_excel(file_path):
    """Read Excel file."""
    return pd.read_excel(file_path)


def read_text_file(file_path):
    """Read plain text file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()


def write_json(data, output_path):
    """Write data to JSON."""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4, ensure_ascii=False)


def write_csv(dataframe, output_path):
    """Write dataframe to CSV."""
    dataframe.to_csv(output_path, index=False)


def ensure_dir(directory):
    """Create directory if not exists."""
    Path(directory).mkdir(parents=True, exist_ok=True)


def list_files(directory, extensions=None):
    """List files in a directory with specific extensions."""
    extensions = extensions or ['.pdf', '.txt']
    return [str(p) for p in Path(directory).glob("**/*") if p.suffix in extensions]
