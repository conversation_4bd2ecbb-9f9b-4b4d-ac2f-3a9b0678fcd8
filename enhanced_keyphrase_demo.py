#!/usr/bin/env python3
"""
Enhanced Keyphrase Extraction Demo
Demonstrates the improved approach for extracting relevant keyphrases from scientific articles.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_training import (
    load_config, process_pdf_segments_enhanced, 
    get_top_keyphrases, evaluate_keyphrases_against_manual,
    load_manual_keyphrases
)
import pprint

def main():
    """Main demo function."""
    CONFIG_PATH = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\config\config.yaml"
    MANUAL_KEYPHRASES_PATH = "section_52_dataset_title_abstract_kw.xlsx"
    
    try:
        config = load_config(CONFIG_PATH)
    except FileNotFoundError:
        print("⚠️  Config file not found. Using default configuration.")
        config = {
            "model_paths": {
                "sentence_transformer": "all-MiniLM-L6-v2"
            },
            "extractors": {
                "yake": {
                    "lan": "en",
                    "n": 2,
                    "dedup_threshold": 0.9,
                    "max_keywords": 30
                }
            },
            "embedding": {
                "similarity_threshold": 0.35
            },
            "relevance": {
                "threshold": 0.3,
                "max_keyphrases": 20
            }
        }

    # Sample scientific article data
    title = "Sarcopenia in chronic kidney disease: prevalence by different definitions and relationship with adiposity"
    abstract = "Background: Sarcopenia is a syndrome characterized by progressive and generalized loss of skeletal muscle mass and strength. The prevalence of sarcopenia in chronic kidney disease (CKD) patients varies widely depending on the definition used. This study aimed to determine the prevalence of sarcopenia in CKD patients using different definitions and to examine its relationship with adiposity measures."
    
    section_texts = {
        "Results": "The study included 150 CKD patients (mean age 65.2 ± 12.4 years, 60% male). The prevalence of sarcopenia varied significantly depending on the definition used: 15.3% using EWGSOP2 criteria, 22.7% using AWGS criteria, and 28.0% using FNIH criteria. Sarcopenic patients had significantly lower BMI (22.1 ± 3.2 vs 25.8 ± 4.1 kg/m², p<0.001) and higher visceral fat area (120.5 ± 45.2 vs 98.3 ± 38.7 cm², p=0.02).",
        
        "Conclusion": "The prevalence of sarcopenia in CKD patients varies considerably depending on the diagnostic criteria used. Sarcopenia is associated with altered body composition, particularly reduced BMI and increased visceral adiposity. These findings highlight the importance of standardized diagnostic criteria for sarcopenia in CKD populations.",
        
        "Methods": "This cross-sectional study included adult CKD patients stages 3-5. Sarcopenia was diagnosed using three different criteria: European Working Group on Sarcopenia in Older People 2 (EWGSOP2), Asian Working Group for Sarcopenia (AWGS), and Foundation for the National Institutes of Health (FNIH). Body composition was assessed using dual-energy X-ray absorptiometry (DXA)."
    }

    print("="*80)
    print("🔬 ENHANCED KEYPHRASE EXTRACTION FOR SCIENTIFIC ARTICLES")
    print("="*80)
    print(f"📄 Title: {title}")
    print(f"📝 Abstract: {abstract[:100]}...")
    print(f"📊 Sections: {list(section_texts.keys())}")
    
    try:
        # Run enhanced extraction
        print("\n🚀 Running enhanced keyphrase extraction...")
        output = process_pdf_segments_enhanced(
            config, title, abstract, section_texts, 
            manual_keyphrases_path=MANUAL_KEYPHRASES_PATH,
            top_k_sentences=5
        )
        
        print("\n📊 EXTRACTION STATISTICS:")
        pprint.pprint(output["extraction_stats"])
        
        print("\n🎯 TOP RELEVANT KEYPHRASES (YAKE + Relevance Filtering):")
        top_yake = get_top_keyphrases(output["keyphrases_filtered"]["yake_relevant"], 15)
        for i, kp in enumerate(top_yake, 1):
            print(f"{i:2d}. {kp}")
        
        if output["keyphrases_filtered"]["keybert_relevant"]:
            print("\n🎯 TOP RELEVANT KEYPHRASES (KeyBERT + Relevance Filtering):")
            top_keybert = get_top_keyphrases(output["keyphrases_filtered"]["keybert_relevant"], 15)
            for i, kp in enumerate(top_keybert, 1):
                print(f"{i:2d}. {kp}")
        
        print("\n📝 MOST RELEVANT SENTENCES BY SECTION:")
        for section, sentences in output["relevant_sentences_by_section"].items():
            print(f"\n📋 {section}:")
            for i, sent in enumerate(sentences[:2], 1):  # Show top 2 sentences
                print(f"  {i}. {sent[:120]}...")
        
        # Show manual keyphrase validation if available
        if output["manual_keyphrases_available"]:
            print("\n✅ Manual keyphrases dataset is available for validation!")
        else:
            print("\n⚠️  Manual keyphrases dataset not found. Consider adding it for validation.")
            
    except Exception as e:
        print(f"\n❌ Error during extraction: {e}")
        print("This might be due to missing dependencies or model files.")
        print("Please ensure all required packages are installed.")
    
    print("\n" + "="*80)
    print("📋 RECOMMENDED APPROACH SUMMARY:")
    print("="*80)
    print("""
    ✅ BEST PRACTICES FOR KEYPHRASE EXTRACTION:
    
    1. 🎯 RELEVANCE FILTERING:
       - Use semantic similarity between keyphrases and title+abstract
       - Filter out irrelevant terms that don't match the main topic
       
    2. 🔄 MULTI-METHOD APPROACH:
       - Combine YAKE (unsupervised) + KeyBERT (transformer-based)
       - Use different algorithms to capture various types of keyphrases
       
    3. 📊 MANUAL VALIDATION:
       - Leverage your Excel dataset with manually extracted keyphrases
       - Train relevance classifiers using manual annotations
       
    4. ⚖️ BALANCED SCORING:
       - Weight extraction confidence + relevance score
       - Prioritize keyphrases that are both well-extracted and topically relevant
       
    5. 🎛️ CONFIGURABLE THRESHOLDS:
       - Adjust similarity thresholds based on domain requirements
       - Fine-tune maximum number of keyphrases per document
    """)

if __name__ == "__main__":
    main()
