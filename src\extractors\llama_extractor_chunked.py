# # llama_extractor_chunked.py

import re
import requests
import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from typing import Dict, List, Tuple, Optional



def validate_keyphrases_in_text(keyphrases: List[str], input_text: str) -> List[str]:
    """
    Validate that extracted keyphrases are actually present in the input text.

    Args:
        keyphrases: List of extracted keyphrases
        input_text: Original input text to validate against

    Returns:
        List of validated keyphrases that are present in the input text
    """
    if not keyphrases or not input_text:
        return []

    validated_keyphrases = []
    input_text_lower = input_text.lower()

    for keyphrase in keyphrases:
        keyphrase = keyphrase.strip()
        if not keyphrase:
            continue

        # Check if keyphrase exists in input text (case-insensitive)
        if keyphrase.lower() in input_text_lower:
            validated_keyphrases.append(keyphrase)
        # else:
        #     print(f"⚠️  Rejected keyphrase not found in input: '{keyphrase}'")

    return validated_keyphrases


def clean_llama_response(response_text, input_text=None):
    """
    Cleans the response text to return only a list of keyphrases.
    Optionally validates that keyphrases are present in input text.

    Args:
        response_text: Raw response from LLaMA
        input_text: Original input text for validation (optional)

    Returns:
        List of cleaned and validated keyphrases
    """
    lines = response_text.strip().splitlines()
    keywords = []

    for line in lines:
        matches = re.findall(r"[a-zA-Z0-9\- ]+", line)
        for match in matches:
            term = match.strip()
            if term and not term.lower().startswith("output") and not term.lower().startswith("input"):
                keywords.extend(term.split(','))

    # Clean and deduplicate
    cleaned_keywords = list({kw.strip() for kw in keywords if kw.strip()})

    # Validate against input text if provided
    if input_text:
        cleaned_keywords = validate_keyphrases_in_text(cleaned_keywords, input_text)

    return cleaned_keywords


def chunk_text(text, max_chars=1000):
    """Split long text into sentence-based chunks under `max_chars`."""
    import re
    sentences = re.split(r'(?<=[.!?])\s+', text.strip())
    chunks, current = [], ""
    for sentence in sentences:
        if len(current) + len(sentence) <= max_chars:
            current += " " + sentence
        else:
            chunks.append(current.strip())
            current = sentence
    if current:
        chunks.append(current.strip())
    return chunks


def chunk_segments(segments: dict, max_chars=1000):
    """
    Chunk non-title/abstract segments into paragraph/sentence chunks.
    """
    chunked = {}
    for seg_name, text in segments.items():
        seg_name_lower = seg_name.lower().strip()

        if seg_name_lower in ['title', 'abstract']:
            chunked[seg_name_lower] = text.strip()
        else:
            para_chunks = []
            paragraphs = text.split("\n\n")

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                if len(para) > max_chars:
                    para_chunks.extend(chunk_text(para, max_chars))
                else:
                    para_chunks.append(para)

            for i, chunk in enumerate(para_chunks):
                chunk_key = f"{seg_name_lower}_{i+1}"
                chunked[chunk_key] = chunk

    return chunked


# Note: filter_sentences_by_semantic_similarity is now implemented in unified_keyphrase_extractor.py
# A standalone version is provided at the end of this file for testing purposes


def get_cleaned_and_filtered_text_for_extractors(
    title: str,
    abstract: str,
    segments: Dict[str, str],
    model_name: str = "all-MiniLM-L6-v2",
    similarity_threshold: float = 0.3,
    top_k_per_segment: int = 10,
    include_original_title_abstract: bool = True
) -> Dict[str, str]:
    """
    Get cleaned and semantically filtered text suitable for other extractors.

    Args:
        title: Document title
        abstract: Document abstract
        segments: Dictionary of segment_name -> text
        model_name: SentenceTransformer model name
        similarity_threshold: Minimum similarity score
        top_k_per_segment: Maximum sentences per segment
        include_original_title_abstract: Whether to include original title/abstract

    Returns:
        Dictionary of segment_name -> cleaned_filtered_text for use by other extractors
    """
    # Get filtered sentences
    filtered_segments = filter_sentences_by_semantic_similarity(
        title=title,
        abstract=abstract,
        segments=segments,
        model_name=model_name,
        similarity_threshold=similarity_threshold,
        top_k_per_segment=top_k_per_segment
    )

    # Convert back to text format for other extractors
    cleaned_segments = {}

    # Include title and abstract if requested
    if include_original_title_abstract:
        if title:
            cleaned_segments['title'] = clean_and_form_sentences(title)
        if abstract:
            cleaned_segments['abstract'] = clean_and_form_sentences(abstract)

    # Add filtered content from other segments
    for segment_name, filtered_sentences in filtered_segments.items():
        if filtered_sentences:
            # Join filtered sentences into clean text
            cleaned_text = ' '.join(filtered_sentences)
            cleaned_segments[f"{segment_name}_filtered"] = cleaned_text

    return cleaned_segments


def clean_and_form_sentences(text: str) -> str:
    """
    Clean text and form proper sentences from potentially broken text with newlines.

    Args:
        text: Raw text that may have broken sentences due to newlines

    Returns:
        Cleaned text with properly formed sentences
    """
    if not text or not text.strip():
        return ""

    # Step 1: Basic cleaning
    cleaned = text.strip()

    # Step 2: Handle broken sentences due to newlines
    # Replace newlines that break sentences (not followed by capital letter or end of sentence)
    lines = cleaned.split('\n')
    reformed_lines = []

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # If this line doesn't end with sentence punctuation and next line doesn't start with capital
        if i < len(lines) - 1:
            next_line = lines[i + 1].strip()
            if (line and not line[-1] in '.!?' and
                next_line and not next_line[0].isupper() and
                not next_line.startswith(('Fig.', 'Table', 'Eq.', 'Section'))):
                # This line should be joined with the next
                reformed_lines.append(line + ' ')
            else:
                reformed_lines.append(line + ' ')
        else:
            reformed_lines.append(line)

    # Step 3: Join and clean up extra spaces
    reformed_text = ''.join(reformed_lines)
    reformed_text = re.sub(r'\s+', ' ', reformed_text)  # Multiple spaces to single space

    # Step 4: Fix common issues
    # Fix spaces before punctuation
    reformed_text = re.sub(r'\s+([.!?,:;])', r'\1', reformed_text)
    ## replace "\n\n" with space
    reformed_text = re.sub(r'\n\n', ' ', reformed_text)
    ## replace "\n" with space
    reformed_text = re.sub(r'\n', ' ', reformed_text)
    ## replace "  " with space
    reformed_text = re.sub(r'  ', ' ', reformed_text)
    # Fix missing spaces after punctuation
    reformed_text = re.sub(r'([.!?])([A-Z])', r'\1 \2', reformed_text)

    # Fix abbreviations that got broken
    reformed_text = re.sub(r'([A-Z])\s+([A-Z])\s+([A-Z])', r'\1\2\3', reformed_text)  # Fix broken acronyms

    return reformed_text.strip()


def _split_into_sentences(text: str) -> List[str]:
    """
    Split cleaned text into sentences using regex.

    Args:
        text: Cleaned text with proper sentence formation

    Returns:
        List of individual sentences
    """
    # First clean and form proper sentences
    cleaned_text = clean_and_form_sentences(text)

    if not cleaned_text:
        return []

    # Split into sentences
    sentences = re.split(r'(?<=[.!?])\s+', cleaned_text)

    # Filter and clean sentences
    valid_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        # Keep sentences that are meaningful (length > 15 chars, contain letters)
        if (sentence and len(sentence) > 15 and
            re.search(r'[a-zA-Z]', sentence) and
            not sentence.lower().startswith(('fig.', 'table', 'eq.', 'section'))):
            valid_sentences.append(sentence)

    return valid_sentences


def _fallback_sentence_filtering(segments: Dict[str, str], top_k: int) -> Dict[str, List[str]]:
    """Fallback filtering when semantic model is not available."""
    filtered = {}
    for segment_name, segment_text in segments.items():
        if segment_name.lower().strip() not in ['title', 'abstract']:
            sentences = _split_into_sentences(segment_text)
            # Take first top_k sentences as fallback
            filtered[segment_name] = sentences[:top_k]
    return filtered


def build_llama_prompt(sentence, topic_name=None, topic_info=None, sample_keyphrases=None, examples=None):
    """
    Build a full LLaMA prompt with topic context, sample keyphrases, and few-shot examples.
    """
    # instruction_0 = (
    #     "You are an expert scientific keyphrase extractor.\n"
    #     "Given a scientific sentence and topic, extract up to **6 most important keyphrases** "
    #     "that are strongly relevant to the topic.\n"
    #     "If there are no relevant keyphrases in the sentence, return an empty list: [].\n"
    #     "Always return the keyphrases as a **Python-style comma-separated list inside square brackets**.\n"
    #     "Do NOT include any explanation or extra text."
    # )

    instruction = (
        "You are an Expert SCIENTIFIC KEYPHRASE EXTRACTION assistant.\n\n"

        "CRITICAL INSTRUCTION: Extract keyphrases ONLY from the provided INPUT TEXT. "
        "DO NOT extract any terms from the examples below or from your knowledge. "
        "Every keyphrase MUST be present in the input text exactly as written.\n\n"

        "TASK: Extract **the most important scientific KEYPHRASES** that are:\n"
        "- Present in the input text\n"
        "- Strongly relevant to the given topic\n"
        "- Scientifically meaningful\n\n"

        "RULES:\n"
        "1. MANDATORY: Extract keyphrases ONLY from the input text provided below\n"
        "2. VERIFY: Each keyphrase must exist in the input text before including it\n"
        "3. Focus on keyphrases semantically related to the title and abstract topic\n"
        "4. Include repeated keyphrases if they are contextually important\n"
        "5. Prioritize domain-specific scientific terms, methods, materials, concepts\n"
        "6. Exclude pure numbers, common words, and irrelevant terms\n"
        "7. Return fewer keyphrases if the text has limited relevant content\n\n"

        "OUTPUT FORMAT: Return only a comma-separated Python list of keyphrases.\n"
        "DO NOT include explanations, commentary, or extra text.\n"
        "REMEMBER: Only extract what is actually present in the input text below."
    )



    topic_block = f"\n\n### Topic Name:\n{topic_name.strip()}" if topic_name else ""
    topic_info_block = f"\n\n### Topic Information:\n{topic_info.strip()}" if topic_info else ""

    sample_kws_block = ""
    if sample_keyphrases:
        formatted_kws = ', '.join(sample_keyphrases[:15])
        sample_kws_block = f"\n\n### Example Keyphrases from Topic:\n{formatted_kws}"

    examples_block = examples or """
        \n\n### Examples:
        Input: "In order to investigate the structural behaviours of Na4MnO4 and lithium substituted Na4MnO4 materials (Li2Na2MnO4) under conditions of increasing temperature, and to determine whether or not they possess the necessary characteristics to be a candidate for battery cathode, these materials' electronic and crystal structure properties were studied at temperatures of 300 K, 323 K, 343 K, 373 K, and 423 K. Studies on the electronic structure have shown that lithium substituted compounds and their parent materials both exhibit a stable electronic structure at high temperatures. The alkali metals (Na and Li) and also Mn are strongly linked to the oxygen atoms in the crystal, as evidenced by the durability of the crystal structure."
        Output: ['battery cathode', 'crystal structure', 'electronic structure']

        Input: "The density of states (DOS) calculation seems to show the electronic interaction between nearby Mn and O atoms that leads to hybridised levels."
        Output: ['density of states']

        Input: "Fig. 5 (a), in contrast, displays scattering data that was taken from the extended part of the XAFS data (EXAFS), which was produced from fluctuations at the tail section of the spectra. The scattering mechanism of the photoelectrons released from the source atom is what causes the oscillations in the tail portion of the XAFS spectra."
        Output: ['XAFS spectra', 'photoelectrons', 'EXAFS spectra']

        Input: "Due to the intercalation mechanism, the close binding of metals with oxygen atoms can cause issues with cathodic characteristics. Although the examined materials lacked the necessary characteristics to serve as a battery cathode, high electronic data loss with rising temperature circumstances suggested potential for good thermoelectric performances at room temperature with an unaffected, stable crystal structure. The comparative spectra in Fig. 4 (a) have provided extremely valuable information regarding the electronic interaction near the oxygen atoms of the investigated sample. The transition of excited 1s core electrons to unoccupied states above the Fermi level as a final state, i.e., 1s —>2p, is what creates the O K-edge. The Mn 3d-O 2p hybridised state caused the spectra to sharply peak at 529.77 eV as they began to rise."
        Output: ['intercalation', 'crystal structure', 'Fermi level']
        """

    prompt = (
        f"{instruction}"
        f"{topic_block}"
        f"{topic_info_block}"
        f"{sample_kws_block}"
        f"{examples_block}"
        f"\n\nInput: \"{sentence}\"\nOutput:"
    )

    return prompt


def extract_llama_keyphrases_from_segments(
    api_url: str,
    segments: dict,
    topic_name = None,
    topic_info= None,
    sample_keywords = None,
    max_chars: int = 1000
) -> pd.DataFrame:
    """
    Extract keyphrases for each segment or chunk using the LLaMA model.
    
    Args:
        api_url (str): URL to your LLaMA inference endpoint.
        segments (dict): Dictionary with keys like "title", "abstract", "methods", etc.
        topic_name (str): Name of the domain/topic.
        topic_info (str): Detailed topic background.
        sample_keywords (list): Example keywords from the topic.
        max_chars (int): Max chunk size for long segments.

    Returns:
        pd.DataFrame with columns: ['segment', 'text', 'keyphrases']
    """
    chunked_segments = chunk_segments(segments, max_chars=max_chars)
    results = []

    for segment_name, text_chunk in chunked_segments.items():
        prompt = build_llama_prompt(
            sentence=text_chunk,
            topic_name=topic_name,
            topic_info=topic_info,
            sample_keyphrases=sample_keywords
        )

        try:
            response = requests.post(api_url, json={"prompt": prompt})
            response.raise_for_status()
            raw_output = response.text.strip()
            phrases = clean_llama_response(raw_output, input_text=text_chunk)

            results.append({
                "segment": segment_name,
                "text": text_chunk,
                "keyphrases": phrases
            })

        except Exception as e:
            print(f"[⚠️] LLaMA error for segment: {segment_name[:30]}... | {e}")
            results.append({
                "segment": segment_name,
                "text": "text_chunk",
                "keyphrases": []
            })
            
    return pd.DataFrame(results)


def extract_llama_keyphrases_with_semantic_filtering(
    api_url: str,
    segments: Dict[str, str],
    topic_name: Optional[str] = None,
    topic_info: Optional[str] = None,
    sample_keywords: Optional[List[str]] = None,
    max_chars: int = 1000,
    similarity_threshold: float = 0.3,
    top_k_sentences: int = 10,
    model_name: str = "all-MiniLM-L6-v2"
) -> pd.DataFrame:
    """
    Enhanced LLaMA keyphrase extraction with semantic filtering.

    Args:
        api_url: URL to LLaMA inference endpoint
        segments: Dictionary with 'title', 'abstract', and other segments
        topic_name: Name of the domain/topic
        topic_info: Detailed topic background
        sample_keywords: Example keywords from the topic
        max_chars: Max chunk size for long segments
        similarity_threshold: Minimum similarity to title+abstract for sentence inclusion
        top_k_sentences: Maximum sentences to keep per segment
        model_name: SentenceTransformer model for semantic filtering

    Returns:
        DataFrame with columns: ['segment', 'text', 'keyphrases', 'similarity_filtered']
    """
    # Extract title and abstract
    title = segments.get('title', '') or segments.get('Title', '')
    abstract = segments.get('abstract', '') or segments.get('Abstract', '')

    if not title and not abstract:
        print("⚠️  Warning: No title or abstract found for semantic filtering")
        # Fall back to original method
        return extract_llama_keyphrases_from_segments(
            api_url, segments, topic_name, topic_info, sample_keywords, max_chars
        )

    print(f"🔍 Applying semantic filtering with threshold: {similarity_threshold}")
    print(f"📄 Reference: {title[:50]}... | {abstract[:50]}...")

    # Step 1: Clean and filter sentences by semantic similarity to title + abstract
    # First clean all segments to handle broken sentences
    cleaned_segments = {}
    for k, v in segments.items():
        if k.lower() not in ['title', 'abstract']:
            cleaned_segments[k] = clean_and_form_sentences(v)

    print(f"🧹 Cleaned {len(cleaned_segments)} segments for semantic filtering")

    filtered_segments = filter_sentences_by_semantic_similarity(
        title=title,
        abstract=abstract,
        segments=cleaned_segments,
        model_name=model_name,
        similarity_threshold=similarity_threshold,
        top_k_per_segment=top_k_sentences
    )

    print(f"  Filtered segments: {filtered_segments}")

    # Step 2: Prepare segments for LLaMA processing
    # Always include title and abstract
    llama_segments = {}
    if title:
        llama_segments['title'] = title
    if abstract:
        llama_segments['abstract'] = abstract

    # Add filtered sentences from other segments
    for segment_name, filtered_sentences in filtered_segments.items():
        if filtered_sentences:
            # Combine filtered sentences back into text chunks
            combined_text = ' '.join(filtered_sentences)

            # Chunk if too long
            if len(combined_text) > max_chars:
                chunks = chunk_text(combined_text, max_chars)
                for i, chunk in enumerate(chunks):
                    llama_segments[f"{segment_name}_filtered_{i+1}"] = chunk
            else:
                llama_segments[f"{segment_name}_filtered"] = combined_text

    print(f"📊 Segments for LLaMA: {list(llama_segments.keys())}")

    # Step 3: Extract keyphrases using LLaMA
    results = []

    for segment_name, text_chunk in llama_segments.items():
        # Enhanced prompt with topic context
        prompt = build_llama_prompt(
            sentence=text_chunk,
            topic_name=topic_name,
            topic_info=topic_info,
            sample_keyphrases=sample_keywords
        )

        try:
            response = requests.post(api_url, json={"prompt": prompt})
            response.raise_for_status()
            raw_output = response.text.strip()
            phrases = clean_llama_response(raw_output, input_text=text_chunk)

            results.append({
                "segment": segment_name,
                "text": text_chunk,
                "keyphrases": phrases,
                "similarity_filtered": "filtered" in segment_name
            })

            print(f"✅ {segment_name}: {len(phrases)} keyphrases extracted")

        except Exception as e:
            print(f"❌ LLaMA error for segment: {segment_name} | {e}")
            results.append({
                "segment": segment_name,
                "text": text_chunk,
                "keyphrases": [],
                "similarity_filtered": "filtered" in segment_name
            })

    return pd.DataFrame(results)


def filter_sentences_by_semantic_similarity(
    title: str,
    abstract: str,
    segments: Dict[str, str],
    topic_name: Optional[str] = None,
    topic_info: Optional[str] = None,
    model_name: str = "all-MiniLM-L6-v2",
    similarity_threshold: float = 0.3,
    top_k_per_segment: int = 10
) -> Dict[str, List[str]]:
    """
    Filter sentences from segments based on semantic similarity to title + abstract + topic.
    This is a standalone version for testing the LLaMA module independently.
    """
    try:
        model = SentenceTransformer(model_name)
    except Exception as e:
        print(f"⚠️  Error loading model {model_name}: {e}")
        return {}

    # Create reference text from title, abstract, and topic information
    reference_parts = []
    if title:
        reference_parts.append(title.strip())
    if abstract:
        reference_parts.append(abstract.strip())
    if topic_name:
        reference_parts.append(topic_name.strip())
    if topic_info:
        reference_parts.append(topic_info.strip())

    reference_text = ' '.join(reference_parts)
    reference_embedding = model.encode([reference_text], normalize_embeddings=True)

    filtered_segments = {}

    for segment_name, segment_text in segments.items():
        if segment_name.lower().strip() in ['title', 'abstract']:
            continue

        cleaned_text = clean_and_form_sentences(segment_text)
        sentences = _split_into_sentences(cleaned_text)

        if not sentences:
            filtered_segments[segment_name] = []
            continue

        sentence_embeddings = model.encode(sentences, normalize_embeddings=True)
        similarities = cosine_similarity(sentence_embeddings, reference_embedding).flatten()

        sentence_scores = list(zip(sentences, similarities))
        relevant_sentences = [
            sentence for sentence, score in sentence_scores
            if score >= similarity_threshold
        ]

        relevant_sentences = sorted(
            relevant_sentences,
            key=lambda x: sentence_scores[sentences.index(x)][1],
            reverse=True
        )[:top_k_per_segment]

        filtered_segments[segment_name] = relevant_sentences

        print(f"📊 {segment_name}: {len(sentences)} → {len(relevant_sentences)} sentences (threshold: {similarity_threshold})")

    return filtered_segments


if __name__ == "__main__":
    api_url = "http://***********:5007/lamma_3_2_model"

    topic = "Electrochemical, Radiational, and Thermal Energy Technology"

    topic_information = """This topic covers thermal, chemical, biochemical, electrochemical, photochemical, and engineering aspects of energy handling, transport, storage, and conversion, with a focus on non-fossil fuel sources and associated technologies. It includes heat storage and transport systems, such as sensible, latent, and thermochemical storage; solar energy collection, absorption, and pond systems; and heat pump technologies for the utilization of low-grade thermal energy sources. Hydrogen storage, distribution, and transport—both physical and materials-based—are also addressed.  

    Energy sources in scope include solar energy, ocean thermal energy, geothermal energy, waste heat recovery, and fuels derived from fermentation, gasification, or pyrolysis of biomass and wastes. Studies involving hydrogen as a fuel, combustion of non-fossil fuels, and the integration of waste heat into usable energy streams are also covered.  

    Energy conversion devices and technologies covered by this topic include batteries, fuel cells, solar cells, photoelectrochemical cells, solar thermophotovoltaic devices, thermoelectric devices, thermionic energy converters, magnetohydrodynamic and electrohydrodynamic generators, thermomagnetic energy converters, and other emerging energy conversion systems, including their components and materials.  

    The topic further encompasses safety aspects of energy utilization, including thermal system safety, hydrogen safety, battery and electrical safety, and process hazard mitigation for non-fossil fuel systems. Cross-cutting considerations such as system integration, durability, control systems, and environmental impact assessments are also included."""

    sample_keyphrases = [
        "Fuel cell", "Lithium secondary batteries", "Battery cathode", "Fuel cells, polymer electrolyte",
        "Secondary battery", "Battery anode", "Fluoropolymer", "Fuel cell electrolyte", "Solid oxide fuel cell", "Fuel cell electrode", "Carbon black", "Battery electrolyte", "Fuel cell separator", "Solar cell", "Battery electrode", "Fuel cells, proton exchange membrane", "Ionomer", "Polyoxyalkylenes",
        "Electric current-potential relationship", "Fuel cell cathode", "Fuel cell anode", "Polymer electrolyte", "Primary battery", "Carbon fiber", "Lead-acid secondary battery"
    ]


    section_texts_dict = {
        "title": "Leveraging AI and geothermal cogeneration to boost energy efficiency in a multipurpose zero energy building",
        "abstract": """
            Recent studies have targeted the emission of less CO2 alongside saving more energy. To achieve part of this purpose, zero energy buildings (ZEBs) are introduced, replacing fossil fuels with renewable energies. This study focuses on a proposed cogeneration geothermal system, comprising two Rankine cycle units and an absorption chiller, designed to fulfill the clean energy requirements of a 5-story ZEB in Birmingham, England. Using BEopt software, the simulation, and optimization of the complex was performed. The net energy consumption of the building was calculated in one year. The energy assessment revealed the building’s electricity consumption at 2594.48 MWh, heating demand at 3157.63 MWh, and cooling requirement at 75.56 MWh annually. Using EES
            software, the complex’s energy supply system, was analyzed. Exergy efficiency (EE), and cost rate (CR), the outputs of EES, ought to be optimized via a combination of ANN and NSGA-II (Non-dominated Sorting Genetic Algorithm II). The optimization results showed that in the most optimal operating mode, an EE of 69.11 % and a CR of 23.1 $/h can be reached. Evaluating the cogeneration system’s performance in Birmingham demonstrated its capability to generate 6884.61 MWh of electricity, 27841.66 MWh of heating, and 2462.53 MWh of cooling per year using geothermal energy. Comparing the building’s energy consumption with the system’s production highlighted significant savings: 4290.28 MWh of electricity, 24682.71 MWh of heating, and 2385.01 MWh of cooling annually while meeting the complex’s energy needs yearly.
            """,
        "results": """Table 7 showcases the optimal materials and features selected for the
            designed complex through building optimization. Fig. 7 displays the hourly heating bar consumption for the commercial,
            cultural, and recreational complex over a year. The data indicates
            that in Birmingham, the heating demand for the complex
            surpasses the cooling bar requirement. The heating bar consumption for
            the building ranges from 0 to 3600 kW hours annually.
            Fig. 8 showcases the hourly cooling load consumption for the
            building over a year. The data reveals that in Birmingham, the hourly
            values of cooling demand for the complex are relatively low, ranging
            from 0 to 250 kW hours.
            Fig. 9 displays the hourly electricity consumption for the building
            over the span of a year. The electricity usage for the complex ranges
            from 0 to 800 kW hours annually.
            In Fig. 10, calculations are presented for the energy consumption
            cost, energy savings, carbon dioxide emissions, and the decrease in
            carbon dioxide emissions relative to the energy consumption of the
            building. Each data point represents a specific optimization state within
            the energy usage of the complex. The study establishes the initial state
            with the highest energy consumption cost as a reference point, where
            energy savings are considered zero. Subsequent data points reflect the
            energy savings achieved in comparison to this reference mode. Notably,
            the final data point represents the maximum energy savings attained in
            energy consumption optimization.
            Table 8 presents the optimal values derived from six Pareto diagrams.
            These optimal points aim to achieve three primary objectives,
            examining the economic parameters in the software outputs: maximizing
            energy saving in buildings, decreasing carbon dioxide emissions,
            and lowering building costs.
            2.3. Cogeneration system energy production 􀀀 supply complex building
            energy consumption
            After simulating the complex and extracting the results (including
            calculations for electricity, heating, and cooling), the considered system’s
            ability to supply energy to the complex and store any excess energy
            produced is evaluated. Thus, the performance of the geothermal
            system was evaluated through a multi-objective optimization analysis,
            exergy efficiency, and economic evaluation. The hourly energy production
            of the system was then calculated with the changing weather
            conditions in Birmingham. Subsequently, an environmental analysis was
            conducted, examining the hourly electricity generation and calculating
            the reduction in carbon dioxide emissions as well as the associated costs. Finally, the system’s ability to meet the energy consumption demands
            and the amount of stored energy were determined.
            The assumptions studied by the system include:
            • Stability of geothermal system conditions
            • Being an isentropic of the turbine
            • Pump isentropic
            • Condenser outlet fluid saturation
            • Saturation of the evaporator outlet liquid
            • Insignificance of kinetic energy changes
            • The insignificance of potential energy
            Geothermal system modeling was done using EES software. Then,
            1000 data were extracted from the EES code and optimized by determining
            8 decision variables (8 independent variables) and 2 objective
            functions (2 dependent variables) by combining artificial intelligence
            and genetic algorithm using Statistica software. 1000 random data
            extracted from EES software were optimized after validation and satisfied
            with the accuracy of the written code. 8 independent variables
            consisting of T1, P1, m1, TE, PE, PPeva, T4, and T6 represent the input
            temperature of the evaporator, the input pressure, of the evaporator, the
            input mass flow rate of the evaporator, turbine efficiency, pump efficiency,
            the pinch point of the evaporator, the input temperature of the
            turbine 1, and the output temperature of the heat exchanger, respectively.
            These variables contribute to two objective functions including
            EE and CR.
            Optimization by the artificial neural network method has an input
            layer, a hidden layer, and an output layer. The number of input layers is
            8 layers (8 decision variables) and the number of output layers is 2 layers
            (2 objective functions).
            2.3.1. Proposed new cogeneration system (optimum cogeneration system
            for supplying building energy)
            Fig. 11 depicts the schematic of the renewable energy system
            designed to meet the energy demands of a 5-story building. The proposed
            system, centered on geothermal energy, consists of several subsystems:
            a geothermal system, two Organic Rankine cycles (ORCs) units,
            and an absorption chiller. The system utilizes R113 and ammonia as
            organic fluids, aiming to produce clean power, a cooling bar, and a
            heating bar for the building.
            The methodology flowchart for modeling the system within a
            structured framework is depicted in Fig. 12. The key steps of this
            approach include:
            • Modeling the geothermal system using EES software
            • Employing artificial intelligence techniques for optimization with
            Statistica software
            • Establishing EE, and CR, to improve technical efficiency & lower
            system expenses
            • Conducting economic analysis to evaluate unit and equipment costs
            • Utilizing exergy analysis to assess exergy rates and destruction
            • Selection of Birmingham, England, for system performance
            evaluation
            • Obtaining weather data for Birmingham using Meteonorm software
            • Performing an environmental assessment of the system
            To conduct the thermodynamic aspect of the cogeneration system,
            the basic relations presented in Table 9 were utilized as the fundamental
            equations for the system analysis.
            2.3.2. Artificial neural networks (ANNs)
            ANNs represent a distinct subset of machine learning (ML) models
            which are specifically designed and optimized to perform a variety of
            tasks. These neural networks (NNs) emulate the human thought process,
            allowing artificial intelligence to analyze data effectively. This mechanism
            is closely linked to deep learning and features a relatively complex
            structure. Furthermore, Fig. 13 depicts the configuration of the artificial
            neural network used in the analysis. Indeed, the randomly mentioned
            1000 data sets produced by EES are fitted into accurate equations (EE
            and CR) through ANN. ANN is employed here to prevail suitable equations
            relating the independent variables as inputs to the objective
            functions as outputs for each data series of EES.
            2.3.3. Genetic algorithm (Multi objective genetic algorithm)
            *******. Genetic algorithm. A genetic algorithm is a metaheuristic
            approach that mimics the principles of natural selection to find optimal
            solutions for optimization and search problems. It employs crossover
            and selection mechanisms to iteratively improve a population of
            candidate solutions. Genetic algorithms have broad applications in various domains, such as computer science, operations research, and
            artificial intelligence.
            *******. Non-dominated Sorting genetic algorithm II (NSGA-II). NSGA-II,
            or Non-Dominated Sorting Genetic Algorithm II, is a widely used genetic
            algorithm for multi-objective optimization based on non-dominance. It
            is recognized for its efficiency but has faced criticism for its
            computational complexity, lack of elitism, and the requirement to select
            optimal parameter values. NSGA-II was designed as an enhanced version
            of NSGA to tackle these issues by incorporating elitism, removing the
            need for a sharing parameter, and employing a more efficient sorting
            algorithm. The following outlines the process of NSGA-II:
            The NSGA-II algorithm operates through the following steps:
            1. Population Initialization: The population is initialized based on the
            problem’s range and constraints.
            2. Non-Dominated Sort: The initialized population is sorted into
            different fronts based on non-dominance. Each front represents a
            level of non-dominance, with the first front containing completely
            non-dominant solutions.
            3. Rank Assignment: Individuals in each front are assigned rank
            values based on their level of non-dominance. The first front receives
            a rank of 1, the second front 2, and so on.
            4. Crowding Distance Calculation: A crowding distance is calculated
            for each individual to determine its proximity to neighboring solutions.
            Larger average crowding distances promote better diversity in
            the population.
            5. Parent Selection: Parents are chosen from the population using binary
            tournament selection based on rank and crowding distance. 6. Offspring Generation: Selected parents generate offspring through
            crossover and mutation operators.
            7. Survival Selection: The population, including offspring, is sorted
            again based on non-dominance. Only the best individuals are
            selected to form the next generation, ensuring diversity and highquality
            solutions.
            NSGA-II prioritizes elitism by retaining top solutions across generations
            and employs a diversity-preserving approach via crowding distance
            computation. It centers on non-dominated solutions to achieve a
            varied array of optimal solutions within a single simulation. The broad
            application of NSGA-II stems from its adeptness in effectively managing
            multi-objective optimization challenges by evolving a diverse and toptier
            set of solutions.
            In this syudy, 1000 equations achieved in ANN constitute the initial
            population needed for NSGA-II. In these equations, as mentioned before,
            dependent variable of EE and CR are defined as two objective functions
            for NSGA-II. Through the aforementioned operations the optimum of
            OFs alongside the optimal values of 8 independent variables can be
            accomplished. 

            Validation of cogeneration system. Since the system introduced
            is a newly developed one, validation of the research findings Table 10 presents a comparison between the current study and the
            research by Amiri Rad et al. [19]. The data comparison reveals that the
            results obtained demonstrate the model’s accuracy in the present
            research aligns well with those of the referenced study, indicating a
            satisfactory level of precision.
            2.3.4.2. Multi objective optimization using ANN & genetic algorithm. The
            optimization aims to improve system performance and reduce costs by
            combining neural network and genetic optimization algorithms. The
            process involves analyzing results and output data from the proposed
            system using an intelligent neural network, converting the analyzed data
            into a smart mathematical function, and then optimizing this function
            using a genetic multi-objective optimization tool. The optimization focuses
            on increasing EE and reducing CR. The purpose of this optimization
            is summarized in Fig. 14, providing a clear overview of the
            optimization goals and methods.
            Table 11 outlines the optimization decision variables (input parameters)
            and their ranges that are crucial for optimum proposed system
            process. These variables have been carefully selected, and their ranges
            have been defined to ensure the most effective optimization results.
            The optimal values of decision variables and objective functions are
            presented in Table 12, showcasing the outcomes of the multi-objective
            optimization process. This optimization strategy combines neural
            network and genetic algorithms to enhance system performance and
            reduce costs effectively.
            In multi-objective optimizations, the target is to optimize each
            objective function while considering the trade-offs between them. In the
            context of optimization, the optimal value refers to the best possible
            value that can be achieved for a given objective function. The optimal
            value is typically determined through an optimization process, such as
            the one described in Fig. 15, which involves identifying variations
            (ranges) in the objective functions (OFs) and selecting the most optimal
            value for each function.
            Fig. 16 showcases the Pareto chart depicting the two objective
            functions under evaluation. The Pareto chart, as depicted in Fig. 16, is a
            fundamental tool in multi-objective optimization that helps identify
            optimal solutions by considering conflicting objectives. In this context,
            the Pareto front plays a crucial role in determining the best trade-offs
            between competing objectives.
            Fig. 17 displays a histogram representing the two objective functions:
            EE and the CR, which are key outcomes for the training and
            computation processes. The horizontal axis represents the values of the
            objective functions, while the vertical axis illustrates the percentage
            frequency distribution.
            Fig. 18 illustrates the normality plot of the two OFs concerning EE
            and CR. This diagram displays the normal probability distribution of
            residuals derived from linear regression analysis, providing insights into
            the statistical properties and relationships between these objectives.
            Fig. 19 displays the predicted values of functions plotted against the
            remaining values of those functions. This type of visualization is
            commonly used in data analysis to assess the accuracy of predictions and
            understand how well the predicted values align with the actual
            remaining values. By examining this figure, analysts can evaluate the
            Fig. 15. Constrained variations of the objective functions.
            S. Mobayen et al. performance of their predictive models and identify any discrepancies or
            patterns that may exist between the predicted and actual values of the
            functions.
            Fig. 20 illustrates the simultaneous impact of changes in two input
            variables on the optimization desirability of the two objective functions:
            the cost rate and the exergy efficiency of the proposed system. This visual
            representation enables a comprehensive understanding of how
            adjustments in the two inputs influence the optimization desirability of
            both the CR and the EE. Analyzing this figure can provide valuable insights
            into the interplay between the inputs and the performance of the
            system in terms of these two critical objectives.
            In Fig. 21, the variations of decision variables are illustrated concerning
            the objective function of exergy efficiency. The geothermal
            source temperature is recognized as a pivotal factor influencing system
            performance. Within the range of 210 to 230 degrees Celsius, adjustments
            in the geothermal temperature lead to an enhancement in exergy efficiency from 60 % to 85 %. Similarly, alterations in the inlet temperature
            to the pump result in an efficiency increase from 55 % to 85 %.
            The parametric analysis diagrams showcase how design parameters
            impact exergy efficiency. These diagrams, presented as load distribution
            diagrams, depict the changes in the efficiency objective function across
            different ranges of decision variables, offering valuable insights into
            optimizing system performance based on these key parameters.
            In Fig. 22, the variations of decision variables are depicted concerning
            the cost rate objective function. Utilizing geothermal source
            temperatures within the range of 210 to 230 degrees Celsius and inlet
            temperatures to the pump ranging from 40 to 60 degrees Celsius results
            in an escalation of the system’s costs. This parametric analysis has been
            conducted in optimization mode, evaluating the combined impact of all
            inputs on the objective function. In addition to the parameter displayed
            on the horizontal axis of the graph, six other parameters have been
            considered in relation to exergy efficiency, highlighting a comprehensive
            assessment of factors influencing cost rates and system
            optimization. 2.3.4.3. Exergy destruction (ED). In Fig. 23, the total exergy destruction
            for the system and its components has been calculated. The total exergy
            destruction for the system is determined to be 816 kW hours. The system
            comprises three units: Rankine cycle 1, Rankine cycle 2, and a
            compression chiller. Specifically, the exergy destruction rates for each
            component are as follows: Rankine cycle 1 at 449 kWh, compression
            chiller at 194 kWh, and Rankine cycle 2 at 173 kWh. Among the components,
            the evaporator exhibits the highest exergy destruction rate at
            199 kWh, followed by turbine number 1 at 132 kWh. The heat exchanger
            follows with a rate of 109 kWh, then the condenser at 95 kWh, and
            turbine number 2 at 73 kWh. The pumps exhibit the lowest ED rate
            within the system as their primary function is fluid pumping. """,

        "figure_caption": """ """,
  
        "table_data": """""",

        "conclusion": """
            Environmental and energy concerns prompted us to tackle a chunk of
            these issues through introducing a 5-strory ZEB which is supplied by the
            geothermal potential of the region. This led to the optimization of both
            the building and the geothermal system. These optimization problems
            require weather conditions of the region. Due to the high potential of
            geothermal energy, the city of Birmingham, situated in England, was
            chosen to be studied.
            The optimization of the building aims to maximizing energy saving,
            and minimizing cost and CO2 emission. The BEopt optimization framework
            was utilized to model the building, determine its load requirements,
            and assess energy consumption. The energy supply system
            design for the building was analyzed by EES software. The proposed
            system comprised two Rankine cycle units and an absorption chiller. The
            optimization of the system is a multi-objective problem in which EE and
            CR. The ultimate goal is to implement a renewable geothermal system to
            generate multiple forms of energy to fulfill the building’s electricity,
            cooling, and heating requirements.
            The summary of the results is as follows:
            • In the design of the building, different sizes and materials have been
            selected for its construction, including doors, walls, ceilings, windows,
            and phase change materials (PCM) to ensure the best possible
            structure.
            • The analysis showed that reducing the height of the floor and
            foundation of the complex leads to a reduction in energy consumption.
            The optimum type of building foundation identified for Birmingham
            is Pier&Beam with a flat roof/deck.
            • The energy assessment of the complex revealed electricity consumption
            of 2594.48 MWh, heating consumption of 3157.63 MWh,
            and cooling consumption of 75.56 MWh.
            • The results showed that the annual electricity consumption of this
            complex is between 0 and 800 kW hours.
            • The data showed that in the city of Birmingham, due to the low air
            temperature, the heating demand for the complex is greater than the
            cooling demand.
            • Since the introduced system is a newly developed system, validation
            of organic Rankine cycle subsystem was performed and data comparison
            showed that the obtained results have good solution
            accuracy.
            • Optimization of the geothermal system indicated an EE of 69.11 %
            and a system CR of $23.1 per hour could be achieved.
            • The temperature of the geothermal source in the range of 210 to 230
            degrees Celsius and the inlet temperature to the pump from 40 to 60
            degrees Celsius are the most important parameters on the objective
            functions.
            • The highest exergy destruction rate among the equipment is related
            to the evaporator with 199 kW.
            • Pumps show the lowest rate of exergy destruction in the system
            because their main function is to pump fluid.
            • The highest unit cost rates are related to Organic Rankine cycle 1,
            Organic Rankine cycle 2 and absorption chiller, respectively.
            • Performance evaluations in Birmingham demonstrated that the system
            could generate 6884.61 megawatt hours of electricity, 27841.66
            megawatt hours of heating, and 2462.53 kW hours of cooling
            annually.
            • Comparing the building’s energy consumption with the system’s
            production, it was found that the system could save 4290.28 megawatt
            hours of electricity, 24682.71 megawatt hours of heating, and
            2385.01 megawatt hours of cooling over the year, while effectively
            meeting the complex’s energy demands throughout the year.
            5. Future research directions
            In this part, three important suggestions are given to the researchers
            to continue and complete the current research, which include:
            • To start renewable systems, the potential of the region must be
            checked first, because, in some places, the potential of using two or
            more renewable energies is available at the same time. For this
            reason, by combining renewable energies, it is possible to help the
            stability and increase the reliability of the system. For example, solar
            energy is the most potential and most available renewable energy in
            the world, which can help to increase electricity production by
            combining solar energy and the existing geothermal system.
            • By using freshwater production systems such as reverse osmosis and
            combining them with the proposed system, in addition to generating
            electricity, cooling, and heating buildings, the amount of freshwater
            produced by the system can also be provided.
            • By combining the fuel cell system, the required hydrogen fuel is
            supplied by the proton exchange membrane electrolyzer, or the
            compressed air energy storage system, it is possible to help the stability
            of the system and help supply energy during peak
            consumption."""
                
        }





    # raw_text = " ".join(section_texts_dict.values())

    df = extract_llama_keyphrases_from_segments( 
        api_url=api_url,
        segments = section_texts_dict,
        topic_name= topic,
        topic_info= topic_information,
        sample_keywords= sample_keyphrases,
        max_chars = 1000
    )
    print(df)
    df.to_excel("llama_output.xlsx", index=False)

    print("\n" + "="*80)
    print("ENHANCED LLAMA EXTRACTION WITH SEMANTIC FILTERING")
    print("="*80)

    # Demo the enhanced method with semantic filtering
    print("🚀 Running enhanced extraction with semantic filtering...")

    df_enhanced = extract_llama_keyphrases_with_semantic_filtering(
        api_url=api_url,
        segments=section_texts_dict,
        topic_name=topic,
        topic_info=topic_information,
        sample_keywords=sample_keyphrases,
        max_chars=1000,
        similarity_threshold=0.3,  # Adjust based on your needs
        top_k_sentences=8,         # Max sentences per segment
        model_name="all-MiniLM-L6-v2"
    )

    print(f"\n📊 ENHANCED RESULTS:")
    print(f"Segments processed: {len(df_enhanced)}")

    if not df_enhanced.empty:
        print(f"\n🎯 EXTRACTED KEYPHRASES BY SEGMENT:")
        for _, row in df_enhanced.iterrows():
            filtered_status = "✅ Filtered" if row['similarity_filtered'] else "📄 Original"
            print(f"\n{filtered_status} | {row['segment']}:")
            print(f"  Keyphrases: {row['keyphrases']}")
            print(f"  Text preview: {row['text'][:100]}...")

        # Compare results
        print(f"\n📈 COMPARISON:")
        original_keyphrases = []
        enhanced_keyphrases = []

        for _, row in df.iterrows():
            if isinstance(row['keyphrases'], list):
                original_keyphrases.extend(row['keyphrases'])

        for _, row in df_enhanced.iterrows():
            if isinstance(row['keyphrases'], list):
                enhanced_keyphrases.extend(row['keyphrases'])

        print(f"Original method: {len(original_keyphrases)} total keyphrases")
        print(f"Enhanced method: {len(enhanced_keyphrases)} total keyphrases")

        # Show unique keyphrases
        original_unique = set(original_keyphrases)
        enhanced_unique = set(enhanced_keyphrases)

        print(f"Original unique: {len(original_unique)}")
        print(f"Enhanced unique: {len(enhanced_unique)}")

        # Show overlap and differences
        overlap = original_unique & enhanced_unique
        enhanced_only = enhanced_unique - original_unique

        print(f"Overlap: {len(overlap)} keyphrases")
        if enhanced_only:
            print(f"Enhanced method found additionally: {list(enhanced_only)[:5]}...")

    #  enhanced results
    df_enhanced.to_excel("enhanced_output.xlsx", index=False)
    print(f"\nEnhanced results saved to: enhanced_output.xlsx")

