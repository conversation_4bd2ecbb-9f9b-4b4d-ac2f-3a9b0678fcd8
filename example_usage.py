#!/usr/bin/env python3
"""
Simple example showing how to use the enhanced unified keyphrase extractor
with deduplication and structured output.
"""

from unified_keyphrase_extractor import (
    extract_keywords_with_sources_unified,
    extract_keyphrases_structured_output
)

def main():
    # Sample scientific article data
    title = "Leveraging AI and geothermal cogeneration to boost energy efficiency in a multipurpose zero energy building"
    
    abstract = """
    Recent studies have targeted the emission of less CO2 alongside saving more energy. To achieve part of this purpose, zero energy buildings (ZEBs) are introduced, replacing fossil fuels with renewable energies. This study focuses on a proposed cogeneration geothermal system, comprising two Rankine cycle units and an absorption chiller, designed to fulfill the clean energy requirements of a 5-story ZEB in Birmingham, England. Using BEopt software, the simulation, and optimization of the complex was performed. The net energy consumption of the building was calculated in one year. The energy assessment revealed the building’s electricity consumption at 2594.48 MWh, heating demand at 3157.63 MWh, and cooling requirement at 75.56 MWh annually. Using EES
    software, the complex’s energy supply system, was analyzed. Exergy efficiency (EE), and cost rate (CR), the outputs of EES, ought to be optimized via a combination of ANN and NSGA-II (Non-dominated Sorting Genetic Algorithm II). The optimization results showed that in the most optimal operating mode, an EE of 69.11 % and a CR of 23.1 $/h can be reached. Evaluating the cogeneration system’s performance in Birmingham demonstrated its capability to generate 6884.61 MWh of electricity, 27841.66 MWh of heating, and 2462.53 MWh of cooling per year using geothermal energy. Comparing the building’s energy consumption with the system’s production highlighted significant savings: 4290.28 MWh of electricity, 24682.71 MWh of heating, and 2385.01 MWh of cooling annually while meeting the complex’s energy needs yearly.
    """
    
    methods = """• Design of commercial, cultural, and environmental complex (the
    building)
    • Cogeneration system Energy production 􀀀 supply complex building
    Energy consumption (the system)
    • Case study (Birmingham, England) analysis for the cogeneration
    system
    • The efficiency and effectiveness of the system in meeting the energy
    demands of the complex
    The first part brings about the optimization of a 5-story building
    using BEopt software. The inputs of the software are the building’s
    material quality and quantity plus meteorology information of the
    studied region. The selected region in this study is Birmingham which is
    one of the main industrial and successful commercial areas of England,
    for which it serves as an administrative, recreational, and cultural center.
    Since the city has a high potential for setting up geothermal systems,
    our attention was drawn. The outputs would be the optimized objective
    functions in three categories energy, economy, and environment.
    Moreover, the electricity, cooling, and heating demands of the building
    would be calculated.
    However, the second part approaches the optimization of the system
    based on geothermal energy using two Rankine cycles and an absorption
    chiller. This part triggers a multi-objective optimization problem in
    which maximizing exergy efficiency and minimizing cost rate are two
    objective functions. There are 8 independent variables contributing to
    EE and CR which are optimized as well. These variables could have
    different values in different ranges. Firstly, EES software is employed to
    model the system in different values of 8 independent variables. In other
    words, the system is modeled with random amounts of these variables to
    produce an initial population to be fed into ANN. ANN strives to fit an
    accurate equation to independent variables for each member of the
    population. Finally, NSGA-II is applied to discern the optimum values of
    the previous step.
    In the third part, the hourly amounts of energy and economic potential
    of the proposed system under weather conditions in Birmingham
    are drawn as a case study to be compared with the outputs of the first
    part. If the amount of energy produced by the system exceeds the demand
    of the building; it will be saved or sold to the power network. On
    the contrary, whenever the building lacks energy it can be bought from
    the power network. All of these parts are explained as follows. 2.1. Design of commercial, cultural, and entertainment complex
    In the design of commercial, cultural, and entertainment complex
    BEopt was employed which is a building energy optimization tool
    developed by the National Renewable Energy Laboratory (NREL) to
    identify cost-optimal energy efficiency strategies for new and existing
    homes. It works based on a sequential search technique to find
    minimum-cost building designs at different target energy-savings and
    environmental levels, evaluating discrete building options reflecting
    realistic construction materials and practices. BEopt can be used to
    analyze new construction and existing home retrofits, as well as singlefamily
    detached and multi-family buildings. It implements EnergyPlus,
    the Department of Energy’s flagship simulation engine, and provides
    detailed simulation-based analysis based on specific house characteristics,
    such as size, architecture, occupancy, vintage, location, and utility
    rates.
    The design of the complex is illustrated in Fig. 1. This complex spans
    5 floors, each serving a distinct purpose.
    • The first floor encompasses a public library covering 975 square
    meters, featuring two reading rooms of 323 and 420 square meters, two book storage halls of 80 square meters each, and a 25 square
    meter administrative office.
    • The second floor houses a cafe and restaurant with halls measuring
    425 and 200 square meters, a 150 square meter kitchen, an 84 square
    meter warehouse, and a 36 square meter management room.
    • The third floor accommodates a grocery store with halls of 425 and
    240 square meters, along with warehouses of 105 and 72 square
    meters, and a 42 square meter office for management.
    • The fourth floor is designated for administrative offices, comprising
    various units ranging from 20 to 56 square meters.
    • The fifth floor is dedicated to an amusement park featuring halls of
    341 and 400 square meters, a 130 square meter warehouse, and a 40
    square meter management room.The ultimate objective is to implement
    a renewable geothermal system to generate multiple forms of
    energy to fulfill the building’s electricity, cooling, and heating
    requirements.
    The analysis flow diagram for the commercial, cultural, and entertainment
    complex is presented in Fig. 2.
    In the design of the building, various sizes and materials have been
    chosen for its construction, including doors, walls, roofs, windows, and
    phase change materials (PCM), to ensure the best possible structure.
    These are conceived as inputs of the software. The optimization process
    aimed at maximizing energy saving and minimizing cost as well as
    minimizing CO2 emission has resulted in a system runtime of approximately
    16 h. It is essential to note that the complex is oriented to the
    north.
    The materials investigated in Fig. 3, and Tables 1 and 2 were
    analyzed with the target of optimum energy consumption of the building
    in order to achieve energy savings.
    Other information is also needed as the input of the software. Fig. 4
    depicts the seasonal temperature fluctuations in Birmingham city. The
    results indicate that the ambient temperature (Ta) in Birmingham city (located in England country) varies between 0 and 30 (0C), with the
    highest value of temperature recorded during the summer season.
    Moreover, the dew point (DP) temperature (as hourly changes) for the
    city of Birmingham city has been plotted, revealing a range of 􀀀 10 to 20
    degrees Celsius. The air contains moisture, which exists as water vapor
    in a highly heated condition. When (Ta) air temperature decreases, this
    water vapor (WV) transitions to a saturated state, known as the DP
    temperature of the air. These two parameters are crucial for optimizing
    building energy efficiency and are essential in the selection of software
    specifications.
    The hourly variations in wind speed (WS) for Birmingham over a
    year represent another significant parameter. The graph illustrates that
    wind speeds fluctuate between 0 and 16 m/s, indicating a considerable
    potential for wind energy in the city. Solar radiation changes in Birmingham,
    England, present another crucial factor, ranging from 0 to
    1000 W per square meter throughout the year. This radiation intensity
    can significantly impact the building’s energy consumption. Furthermore,
    the relative humidity levels in Birmingham fluctuate between
    0 and 100 % annually. An examination of snowfall patterns in Birmingham
    reveals minimal snowfall during winter months. Analyzing
    these diverse parameters aims to yield practical and realistic outcomes
    for optimizing buildings in the city. It should be mentioned that the
    meteorology information is extracted from Meteonorm software which
    generates accurate data in the typical years for any places on Earth.
    This study initially explores various scenarios to determine the
    optimal floor height, building foundation type, foundation height, and
    roof type, as outlined in Table 3.
    Fig. 5 depicts the temperature within the living spaces. The optimal
    temperature range for a house is between 21 ◦C and 25.5 ◦C. Maintaining
    this temperature range is crucial for ensuring the health and
    comfort of the occupants. Therefore, it is essential to utilize central
    heating and other temperature regulation methods. Additionally, the
    setpoint temperature for the cooling bar and heating bar of the building
    is shown in one year to achieve the desired temperature (T0) in the living
    spaces.
    In Fig. 6, the amount of relative humidity inside the building is
    calculated hourly. Ten 10 scenarios have been identified by selecting different design
    types for constructing the complex, with details of each scenario outlined
    in Table 4.
    The outcomes of the analysis (electricity, heating bar, and cooling
    bar of building) for the ten scenarios investigated are summarized in
    Table 5.
    The analysis revealed that reducing the floor and foundation heights
    of the complex leads to lower energy consumption. The optimal building
    foundation type identified for Birmingham is Pier&Beam with a Flat
    Roof/Deck. To optimize energy, Scenario 5 was selected for the
    building design. Subsequently, BEopt was employed to fine-tune the
    building based on local conditions, weather variations, and various
    building parameters. BEopt employs a sequential search optimization
    technique to design construction plans that minimize cost and environmental
    issues while maximizing energy savings. The optimization
    process took over 16 h as mentioned before, involving 86 iterations.
    Table 6 compares three optimal scenarios, evaluating energy saving,
    cost, and environmental issues as three major objective functions for the
    complex under different conditions. Scenario 1 in this table considers
    three categories of maximizing energy saving, minimizing the building
    cost, and minimizing CO2 emission while scenario 2 considers minimizing
    the building cost as a single objective optimization and scenario
    3 is to maximize energy saving as a single objective optimization. Scenario
    1 emerged as the preferred choice in this study. In the optimized
    state, electricity consumption, heating, and cooling are at ideal levels.
        """
    
    results = """
        Table 10 presents a comparison between the current study and the
    research by Amiri Rad et al. [19]. The data comparison reveals that the
    results obtained demonstrate the model’s accuracy in the present
    research aligns well with those of the referenced study, indicating a
    satisfactory level of precision.
    2.3.4.2. Multi objective optimization using ANN & genetic algorithm. The
    optimization aims to improve system performance and reduce costs by
    combining neural network and genetic optimization algorithms. The
    process involves analyzing results and output data from the proposed
    system using an intelligent neural network, converting the analyzed data
    into a smart mathematical function, and then optimizing this function
    using a genetic multi-objective optimization tool. The optimization focuses
    on increasing EE and reducing CR. The purpose of this optimization
    is summarized in Fig. 14, providing a clear overview of the
    optimization goals and methods.
    Table 11 outlines the optimization decision variables (input parameters)
    and their ranges that are crucial for optimum proposed system
    process. These variables have been carefully selected, and their ranges
    have been defined to ensure the most effective optimization results.
    The optimal values of decision variables and objective functions are
    presented in Table 12, showcasing the outcomes of the multi-objective
    optimization process. This optimization strategy combines neural
    network and genetic algorithms to enhance system performance and
    reduce costs effectively.
    In multi-objective optimizations, the target is to optimize each
    objective function while considering the trade-offs between them. In the
    context of optimization, the optimal value refers to the best possible
    value that can be achieved for a given objective function. The optimal
    value is typically determined through an optimization process, such as
    the one described in Fig. 15, which involves identifying variations
    (ranges) in the objective functions (OFs) and selecting the most optimal
    value for each function.
    Fig. 16 showcases the Pareto chart depicting the two objective
    functions under evaluation. The Pareto chart, as depicted in Fig. 16, is a
    fundamental tool in multi-objective optimization that helps identify
    optimal solutions by considering conflicting objectives. In this context,
    the Pareto front plays a crucial role in determining the best trade-offs
    between competing objectives.
    Fig. 17 displays a histogram representing the two objective functions:
    EE and the CR, which are key outcomes for the training and
    computation processes. The horizontal axis represents the values of the
    objective functions, while the vertical axis illustrates the percentage
    frequency distribution.
    Fig. 18 illustrates the normality plot of the two OFs concerning EE
    and CR. This diagram displays the normal probability distribution of
    residuals derived from linear regression analysis, providing insights into
    the statistical properties and relationships between these objectives.
    Fig. 19 displays the predicted values of functions plotted against the
    remaining values of those functions. This type of visualization is
    commonly used in data analysis to assess the accuracy of predictions and
    understand how well the predicted values align with the actual
    remaining values. By examining this figure, analysts can evaluate the
        """
        
    conclusion = """these issues through introducing a 5-strory ZEB which is supplied by the
    geothermal potential of the region. This led to the optimization of both
    the building and the geothermal system. These optimization problems
    require weather conditions of the region. Due to the high potential of
    geothermal energy, the city of Birmingham, situated in England, was
    chosen to be studied.
    The optimization of the building aims to maximizing energy saving,
    and minimizing cost and CO2 emission. The BEopt optimization framework
    was utilized to model the building, determine its load requirements,
    and assess energy consumption. The energy supply system
    design for the building was analyzed by EES software. The proposed
    system comprised two Rankine cycle units and an absorption chiller. The
    optimization of the system is a multi-objective problem in which EE and
    CR. The ultimate goal is to implement a renewable geothermal system to
    generate multiple forms of energy to fulfill the building’s electricity,
    cooling, and heating requirements.
    The summary of the results is as follows:
    • In the design of the building, different sizes and materials have been
    selected for its construction, including doors, walls, ceilings, windows,
    and phase change materials (PCM) to ensure the best possible
    structure.
    • The analysis showed that reducing the height of the floor and
    foundation of the complex leads to a reduction in energy consumption.
    The optimum type of building foundation identified for Birmingham
    is Pier&Beam with a flat roof/deck.
    • The energy assessment of the complex revealed electricity consumption
    of 2594.48 MWh, heating consumption of 3157.63 MWh,
    and cooling consumption of 75.56 MWh.
    • The results showed that the annual electricity consumption of this
    complex is between 0 and 800 kW hours.
    • The data showed that in the city of Birmingham, due to the low air
    temperature, the heating demand for the complex is greater than the
    cooling demand.
    • Since the introduced system is a newly developed system, validation
    of organic Rankine cycle subsystem was performed and data comparison
    showed that the obtained results have good solution
    accuracy.
    • Optimization of the geothermal system indicated an EE of 69.11 %
    and a system CR of $23.1 per hour could be achieved.
    • The temperature of the geothermal source in the range of 210 to 230
    degrees Celsius and the inlet temperature to the pump from 40 to 60
    degrees Celsius are the most important parameters on the objective
    functions.
    • The highest exergy destruction rate among the equipment is related
    to the evaporator with 199 kW.
    • Pumps show the lowest rate of exergy destruction in the system
    because their main function is to pump fluid.
    • The highest unit cost rates are related to Organic Rankine cycle 1,
    Organic Rankine cycle 2 and absorption chiller, respectively.
    • Performance evaluations in Birmingham demonstrated that the system
    could generate 6884.61 megawatt hours of electricity, 27841.66
    megawatt hours of heating, and 2462.53 kW hours of cooling
    annually.
    • Comparing the building’s energy consumption with the system’s
    production, it was found that the system could save 4290.28 megawatt
    hours of electricity, 24682.71 megawatt hours of heating, and
    2385.01 megawatt hours of cooling over the year, while effectively
    meeting the complex’s energy demands throughout the year.
    5. Future research directions
    In this part, three important suggestions are given to the researchers
    to continue and complete the current research, which include:
    • To start renewable systems, the potential of the region must be
    checked first, because, in some places, the potential of using two or
    more renewable energies is available at the same time. For this
    reason, by combining renewable energies, it is possible to help the
    stability and increase the reliability of the system. For example, solar
    energy is the most potential and most available renewable energy in
    the world, which can help to increase electricity production by
    combining solar energy and the existing geothermal system.
    • By using freshwater production systems such as reverse osmosis and
    combining them with the proposed system, in addition to generating
    electricity, cooling, and heating buildings, the amount of freshwater
    produced by the system can also be provided.
    • By combining the fuel cell system, the required hydrogen fuel is
    supplied by the proton exchange membrane electrolyzer, or the
    compressed air energy storage system, it is possible to help the stability
    of the system and help supply energy during peak
    consumption."""
        
    # Combine all text
    full_text = f"{title} {abstract} {methods} {results} {conclusion}"
    
    # Create sections dictionary
    sections = {
        "title": title,
        "abstract": abstract,
        "methods": methods,
        "results": results,
        "conclusion": conclusion
    }
    
    print("ENHANCED KEYPHRASE EXTRACTION EXAMPLE")
    print("=" * 60)
    print(f"Document: {title}")
    print(f"Sections: {list(sections.keys())}")
    
    # Method 1: Extract with deduplication (similar to main_source.py but enhanced)
    print("\nMETHOD 1: Enhanced extraction with deduplication")
    df_enhanced = extract_keywords_with_sources_unified(
        raw_text=full_text,
        section_texts=sections,
        filter_results=True,
        use_deduplication=True  # This applies merge_and_deduplicate_keywords()
    )
    
    print(f"Results: {len(df_enhanced)} keyword-sentence pairs")
    print(f"Unique keywords: {df_enhanced['keyword'].nunique()}")
    
    if not df_enhanced.empty:
        print(f"\nTop keywords (prioritizing multi-extractor consensus):")
        for i, (_, row) in enumerate(df_enhanced.head(10).iterrows(), 1):
            extractor_count = len(row['extractor'].split('|'))
            print(f"  {i:2d}. '{row['keyword']}' (extractors: {row['extractor']}, consensus: {extractor_count})")
    
    # Method 2: Structured output format
    print(f"\nMETHOD 2: Structured output (sentence-grouped)")
    df_structured = extract_keyphrases_structured_output(
        raw_text=full_text,
        section_texts=sections,
        filter_results=True,
        use_deduplication=True
    )
    
    print(f"Results: {len(df_structured)} sentences with keyphrases")
    
    if not df_structured.empty:
        print(f"\nStructured output format:")
        print(f"Columns: {list(df_structured.columns)}")
        
        print(f"\nTop sentences by keyphrase density:")
        for i, (_, row) in enumerate(df_structured.head(5).iterrows(), 1):
            print(f"\n{i}. SENTENCE ({row['input_segment']}): {row['sentence'][:80]}...")
            print(f"   KEYPHRASES ({row['keyphrase_count']}): {row['keyphrase_list']}")
            print(f"   EXTRACTORS: {row['extractor_list']}")
        
        print(f"\nSummary by document segment:")
        segment_summary = df_structured.groupby('input_segment').agg({
            'keyphrase_count': 'sum',
            'sentence': 'count'
        }).rename(columns={'sentence': 'sentence_count'})
        
        for segment, data in segment_summary.iterrows():
            print(f"  {segment}: {data['sentence_count']} sentences, {data['keyphrase_count']} keyphrases")
    
    # Method 3: Compare with and without deduplication
    print(f"\nMETHOD 3: Comparison - with vs without deduplication")
    
    # Without deduplication
    df_raw = extract_keywords_with_sources_unified(
        raw_text=full_text,
        section_texts=sections,
        filter_results=True,
        use_deduplication=False
    )
    
    print(f"Without deduplication: {len(df_raw)} keyword-sentence pairs")
    print(f"With deduplication: {len(df_enhanced)} keyword-sentence pairs")
    print(f"Noise removed: {len(df_raw) - len(df_enhanced)} pairs ({((len(df_raw) - len(df_enhanced)) / len(df_raw) * 100):.1f}%)")
    
    # Show quality improvement
    if not df_enhanced.empty:
        multi_extractor_keywords = df_enhanced[df_enhanced['extractor'].str.contains('|')]
        print(f"Keywords found by multiple extractors: {len(multi_extractor_keywords)} ({len(multi_extractor_keywords)/len(df_enhanced)*100:.1f}%)")
    
    print(f"\nBENEFITS OF THE ENHANCED APPROACH:")
    print("  • Removes duplicate and partial match keywords")
    print("  • Prioritizes keywords found by multiple extractors")
    print("  • Provides sentence-level context grouping")
    print("  • Identifies source document segments")
    print("  • Maintains compatibility with existing workflows")
    
    return df_enhanced, df_structured

if __name__ == "__main__":
    try:
        enhanced_df, structured_df = main()
        
        print(f"\nEXTRACTION COMPLETED SUCCESSFULLY!")
        print(f"Enhanced DataFrame shape: {enhanced_df.shape}")
        print(f"Structured DataFrame shape: {structured_df.shape}")
        
        # Save results for inspection
        if not enhanced_df.empty:
            enhanced_df.to_csv("enhanced_keyphrases.csv", index=False)
            print(f"Enhanced results saved to: enhanced_keyphrases.csv")
        
        if not structured_df.empty:
            structured_df.to_csv("structured_keyphrases.csv", index=False)
            print(f"Structured results saved to: structured_keyphrases.csv")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
