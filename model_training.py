import torch
import yaml
import yake
import spacy
from sentence_transformers import SentenceTransformer, util
from keybert import KeyBERT
from pathlib import Path
import os
import json
import pprint
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
from collections import Counter
from typing import List, Dict, Tuple, Optional

def debug(msg):
    print(f"[DEBUG] {msg}")

# ==========================
# Load config
# ==========================
def load_config(config_path):
    debug(f"Loading config from: {config_path}")
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    debug(f"Config keys loaded: {list(config.keys())}")
    return config

# ==========================
# Manual Keyphrase Dataset Loading
# ==========================
def load_manual_keyphrases(excel_path: str) -> Dict[str, List[str]]:
    """
    Load manually extracted keyphrases from Excel file.
    Returns a dictionary mapping document identifiers to lists of keyphrases.
    """
    try:
        df = pd.read_excel(excel_path)
        debug(f"Loaded Excel file with shape: {df.shape}")
        debug(f"Columns: {list(df.columns)}")

        # Find keyphrase-related columns
        keyphrase_cols = [col for col in df.columns if any(keyword in col.lower()
                         for keyword in ['keyword', 'keyphrase', 'key', 'phrase', 'kw'])]

        debug(f"Found keyphrase columns: {keyphrase_cols}")

        # Extract keyphrases per document
        manual_keyphrases = {}
        for idx, row in df.iterrows():
            doc_id = f"doc_{idx}"
            keyphrases = []

            for col in keyphrase_cols:
                if pd.notna(row[col]):
                    # Handle different formats: comma-separated, semicolon-separated, etc.
                    phrases = str(row[col]).split(',') if ',' in str(row[col]) else [str(row[col])]
                    keyphrases.extend([phrase.strip() for phrase in phrases if phrase.strip()])

            if keyphrases:
                manual_keyphrases[doc_id] = list(set(keyphrases))  # Remove duplicates

        debug(f"Loaded {len(manual_keyphrases)} documents with manual keyphrases")
        return manual_keyphrases

    except Exception as e:
        debug(f"Error loading manual keyphrases: {e}")
        return {}

# ==========================
# Sentence Splitting
# ==========================
def split_into_sentences(text, spacy_model="en_core_web_sm"):
    nlp = spacy.load(spacy_model)
    doc = nlp(text)
    return [sent.text.strip() for sent in doc.sents if sent.text.strip()]

# ==========================
# Embedding & Retrieval
# ==========================
def check_model_structure(model_path):
    modules_json_path = Path(model_path) / "modules.json"
    if not modules_json_path.exists():
        debug(f"⚠ modules.json not found in {model_path}")
        return False
    try:
        with open(modules_json_path, "r", encoding="utf-8") as f:
            modules_data = json.load(f)
        if not all("path" in m and "name" in m for m in modules_data):
            debug(f"⚠ modules.json missing 'path' or 'name' fields: {modules_data}")
            return False
    except Exception as e:
        debug(f"⚠ Error reading modules.json: {e}")
        return False
    return True

def get_embedding_model(model_path):
    debug(f"Loading embedding model from: {model_path}")
    if not check_model_structure(model_path):
        debug("⚠ Model folder is not in SentenceTransformers format. Model load may fail.")
    return SentenceTransformer(str(Path(model_path)))


def retrieve_relevant_sentences(
    title,
    abstract,
    section_texts,
    model,
    top_k=5,
    similarity_threshold=0.35,
    spacy_model="en_core_web_sm"
):
    """
    Returns: dict[str, list[str]]
        Mapping section_name -> list of top sentences (strings), sorted by score desc.
        Guaranteed at least 1 sentence per non-empty section.
    """
    query = f"{title} {abstract}".strip()
    # Use list to ensure shape (1, dim); normalize for cosine
    query_emb = model.encode([query], convert_to_tensor=True, normalize_embeddings=True)
    relevant_sentences_by_section = {}

    for section_name, text in section_texts.items():
        sentences = split_into_sentences(text, spacy_model=spacy_model)
        if not sentences:
            debug(f"Section '{section_name}': no sentences after splitting.")
            relevant_sentences_by_section[section_name] = []
            continue

        # Encode and normalize sentence embeddings
        sentence_embs = model.encode(sentences, convert_to_tensor=True, normalize_embeddings=True)

        # Cosine similarity with normalized vectors = dot product
        cos_scores = util.cos_sim(query_emb, sentence_embs)[0]  # shape: (num_sentences,)
        if cos_scores.numel() == 0:
            debug(f"Section '{section_name}': cosine scores empty.")
            relevant_sentences_by_section[section_name] = []
            continue

        # Debug stats
        max_score = float(torch.max(cos_scores).item())
        min_score = float(torch.min(cos_scores).item())
        avg_score = float(torch.mean(cos_scores).item())
        debug(f"Section '{section_name}': sim scores -> min={min_score:.3f}, avg={avg_score:.3f}, max={max_score:.3f}")

        # Take top-k indices
        k = min(max(1, top_k), len(sentences))
        top_results = torch.topk(cos_scores, k=k)
        top_pairs = [(float(score), sentences[int(idx)]) for score, idx in zip(top_results.values, top_results.indices)]

        # Filter by threshold but keep at least 1
        filtered_pairs = [(s, sent) for s, sent in top_pairs if s >= similarity_threshold]
        if not filtered_pairs:
            # keep the best one even if below threshold
            best_score, best_sent = max(top_pairs, key=lambda x: x[0])
            filtered_pairs = [(best_score, best_sent)]

        # Sort by score desc and return ONLY the sentences (strings)
        filtered_pairs.sort(key=lambda x: x[0], reverse=True)
        selected_sentences = [sent for _, sent in filtered_pairs]

        relevant_sentences_by_section[section_name] = selected_sentences
        debug(f"Section '{section_name}': kept {len(selected_sentences)} sentence(s) (threshold={similarity_threshold}).")

    return relevant_sentences_by_section

# ==========================
# Enhanced Keyphrase Extraction
# ==========================
def extract_keyphrases_yake(text, yake_params):
    """Extract keyphrases using YAKE algorithm."""
    kw_extractor = yake.KeywordExtractor(
        lan=yake_params.get("lan", "en"),
        n=yake_params.get("n", 2),
        dedupLim=yake_params.get("dedup_threshold", 0.9),
        top=yake_params.get("max_keywords", 30)
    )
    keywords = kw_extractor.extract_keywords(text)
    return [(kw, score) for kw, score in keywords]

def extract_keyphrases_keybert(text, model_path, top_n=10, ngram_range=(1, 3)):
    """Extract keyphrases using KeyBERT algorithm."""
    kw_model = KeyBERT(model=SentenceTransformer(str(Path(model_path))))
    keywords = kw_model.extract_keywords(
        text,
        keyphrase_ngram_range=ngram_range,
        stop_words='english',
        top_n=top_n
    )
    return [(kw, score) for kw, score in keywords]

def calculate_relevance_score(keyphrase: str, title: str, abstract: str,
                            embedding_model: SentenceTransformer) -> float:
    """
    Calculate relevance score of a keyphrase to the document's main topic.
    Uses semantic similarity between keyphrase and title+abstract.
    """
    query = f"{title} {abstract}".strip()

    # Encode keyphrase and query
    keyphrase_emb = embedding_model.encode([keyphrase], normalize_embeddings=True)
    query_emb = embedding_model.encode([query], normalize_embeddings=True)

    # Calculate cosine similarity
    similarity = util.cos_sim(keyphrase_emb, query_emb)[0][0].item()
    return similarity

def filter_keyphrases_by_relevance(keyphrases: List[Tuple[str, float]],
                                 title: str, abstract: str,
                                 embedding_model: SentenceTransformer,
                                 relevance_threshold: float = 0.3,
                                 max_keyphrases: int = 20) -> List[Tuple[str, float, float]]:
    """
    Filter and rank keyphrases by relevance to title+abstract.
    Returns list of (keyphrase, extraction_score, relevance_score) tuples.
    """
    scored_keyphrases = []

    for keyphrase, extraction_score in keyphrases:
        relevance_score = calculate_relevance_score(keyphrase, title, abstract, embedding_model)

        if relevance_score >= relevance_threshold:
            scored_keyphrases.append((keyphrase, extraction_score, relevance_score))

    # Sort by combined score (you can adjust the weighting)
    scored_keyphrases.sort(key=lambda x: x[2] * 0.7 + (1 - x[1]) * 0.3, reverse=True)

    return scored_keyphrases[:max_keyphrases]

# def extract_keyphrases_keybert(text, model_path, top_n=10, ngram_range=(1, 3)):
#     kw_model = KeyBERT(model=SentenceTransformer(str(Path(model_path))))
#     print(f"{kw_model =}")
#     keywords = kw_model.extract_keywords(
#         text,
#         keyphrase_ngram_range=ngram_range,
#         stop_words='english',
#         top_n=top_n
#     )
#     return [kw for kw, score in keywords]

# ==========================
# Enhanced Main Pipeline
# ==========================
def process_pdf_segments_enhanced(config, title, abstract, section_texts,
                                manual_keyphrases_path=None, top_k_sentences=20):
    """
    Enhanced keyphrase extraction pipeline with relevance filtering and manual validation.
    """
    embedding_model = get_embedding_model(config["model_paths"]["sentence_transformer"])

    # Step 1: Extract relevant sentences
    relevant_sentences_by_section = retrieve_relevant_sentences(
        title,
        abstract,
        section_texts,
        embedding_model,
        top_k=top_k_sentences,
        similarity_threshold=config.get("embedding", {}).get("similarity_threshold", 0.5),
        spacy_model=config.get("extractors", {}).get("positionrank", {}).get("spacy_model", "en_core_web_sm")
    )

    debug(f"Found relevant sentences in {len(relevant_sentences_by_section)} sections")

    # Step 2: Combine relevant text
    all_relevant_sentences = []
    for _, sents in relevant_sentences_by_section.items():
        all_relevant_sentences.extend(sents)

    combined_relevant_text = " ".join(all_relevant_sentences)
    combined_title_abstract = f"{title} {abstract}"

    # Step 3: Extract keyphrases using multiple methods
    # YAKE extraction
    yake_keyphrases_relevant = extract_keyphrases_yake(combined_relevant_text, config["extractors"]["yake"])
    yake_keyphrases_title_abs = extract_keyphrases_yake(combined_title_abstract, config["extractors"]["yake"])

    # KeyBERT extraction (if configured)
    keybert_keyphrases_relevant = []
    keybert_keyphrases_title_abs = []
    if "keybert" in config["extractors"]:
        keybert_keyphrases_relevant = extract_keyphrases_keybert(
            combined_relevant_text,
            config["extractors"]["keybert"]["model_path"],
            top_n=config["extractors"]["keybert"]["top_n"]
        )
        keybert_keyphrases_title_abs = extract_keyphrases_keybert(
            combined_title_abstract,
            config["extractors"]["keybert"]["model_path"],
            top_n=config["extractors"]["keybert"]["top_n"]
        )

    # Step 4: Filter by relevance
    relevance_threshold = config.get("relevance", {}).get("threshold", 0.3)
    max_keyphrases = config.get("relevance", {}).get("max_keyphrases", 20)

    filtered_yake_relevant = filter_keyphrases_by_relevance(
        yake_keyphrases_relevant, title, abstract, embedding_model,
        relevance_threshold, max_keyphrases
    )

    filtered_keybert_relevant = filter_keyphrases_by_relevance(
        keybert_keyphrases_relevant, title, abstract, embedding_model,
        relevance_threshold, max_keyphrases
    ) if keybert_keyphrases_relevant else []

    # Step 5: Load manual keyphrases for validation (if available)
    manual_keyphrases = {}
    if manual_keyphrases_path:
        manual_keyphrases = load_manual_keyphrases(manual_keyphrases_path)

    return {
        "relevant_sentences_by_section": relevant_sentences_by_section,
        "keyphrases_filtered": {
            "yake_relevant": filtered_yake_relevant,
            "keybert_relevant": filtered_keybert_relevant,
            "yake_title_abstract": yake_keyphrases_title_abs,
            "keybert_title_abstract": keybert_keyphrases_title_abs
        },
        "manual_keyphrases_available": len(manual_keyphrases) > 0,
        "extraction_stats": {
            "total_relevant_sentences": len(all_relevant_sentences),
            "yake_keyphrases_before_filter": len(yake_keyphrases_relevant),
            "yake_keyphrases_after_filter": len(filtered_yake_relevant),
            "keybert_keyphrases_before_filter": len(keybert_keyphrases_relevant),
            "keybert_keyphrases_after_filter": len(filtered_keybert_relevant)
        }
    }

# ==========================
# Keyphrase Evaluation and Validation
# ==========================
def evaluate_keyphrases_against_manual(extracted_keyphrases: List[str],
                                      manual_keyphrases: List[str],
                                      embedding_model: SentenceTransformer,
                                      similarity_threshold: float = 0.8) -> Dict:
    """
    Evaluate extracted keyphrases against manually annotated ones.
    Uses semantic similarity for fuzzy matching.
    """
    if not manual_keyphrases:
        return {"precision": 0, "recall": 0, "f1": 0, "matches": []}

    matches = []
    matched_manual = set()

    for ext_kp in extracted_keyphrases:
        best_match = None
        best_score = 0

        for man_kp in manual_keyphrases:
            if man_kp in matched_manual:
                continue

            # Calculate semantic similarity
            ext_emb = embedding_model.encode([ext_kp], normalize_embeddings=True)
            man_emb = embedding_model.encode([man_kp], normalize_embeddings=True)
            similarity = util.cos_sim(ext_emb, man_emb)[0][0].item()

            if similarity > best_score and similarity >= similarity_threshold:
                best_score = similarity
                best_match = man_kp

        if best_match:
            matches.append((ext_kp, best_match, best_score))
            matched_manual.add(best_match)

    precision = len(matches) / len(extracted_keyphrases) if extracted_keyphrases else 0
    recall = len(matches) / len(manual_keyphrases) if manual_keyphrases else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    return {
        "precision": precision,
        "recall": recall,
        "f1": f1,
        "matches": matches,
        "total_extracted": len(extracted_keyphrases),
        "total_manual": len(manual_keyphrases)
    }

def get_top_keyphrases(filtered_keyphrases: List[Tuple[str, float, float]],
                      top_n: int = 10) -> List[str]:
    """Extract top N keyphrases from filtered results."""
    return [kp[0] for kp in filtered_keyphrases[:top_n]]

# Legacy function for backward compatibility
def process_pdf_segments(config, title, abstract, section_texts, top_k_sentences=20):
    """Legacy function - use process_pdf_segments_enhanced for better results."""
    return process_pdf_segments_enhanced(config, title, abstract, section_texts,
                                       None, top_k_sentences)

# ==========================
#  MAIN block
# # ==========================
if __name__ == "__main__":
    CONFIG_PATH = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\config\config.yaml"
    config = load_config(CONFIG_PATH)

    title = "Sarcopenia in chronic kidney disease: prevalence by different definitions and relationship with adipositye"
    abstract = "This was a cross-sectional study with chronic kidney disease (CKD) patients under non-dialysis-dependent (NDD), hemodialysis (HD), and kidney transplant (KTx) treatment aimed to evaluate the prevalence of sarcopenia using the European Working Group on Sarcopenia in Older People (EWGSOP2) and the Foundation for the National Institutes of Health (FNIH) guidelines, and to analyze the relationship between sarcopenia and its components and body adiposity. Body composition was assessed by dual-energy X-ray absorptiometry and anthropometry. Bioelectrical impedance provided data on the phase angle and body water. The prevalence of sarcopenia in the total sample (n = 243; 53% men, 48 ± 10 years) was 7% according to the FNIH and 5% according to the EWGSOP2 criteria, and was low in each CKD group independently of the criteria applied (maximum 11% prevalence). Low muscle mass was present in 39% (FNIH) and 36% (EWGSOP2) and dynapenia in 10% of the patients. Patients who were sarcopenic according to the EWGSOP2 criteria presented low body adiposity. Conversely, patients who were sarcopenic according to the FNIH criteria presented high adiposity. This study suggests that in CKD (i) sarcopenia and low muscle mass prevalence varies according to the diagnostic criteria; (ii) sarcopenia and low muscle mass are common conditions; (iii) the association with body adiposity depends on the criteria used to define low muscle mass; and (iv) the FNIH criteria detected higher adiposity in individuals with sarcopenia."
    section_texts = {
        "results": "Out of 458 patients in NDD, 113 met the eligibility criteria and 83 agreed to participate; out of 304 patients in HD, 107 met the eligibility criteria and 80 agreed to participate; and out of 578 patients in KTx, 150 met the eligibility criteria and 81 agreed to participate. Subjects had a mean age of 48 ± 10 years and 53% were men. For those that met the eligibility criteria but did not agree to participate (HD, n = 27; NDD, n = 30; and KTx, n = 69), the mean age was 51 ± 9 yearsand 55% were men. Of the total sample, 29% had diabetes mellitus, 68% had systemic arterial hypertension, and 22% had dyslipidemia. The eGFRs of the NDD and KTx groups were 19 ± 9 and 70±18 mL/min/1.73m2, respectively. Transplant time had a median of 82 months, from 7 to 307, with an interquartile range of 79 months. For the HD group, the total weekly Kt/V had a mean of 2 ± 1 with a median time of HD of 64 months, from 4 to 373, with an interquartile range of 72 months. The prevalence of sarcopenia, low muscle mass, and dynapenia according to both guidelines is presented in Fig. 1 for the total, male, and female samples and in SupplementalFig. S1 for each CKD subgroup. The frequency of low muscle mass was higher for women (p = 0.02) and for HD (NDD vs. HD, p = 0.00) on applying the EWGSOP2 criteria. Dynapenia prevalence was similar for the two criteria, and higher for NDD and HD compared with KTx for the EWGSOP2 (NDD vs. KTx, p = 0.03; HD vs. KTx, p = 0.04) and the FNIH criteria (NDD vs. KTx, p = 0.03; HD vs. KTx, p = 0.04). There was no difference for sarcopenia prevalence between sexes and CKD subgroups for both criteria. The agreement, according to kappa values (Table 1), for sarcopenia, low muscle mass, and dynapenia between the EWGSOP2 and FNIH criteria was moderate, slight, and almost perfect, respectively. Only for the HD group was the agreement for sarcopenia substantial and better than in the other CKD subgroups. Also, the correlation between ASMI and ASM/BMI, in the total sample, is presented in Supplemental Fig. S2. The participants’ demographic and clinical characteristics according to the presence of sarcopenia are summarized in Table 2. Supplementary Tables S1, S2, and S3 present the same analysis stratified by CKD subgroups NDD, HD, and KTx, respectively. Considering the EWGSOP2 criteria, participants presenting with sarcopenia had lower body weight, BCM, body water parameters, and PhA, with significantly more patients presenting with low PhA compared with those without sarcopenia. According to the FNIH criteria, participants with sarcopenia had lower ICW and BCM. Body adiposity parameters evaluated by anthropometry (BMI, WC, and WHtR) were significantly lower in the sarcopenia group compared with the control group, according to the EWGSOP2 criteria (including both sexes in the analyses) (Table 3). Conversely, according to the FNIH criteria, the sarcopenia group compared with the control group (including both sexes in the analyses) presented significantly higher values for the WHtR and the total and trunk body fat as assessed by DXA (Table 3). The frequency of sarcopenia, lowmusclemass, and dynapenia according to body adiposity is shown in Table 4. Individuals presenting with excessive total and abdominal body adiposity based on anthropometric measures and BF% by DXA presented a lower frequency of low muscle mass when it was defined by the EWGSOP2 criteria and a higher frequency when it was defined by the FNIH criteria. The frequency of dynapenia was not associated with body adiposity. The association between sarcopenia, lowmusclemass, and dynapenia with body adiposity was evaluated by odds ratio analyses (Table 5). With regard to low muscle mass diagnosis, when applying the EWGSOP2 criteria the odds ratios were significantly lower in individuals presenting with excess adiposity, whereas when applying the FNIH criteria there was an inverse association with higher odds ratios. Additionally, the correlation analysis between HGS and ASMI was 0.61 (p ≤ 0.000) and between HGS and ASM/BMI was 0.76 (p ≤ 0.000)."
        
    }

    output = process_pdf_segments(config, title, abstract, section_texts, top_k_sentences=3)
    pprint.pprint(output)