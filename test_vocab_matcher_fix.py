#!/usr/bin/env python3
"""
Test script to verify the vocab_matcher fix in unified_keyphrase_extractor.py
"""

from unified_keyphrase_extractor import UnifiedKeyphraseExtractor
import os

def test_vocab_matcher_fix():
    """Test the fixed vocab_matcher integration."""
    
    print("🧪 TESTING VOCAB_MATCHER FIX")
    print("=" * 50)
    
    # Sample text for testing
    sample_text = """
    Renewable energy systems include solar panels, wind turbines, and geothermal systems.
    Energy storage technologies are crucial for grid integration and stability.
    Smart grid algorithms optimize energy efficiency and reduce waste.
    """
    
    print(f"📄 Sample text: {sample_text.strip()}")
    
    try:
        # Initialize the unified extractor
        print(f"\n🔧 Initializing unified extractor...")
        extractor = UnifiedKeyphraseExtractor()
        
        # Check vocab_matcher configuration
        vocab_config = extractor.config["extractors"]["vocab_matcher"]
        print(f"\n📊 VOCAB_MATCHER CONFIGURATION:")
        print(f"  Threshold: {vocab_config.get('threshold', 'Not set')}")
        print(f"  Embedding model: {vocab_config.get('embedding_model', 'Not set')}")
        print(f"  Embedding store: {vocab_config.get('embedding_store', 'Not set')}")
        
        # Check if files exist
        embedding_model_path = vocab_config.get('embedding_model', '')
        embedding_store_path = vocab_config.get('embedding_store', '')
        
        print(f"\n📁 FILE EXISTENCE CHECK:")
        if embedding_model_path:
            if os.path.exists(embedding_model_path):
                print(f"  ✅ Embedding model path exists: {embedding_model_path}")
            else:
                print(f"  ❌ Embedding model path not found: {embedding_model_path}")
        
        if embedding_store_path:
            if os.path.exists(embedding_store_path):
                print(f"  ✅ Embedding store exists: {embedding_store_path}")
            else:
                print(f"  ❌ Embedding store not found: {embedding_store_path}")
                print(f"  💡 You may need to create vocab_embeddings.pkl first")
        
        # Test vocab_matcher extraction directly
        print(f"\n🔍 TESTING VOCAB_MATCHER EXTRACTION:")
        vocab_keywords = extractor._extract_vocab_keywords(sample_text)
        
        print(f"\n📊 VOCAB_MATCHER RESULTS:")
        print(f"  Keywords found: {len(vocab_keywords)}")
        if vocab_keywords:
            print(f"  Keywords: {list(vocab_keywords)}")
        else:
            print(f"  No keywords found")
        
        # Test full extraction
        print(f"\n🚀 TESTING FULL EXTRACTION:")
        
        # Create minimal section texts
        section_texts = {
            "title": "Renewable Energy Systems",
            "abstract": sample_text
        }
        
        df = extractor.extract_from_text(
            text=sample_text,
            section_texts=section_texts,
            use_cleaned_text=False  # Disable semantic filtering for simpler test
        )
        
        if not df.empty:
            print(f"\n✅ EXTRACTION SUCCESSFUL!")
            
            # Check if vocab_matcher contributed
            vocab_results = df[df['extractor'] == 'vocab_matcher']
            if not vocab_results.empty:
                print(f"  ✅ Vocab_matcher contributed {len(vocab_results)} results")
                print(f"  Keywords: {list(vocab_results['keyword'].unique())}")
            else:
                print(f"  ⚠️  Vocab_matcher did not contribute any results")
            
            # Show all extractor results
            print(f"\n📊 ALL EXTRACTOR RESULTS:")
            extractor_counts = df['extractor'].value_counts()
            for extractor_name, count in extractor_counts.items():
                print(f"  {extractor_name}: {count} keywords")
        else:
            print(f"  ⚠️  No keywords extracted from any extractor")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vocab_matcher_standalone():
    """Test vocab_matcher module standalone for comparison."""
    print(f"\n🔧 TESTING VOCAB_MATCHER STANDALONE")
    print("=" * 40)
    
    try:
        from src.extractors.vocab_matcher import matched_vocab_terms, load_config
        
        # Load the original config
        config_path = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\machine_learning_tests2\config\config.yaml"
        
        if os.path.exists(config_path):
            print(f"✅ Config file found: {config_path}")
            config = load_config(config_path)
            
            sample_text = "renewable energy solar wind geothermal storage"
            
            print(f"📄 Testing with: {sample_text}")
            
            vocab_matches = matched_vocab_terms(sample_text, config, threshold=0.5)
            
            print(f"📊 Standalone results: {len(vocab_matches)} matches")
            if vocab_matches:
                print(f"  Matches: {vocab_matches}")
            else:
                print(f"  No matches found")
                
        else:
            print(f"❌ Config file not found: {config_path}")
            
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")

def provide_troubleshooting_tips():
    """Provide troubleshooting tips for vocab_matcher issues."""
    print(f"\n💡 TROUBLESHOOTING TIPS")
    print("=" * 30)
    
    print("""
    🔧 COMMON ISSUES AND SOLUTIONS:
    
    1. 📁 MISSING EMBEDDING STORE:
       • Create vocab_embeddings.pkl using the vocab_matcher module
       • Run the embedding creation script first
       • Check the file path in configuration
    
    2. 🤖 MODEL LOADING ISSUES:
       • The fix now falls back to 'paraphrase-MiniLM-L6-v2' if local model fails
       • Ensure internet connection for downloading standard models
       • Check if local model directory has config.json
    
    3. ⚙️ CONFIGURATION MISMATCH:
       • The unified extractor now properly formats config for vocab_matcher
       • Embedding store path is correctly mapped
       • Model path is validated before use
    
    4. 🔍 NO MATCHES FOUND:
       • Check if vocab_embeddings.pkl contains relevant terms
       • Adjust similarity threshold (try 0.7 instead of 0.9)
       • Verify input text contains terms similar to vocabulary
    
    📋 RECOMMENDED STEPS:
    1. Ensure vocab_embeddings.pkl exists in the correct location
    2. Test with a lower similarity threshold (0.7-0.8)
    3. Use standard SentenceTransformer model names if local models fail
    4. Check that vocabulary terms are relevant to your input text
    """)

if __name__ == "__main__":
    print("🚀 VOCAB_MATCHER FIX VERIFICATION")
    print("=" * 50)
    
    # Test the fix
    success = test_vocab_matcher_fix()
    
    # Test standalone for comparison
    test_vocab_matcher_standalone()
    
    # Provide troubleshooting tips
    provide_troubleshooting_tips()
    
    if success:
        print(f"\n🎉 VOCAB_MATCHER FIX VERIFICATION COMPLETED!")
        print(f"✅ The configuration and error handling improvements should resolve the issues")
    else:
        print(f"\n❌ VERIFICATION FAILED")
        print(f"Please check the error messages and follow the troubleshooting tips above")
    
    print(f"\n📋 SUMMARY OF FIXES:")
    print(f"• ✅ Added proper file existence checks")
    print(f"• ✅ Improved model path validation")
    print(f"• ✅ Fallback to standard SentenceTransformer models")
    print(f"• ✅ Better error messages and debugging info")
    print(f"• ✅ Correct configuration formatting for vocab_matcher function")
