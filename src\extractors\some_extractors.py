from summa import keywords as summa_keywords
from rake_nltk import Rake
import yake
import pke
from nltk.corpus import stopwords
import string
from sklearn.feature_extraction.text import TfidfVectorizer
import pandas as pd
import re
import spacy

class YAKEExtractor:
    def __init__(self, top_n=20):
        self.kw_extractor = yake.KeywordExtractor(lan="en", n=2, dedupLim=0.9, top=top_n)

    def extract(self, text):
        return [kw for kw, score in self.kw_extractor.extract_keywords(text)]



import re
from rake_nltk import Rake

class RAKEExtractor:
    def __init__(self, top_n=10):
        self.rake = Rake(max_length=4)
        self.top_n = top_n

    def extract(self, text):
        self.rake.extract_keywords_from_text(text)
        phrases_with_scores = self.rake.get_ranked_phrases_with_scores()

        filtered_phrases = []
        for score, phrase in phrases_with_scores:  # correct order: (score, phrase)
            phrase_clean = phrase.strip()
            # Skip if pure number or number-like
            if re.fullmatch(r'\d+(\.\d+)?', phrase_clean):
                continue
            # Skip if just punctuation or empty
            if not re.search(r'[A-Za-z]', phrase_clean):
                continue
            filtered_phrases.append(phrase_clean)

        return filtered_phrases[:self.top_n]



class SummaTextRankExtractor:
    def __init__(self, top_n=10):
        self.top_n = top_n

    def extract(self, text):
        try:
            kw = summa_keywords.keywords(text, words=self.top_n, split=True)
            return [k.strip() for k in kw if k.strip()]
        except Exception as e:
            print(f"Summa error: {e}")
            return []




class TFIDFExtractor:
    def __init__(self, top_n=10, spacy_model="en_core_web_sm"):
        self.top_n = top_n
        self.nlp = spacy.load(spacy_model)
        self.stop_articles = {"a", "an", "the"}

    def _clean_phrase(self, phrase):
        # Remove newlines, multiple spaces, lowercase
        phrase = phrase.replace("\n", " ").strip().lower()

        # Remove starting/ending articles
        words = phrase.split()
        if words and words[0] in self.stop_articles:
            words = words[1:]
        if words and words[-1] in self.stop_articles:
            words = words[:-1]

        return " ".join(words).strip()

    def extract(self, text):
        try:
            # Extract noun phrases with cleaning
            doc = self.nlp(text)
            noun_chunks = list(
                set(self._clean_phrase(chunk.text) for chunk in doc.noun_chunks if len(chunk.text.split()) <= 3)
            )

            # Remove empty phrases after cleaning
            noun_chunks = [p for p in noun_chunks if p]

            if not noun_chunks:
                return []

            # Score noun phrases with TF-IDF
            vectorizer = TfidfVectorizer(vocabulary=noun_chunks, stop_words="english")
            X = vectorizer.fit_transform([text])
            scores = X.toarray()[0]

            # Sort by score and return top phrases
            scored_phrases = sorted(
                ((phrase, score) for phrase, score in zip(vectorizer.get_feature_names_out(), scores)),
                key=lambda x: x[1],
                reverse=True
            )

            return [phrase for phrase, score in scored_phrases[:self.top_n]]

        except Exception as e:
            print(f"TF-IDF error: {e}")
            return []


class MultipartiteRankExtractor:
    def __init__(self, top_n=10):
        self.top_n = top_n

    def extract(self, text):
        try:
            extractor = pke.unsupervised.MultipartiteRank()
            extractor.load_document(input=text, language="en")
            pos = {'NOUN', 'PROPN', 'ADJ'}
            extractor.candidate_selection(pos=pos)
            extractor.candidate_weighting(alpha=1.1, threshold=0.85, method='average')
            return [kw for kw, score in extractor.get_n_best(n=self.top_n)]
        except Exception as e:
            print(f"MultipartiteRank error: {e}")
            return []



def run_all_extractors(text, top_n=10):
    """Run all keyword extractors and return a dictionary."""
    extractors = {
        "YAKE": YAKEExtractor(top_n=top_n),
        "RAKE": RAKEExtractor(top_n=top_n),
        "SummaTextRank": SummaTextRankExtractor(top_n=top_n),
        "TFIDF": TFIDFExtractor(top_n=top_n),
        "MultipartiteRank": MultipartiteRankExtractor(top_n=top_n)
    }
    
    results = {}
    for name, extractor in extractors.items():
        print(f"Running {name} extractor...")
        results[name] = extractor.extract(text)
    return results

def save_keywords_to_excel(results_dict, output_path):
    """Save all keywords with extractor name to an Excel file."""
    rows = []
    for extractor_name, keywords_list in results_dict.items():
        for kw in keywords_list:
            rows.append({"Extractor": extractor_name, "Keyword": kw})
    df = pd.DataFrame(rows)
    df.to_excel(output_path, index=False)
    print(f"✅ Keywords saved to {output_path}")

if __name__ == "__main__":
    sample_text = """
    Solar energy storage and conversion is a growing field with applications in renewable energy systems. 
    Advances in hydrogen fuel storage, thermoelectric devices, and biomass gasification have increased efficiency. 
    Waste heat recovery systems and geothermal energy harvesting are critical for sustainable power generation.
    """

    # Run all extractors
    results = run_all_extractors(sample_text, top_n=10)

    # Save to Excel
    save_keywords_to_excel(results, "extracted_keywords.xlsx")

    # Print results
    for extractor, kws in results.items():
        print(f"\n{extractor} keywords:")
        print(kws)
