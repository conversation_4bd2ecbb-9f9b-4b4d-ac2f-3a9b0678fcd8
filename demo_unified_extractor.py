#!/usr/bin/env python3
"""
Demo script for the Unified Keyphrase Extractor
Shows how to use all extractors together and get comprehensive results.
"""

import pandas as pd
import time
from unified_keyphrase_extractor import (
    UnifiedKeyphraseExtractor, 
    extract_unified_keyphrases,
    extract_keywords_with_sources_unified
)

def demo_basic_usage():
    """Demonstrate basic usage of the unified extractor."""
    print("🔬 UNIFIED KEYPHRASE EXTRACTOR - BASIC DEMO")
    print("=" * 60)
    
    # Sample scientific text
    sample_text = """
    Sarcopenia is a syndrome characterized by progressive and generalized loss of skeletal muscle mass and strength. 
    The prevalence of sarcopenia in chronic kidney disease (CKD) patients varies widely depending on the definition used. 
    This study aimed to determine the prevalence of sarcopenia in CKD patients using different definitions and to examine 
    its relationship with adiposity measures. Body composition was assessed using dual-energy X-ray absorptiometry (DXA). 
    The results showed significant differences in prevalence rates between different diagnostic criteria.
    """
    
    sample_sections = {
        "title": "Sarcopenia in chronic kidney disease: prevalence and relationship with adiposity",
        "abstract": sample_text,
        "methods": "Cross-sectional study of CKD patients stages 3-5. Sarcopenia diagnosed using EWGSOP2, AWGS, and FNIH criteria.",
        "results": "Prevalence varied from 15.3% (EWGSOP2) to 28.0% (FNIH). Sarcopenic patients had lower BMI and higher visceral fat.",
        "conclusion": "Sarcopenia prevalence varies by diagnostic criteria. Associated with altered body composition in CKD patients."
    }
    
    print(f"📄 Sample Text: {sample_text[:100]}...")
    print(f"📊 Sections: {list(sample_sections.keys())}")
    
    # Extract using convenience function
    print("\n🚀 Extracting keyphrases...")
    start_time = time.time()
    
    df = extract_keywords_with_sources_unified(
        raw_text=sample_text,
        section_texts=sample_sections,
        filter_results=True
    )
    
    extraction_time = time.time() - start_time
    print(f"⏱️  Extraction completed in {extraction_time:.2f} seconds")
    
    # Display results
    if not df.empty:
        print(f"\n📊 EXTRACTION RESULTS:")
        print(f"Total keyword-sentence pairs: {len(df)}")
        print(f"Unique keywords: {df['keyword'].nunique()}")
        print(f"Extractors used: {', '.join(df['extractor'].unique())}")
        
        print(f"\n📈 KEYWORDS PER EXTRACTOR:")
        extractor_counts = df['extractor'].value_counts()
        for extractor, count in extractor_counts.items():
            print(f"  {extractor}: {count} keywords")
        
        print(f"\n🎯 TOP 10 KEYWORDS BY FREQUENCY:")
        top_keywords = df['keyword'].value_counts().head(10)
        for i, (keyword, count) in enumerate(top_keywords.items(), 1):
            extractors = df[df['keyword'] == keyword]['extractor'].unique()
            print(f"  {i:2d}. {keyword} (freq: {count}, extractors: {', '.join(extractors)})")
        
        print(f"\n📝 SAMPLE KEYWORD-SENTENCE PAIRS:")
        sample_df = df.head(5)
        for _, row in sample_df.iterrows():
            print(f"  🔹 {row['extractor']}: '{row['keyword']}' in '{row['source_sentence'][:80]}...'")
    else:
        print("❌ No keywords extracted")

def demo_advanced_usage():
    """Demonstrate advanced usage with custom configuration."""
    print("\n" + "=" * 60)
    print("🔬 UNIFIED KEYPHRASE EXTRACTOR - ADVANCED DEMO")
    print("=" * 60)
    
    # Custom configuration
    custom_config = {
        "extractors": {
            "yake": {
                "lan": "en",
                "max_keywords": 25,
                "dedup_threshold": 0.8,
                "n": 3  # Extract trigrams
            },
            "keyphrase": {
                "model_path": "models/keyphrase_model", 
                "top_n": 25,
                "score_threshold": 0.4
            },
            "rakun": {
                "top_n": 15,
                "merge_threshold": 1.0,
                "alpha": 0.4,
                "token_prune_len": 2
            },
            "llama": {
                "api_url": "http://172.27.7.85:5007/lamma_3_2_model",
                "topic_name": "Medical Research",
                "topic_info": "Clinical studies on muscle disorders and kidney disease",
                "sample_keywords": ["sarcopenia", "chronic kidney disease", "muscle mass", "body composition"],
                "max_chars": 800
            },
            "vocab_matcher": {
                "threshold": 0.75
            }
        }
    }
    
    # Initialize extractor with custom config
    extractor = UnifiedKeyphraseExtractor(custom_config)
    
    # Sample medical research text
    medical_text = """
    Chronic kidney disease (CKD) is associated with various complications including muscle wasting and sarcopenia. 
    Sarcopenia, defined as the loss of skeletal muscle mass and function, affects quality of life and mortality in CKD patients. 
    Different diagnostic criteria including EWGSOP2, AWGS, and FNIH have been proposed for sarcopenia diagnosis. 
    Body composition analysis using dual-energy X-ray absorptiometry (DXA) provides accurate measurements of muscle mass. 
    Bioelectrical impedance analysis (BIA) offers an alternative method for body composition assessment. 
    The relationship between sarcopenia and adiposity in CKD patients remains complex and requires further investigation.
    """
    
    medical_sections = {
        "title": "Sarcopenia and body composition in chronic kidney disease",
        "abstract": medical_text,
        "methods": "Observational study using DXA and BIA for body composition analysis",
        "results": "Sarcopenia prevalence varied by criteria. Strong correlation between muscle mass and kidney function.",
        "discussion": "Findings suggest need for standardized sarcopenia criteria in CKD populations"
    }
    
    print("🚀 Extracting with custom configuration...")
    df = extractor.extract_from_text(medical_text, medical_sections)
    
    if not df.empty:
        # Get summary statistics
        stats = extractor.get_summary_stats(df)
        print(f"\n📊 SUMMARY STATISTICS:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Get top keywords by frequency
        top_keywords_df = extractor.get_top_keywords_by_frequency(df, top_n=15)
        print(f"\n🏆 TOP KEYWORDS BY FREQUENCY:")
        for _, row in top_keywords_df.iterrows():
            print(f"  🔹 {row['keyword']} (freq: {row['frequency']}, extractors: {row['extractors']})")
        
        # Apply relevance filtering
        filtered_df = extractor.filter_by_relevance(df, min_frequency=2, min_length=4)
        print(f"\n🎯 AFTER RELEVANCE FILTERING:")
        print(f"  Original keywords: {len(df)}")
        print(f"  Filtered keywords: {len(filtered_df)}")
        
        if not filtered_df.empty:
            print(f"  Top filtered keywords:")
            top_filtered = filtered_df['keyword'].value_counts().head(10)
            for keyword, count in top_filtered.items():
                print(f"    • {keyword} (freq: {count})")
    else:
        print("❌ No keywords extracted with custom configuration")

def demo_comparison_with_main_source():
    """Compare output format with main_source.py."""
    print("\n" + "=" * 60)
    print("🔬 COMPARISON WITH MAIN_SOURCE.PY FORMAT")
    print("=" * 60)
    
    sample_text = "Sarcopenia affects muscle mass and strength in chronic kidney disease patients."
    
    # Extract using unified method
    df_unified = extract_keywords_with_sources_unified(sample_text, filter_results=False)
    
    print("📊 UNIFIED EXTRACTOR OUTPUT (similar to main_source.py):")
    print(f"Columns: {list(df_unified.columns)}")
    print(f"Shape: {df_unified.shape}")
    
    if not df_unified.empty:
        print("\nSample rows:")
        print(df_unified.head().to_string(index=False))
        
        print(f"\n📈 EXTRACTOR BREAKDOWN:")
        for extractor in df_unified['extractor'].unique():
            count = len(df_unified[df_unified['extractor'] == extractor])
            print(f"  {extractor}: {count} keyword-sentence pairs")
    
    print("\n✅ Output format matches main_source.py structure!")
    print("   - 'extractor': Source of the keyword")
    print("   - 'keyword': Extracted keyphrase")
    print("   - 'source_sentence': Sentence containing the keyword")
    print("   - 'confidence': Confidence score (can be enhanced)")

if __name__ == "__main__":
    try:
        # Run all demos
        demo_basic_usage()
        demo_advanced_usage()
        demo_comparison_with_main_source()
        
        print("\n" + "=" * 60)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("""
        📋 NEXT STEPS:
        1. Integrate unified_keyphrase_extractor.py into your workflow
        2. Replace main_source.py calls with extract_keywords_with_sources_unified()
        3. Customize configuration for your specific domain
        4. Add relevance filtering based on title/abstract similarity
        5. Evaluate results against your manual keyphrase dataset
        """)
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("This might be due to missing dependencies or model files.")
        print("Please ensure all required packages and models are available.")
