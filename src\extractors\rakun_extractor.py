from rakun2 import RakunKeyphraseDetector
import yaml
# config = load_config("config/config.yaml")


def load_config(config_path):
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


class RakunExtractor:
    def __init__(self, top_n=20, merge_threshold=1.1, alpha=0.3, token_prune_len=3):
        """
        Initializes the Rakun keyphrase extractor with configurable hyperparameters.
        """
        self.top_n = top_n
        self.hyperparameters = {
            "num_keywords": self.top_n,
            "merge_threshold": merge_threshold,
            "alpha": alpha,
            "token_prune_len": token_prune_len
        }
        self.model = RakunKeyphraseDetector(self.hyperparameters)

    def extract(self, text):
        """
        Extracts keywords from input text.

        Returns:
            List[str]: Extracted keywords
        """
        keywords = self.model.find_keywords(text, input_type="string")
        return [kw[0] for kw in keywords if kw and kw[0]]



if __name__ == "__main__":

    doc ={
        "title": "Investigation of Electronic and Topological Properties of Magnesium-coated Boron Fullerenes and Their Interaction with Hydrogen Molecule",
        "abstract": "Various nanostructures have been widely investigated as alternative materials for hydrogen storage using experimental and computational techniques. In this research, adsorption, electronic, topological properties, and some molecular descriptors of magnesium-doped boron fullerenes and their interaction with H2 for hydrogen storage are investigated using density functional theory at B3LYP/6-31G//M062X/6-31G** theoretical level. Structures of B80, Mg12B80, Mg20B80 and Mg30B80 were optimized and their interaction with hydrogen molecule were analyzed. Results shows that charge transfer from Mg to B atoms is responsible for positive charge of Mg atoms. When hydrogen molecule approach to the system, it gets polarized and adsorbed to these boron fullerenes doped with Mg atoms. Also, it was found that Mg12B80 possesses the lowest energy gap (ΔEH-L), lowest hardness (η), and the highest adsorption energy, which indicates the reactivity and the hydrogen storage capability of this structure to adsorb hydrogen.",
        "results": "Fig. 1 shows optimized structures in which Mg atoms bind to the boron atoms in boron fullerene (B80). According to the structure presented by Szwacki et al. [31], when Mg atoms bind to the boron fullerene, charge transfer takes place from Mg atoms to B atoms. Tables 1 to 3 include amount of charge transfers for Mg12B80, Mg20B80 and Mg30B80 when Mg atoms bind to boron pentagonal and boron hexagonal rings, calculated in the context of Mulliken population analysis. This charge transfers make Mg atoms to become positively-charged, that leads to interaction with boron fullerene with negative charge. The binding energies for boron fullerene doped with Mg is calculated b",
        "figure_caption": "Fig. 1. Optimized configurations of side view of a) B80, c) Mg12B80, e) Mg20B80 and g) Mg30B80. Optimized configurations of top view of b) B80, d) Mg12B80, f) Mg20B80 and h) Mg30B80. The blue ball for B, the green ball for Mg.",
        "table_data": "",
        "table_caption": "Table 1. Mulliken charge distribution in the Mg and B atoms of the Mg12B80H2 and average charge transfer (Δq) from Mg to B atoms in pentagonal faces of B80 cage. Table 2. Mulliken charge distribution in the Mg and B atoms of the Mg20B80H2 and average charge transfer(Δq) from Mg to B atoms in hexagonal faces of the B80 cage. Table 3. Mulliken charge distribution in the Mg and B atoms of the Mg30B80H2 and average charge transfer (Δq)from Mg to B atoms in hexagonal faces of the B80 cage. Table 5. Hydrogen absorption energy (Ea) on Metal-coated Boron nanostructures calculated in previous papers and this research."
    }

    raw_text = " ".join(doc.values())
    config = load_config(r"C:\Users\<USER>\Desktop\chemindexing_2025\config\config.yaml")

    rakun_extractor = RakunExtractor(**config['extractors']['rakun'])
    keywords = rakun_extractor.extract(raw_text)
    print(keywords)
